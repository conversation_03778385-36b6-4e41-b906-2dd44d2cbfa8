import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '项目管理',
    },
    name: 'ProjectManagement',
    path: '/project-management',
    children: [
      {
        name: 'initiation',
        path: 'initiation',
        component: () => import('#/views/project-management/initiation/index.vue'),
        meta: {
          // title: $t('page.projectManagement.initiation'),
          title: '项目立项管理',
        },
      },
      {
        name: 'review',
        path: 'review',
        component: () => import('#/views/project-management/review/index.vue'),
        meta: {
          title: '项目评审管理',
        },
      },
      {
        name: 'branch-committee',
        path: 'branch-committee',
        component: () => import('#/views/project-management/branch-committee/index.vue'),
        meta: {
          title: '支委会',
        },
      },
      {
        name: 'general-manager',
        path: 'general-manager',
        component: () => import('#/views/project-management/general-manager/index.vue'),
        meta: {
          title: '总经办',
        },
      },
    ],
  },
];

export default routes;
