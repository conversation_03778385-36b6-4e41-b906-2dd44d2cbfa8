<script setup lang="ts">
import { computed, ref, reactive } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import {type AddWarehouse, addWarehouseApi, editWarehouseApi, warehouseDetailApi} from '#/api';

import {
  BasicPopup,
  usePopupInner,
} from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import {
  Button,
  Col,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Textarea,
} from 'ant-design-vue';

const emit = defineEmits(['register', 'ok']);

const executorCompanyOptions = ref([
  { label: '上海负责人', value: '1' },
  { label: '北京负责人', value: '2' },
]);

const { getDictList } = useDictStore();

// 默认数据
const defaultForm: Partial<AddWarehouse> = {
  id: undefined,
  createTime: "",
  createBy: 0,
  updateTime: "",
  updateBy: 0,
  deleteFlag: true,
  version: 0,
  warehouseCode: "",
  warehouseName: "",
  warehouseCompanyId: 0,
  warehouseCompanyName: "",
  warehouseCompanyCode: "",
  warehouseType: "",
  province: "",
  city: "",
  district: "",
  detailAddress: "",
  status: "",
  remarks: ""
};

const detailForm = reactive<Partial<AddWarehouse>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  // warehouseName: [{ required: true, message: '请输入仓库名称', trigger: 'change' }],
  // warehouseType: [{ required: true, message: '请输入仓库类型', trigger: 'change' }],
  // warehouseCompanyId: [{ required: true, message: '请选择仓库使用主体', trigger: 'change' }],
  // province: [{ required: true, message: '请选择省', trigger: 'change' }],
  // city: [{ required: true, message: '请选择市', trigger: 'change' }],
  // detailAddress: [{ required: true, message: '请输入详细地址', trigger: 'change' }],
  // remarks: [{ required: false }]
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const init = async (data: any) => {
  if (data.id) {
    const res = await warehouseDetailApi(data.id); // 调用真实 API
    Object.assign(detailForm, res);
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();
    changeOkLoading(true);
    const res = detailForm.id
      ? await editWarehouseApi(detailForm)
      : await addWarehouseApi(detailForm);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

const labelCol = { style: { width: '150px' } };

</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <!-- 仓库编号 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库编号" name="warehouseCode">
            <Input v-model:value="detailForm.warehouseCode" />
          </FormItem>
        </Col>

        <!-- 仓库名称 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库名称" name="warehouseName">
            <Input v-model:value="detailForm.warehouseName" />
          </FormItem>
        </Col>

        <!-- 所属仓储企业 -->
<!--        <Col v-bind="colSpan">-->
<!--          <FormItem label="所属仓储企业" name="businessStructure">-->
<!--            <Select v-model:value="detailForm.businessStructure" :options="businessStructureOptions" />-->
<!--          </FormItem>-->
<!--        </Col>-->

        <!-- 统一社会信用代码 -->
<!--        <Col v-bind="colSpan">-->
<!--          <FormItem label="统一社会信用代码" name="projectModel">-->
<!--            <Select v-model:value="detailForm.projectModel" :options="projectModelOptions" />-->
<!--          </FormItem>-->
<!--        </Col>-->

        <!-- 仓库类型 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库类型" name="warehouseType">
            <Select v-model:value="detailForm.warehouseType" :options="executorCompanyOptions" />
          </FormItem>
        </Col>

        <!-- 仓库使用主体 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库使用主体" name="warehouseCompanyId">
            <Select v-model:value="detailForm.warehouseCompanyId" :options="executorCompanyOptions" />
          </FormItem>
        </Col>

        <!-- 仓库地址 占一行 -->
        <Col span="24">
          <FormItem label="仓库地址" name="detailAddress">
            <Row :gutter="16">
              <Col span="8">
                <Select v-model:value="detailForm.province" :options="getDictList('province')" />
              </Col>
              <Col span="8">
                <Select v-model:value="detailForm.city" :options="getDictList('city')" />
              </Col>
              <Col span="8">
                <Input v-model:value="detailForm.detailAddress" placeholder="详细地址" />
              </Col>
            </Row>
          </FormItem>
        </Col>

        <!-- 仓库占地面积 -->
<!--        <Col v-bind="colSpan">-->
<!--          <FormItem label="仓库占地面积(㎡)" name="area">-->
<!--            <InputNumber v-model:value="detailForm.area" style="width: 100%" />-->
<!--          </FormItem>-->
<!--        </Col>-->

        <!-- 仓库建筑结构 -->
<!--        <Col v-bind="colSpan">-->
<!--          <FormItem label="仓库建筑结构" name="constructionType">-->
<!--            <Select v-model:value="detailForm.constructionType" :options="isGoodsControlModeOptions" />-->
<!--          </FormItem>-->
<!--        </Col>-->

        <!-- 备注 -->
        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
