<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';
import { VbenIcon } from '@vben-core/shadcn-ui';

import { getWarehousePageApi } from '#/api';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, Space, DatePicker, TypographyLink } from 'ant-design-vue';

import Create from './create.vue';

const dictStore = useDictStore();

// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'warehouseCode',
      label: '仓库编号',
    },
    {
      component: 'Input',
      fieldName: 'warehouseName',
      label: '仓库名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '仓库状态',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'warehouseCompanyName',
      label: '所属仓储企业',
    },
    {
      component: 'Select',
      fieldName: 'warehouseType',
      label: '仓库类型',
      // componentProps: {
      //   options: dictStore.getDictList('supplier_company'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'province', // province  city  district
      label: '仓库行政区划',
      // componentProps: {
      //   options: dictStore.getDictList('purchaser_company'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'detailAddress',
      label: '仓库详细地址',
    },
    {
      component: 'Select',
      fieldName: 'isLocationManaged',
      label: '启用仓位管理',
      // componentProps: {
      //   options: dictStore.getDictList('project_status'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'aaa',
      label: '仓库使用主体',
      // componentProps: {
      //   options: dictStore.getDictList('project_status'), // 补全字典类型
      // },
    },
    {
      component: 'Select',
      fieldName: 'bbb',
      label: '仓库建筑结构',
      // componentProps: {
      //   options: dictStore.getDictList('project_status'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'createBy',
      label: '创建人',
    },
    {
      component: 'DatePicker',
      fieldName: 'createTime',
      label: '创建时间',
    },
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'warehouseCode', title: '仓库编号'},
    { field: 'warehouseName', title: '仓库名称' },
    { field: 'warehouseCompanyName', title: '所属仓储企业' },
    { field: 'province', title: '仓库行政区划', formatter: ({ row }) => `${row.province || ''} ${row.city || ''} ${row.district || ''}` },
    { field: 'detailAddress', title: '仓库详细地址' },
    { field: 'isLocationManaged', title: '启用仓位管理', formatter: ['formatBoolean', { true: '启用', false: '禁用' }] },
    { field: 'executorCompanyName', title: '仓位数量' },
    // { field: 'status', title: '仓库类型' },
    { field: 'approvalStatus', title: '仓库使用主体' },
    { field: 'businessManagerName', title: '仓库建筑结构' },
    { field: 'status', title: '仓库状态', formatter: ({ cellValue }) => cellValue == 'ACTIVE' ? '启用' : cellValue == 'INACTIVE' ? '停用' : '' },
    { field: 'remarks', title: '备注' },
    { field: 'createTime', title: '创建时间' },
    { field: 'createBy', title: '创建人' },
    { field: 'createDeptName', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getWarehousePageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const add = () => {
  openFormPopup(true, {});
};

// 定义正确的类型
interface ProjectItem {
  id: number;
  warehouseCode: string;
  warehouseName: string;
  warehouseCompanyName: string;
  projectModel: string;
  supplierCompanyName: string;
  purchaserCompanyName: string;
  executorCompanyName: string;
  status: string; // 修复字段名
  approvalStatus: string;
  businessManagerName: string;
  businessDate: string;
  createTime: string;
  createBy: string;
  createDeptName: string;
}

const edit = (row: ProjectItem) => {
  openFormPopup(true, row);
};

const detail = (row: ProjectItem) => {
  openFormPopup(true, row);
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};


</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
