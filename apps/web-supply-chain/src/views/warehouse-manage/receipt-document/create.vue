<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { type AddInbound, type InboundReceiptItemBOs,  addInboundApi, editInboundApi, inboundDetailApi } from '#/api';

const emit = defineEmits(['register', 'ok']);

// 字典选项
const businessStructureOptions = ref([
  { label: '先采后销', value: '1' },
  { label: '先销后采', value: '2' },
]);

const projectModelOptions = ref([
  { label: '建材模式', value: '1' },
  { label: '产业模式', value: '2' },
]);

const executorCompanyOpations = ref([
  { label: 'aa', value: '1' },
  { label: 'bb', value: '2' },
]);

const executorCompanyOptions = ref([
  { label: '上海负责人', value: '1' },
  { label: '北京负责人', value: '2' },
]);

const { getDictList } = useDictStore();

const generateRandomString = (length: number): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
}

// 默认数据
const defaultForm: Partial<AddInbound> = {
  id: undefined,
  createBy: 0,
  createTime: '',
  updateBy: 0,
  updateTime: '',
  version: 0,
  inboundReceiptCode: generateRandomString(12),
  receiptDate:"",
  projectId: 0,
  projectName: generateRandomString(12),
  projectCode: generateRandomString(12),
  warehouseId: 0,
  warehouseCode: generateRandomString(12),
  warehouseName: generateRandomString(12),
  customerCompanyCode: generateRandomString(12),
  customerCompanyName: generateRandomString(12),
  sourceDocumentType: generateRandomString(12),
  deliveryReceiptId: 0,
  deliveryReceiptDisplay: generateRandomString(12),
  amountWithTax: 0,
  invoicedAmountWithTax: 0,
  status: generateRandomString(12),
  approvalStatus: generateRandomString(12),
  invoiceStatus: generateRandomString(12),
  remarks: generateRandomString(12),
  isSnManaged: 0,
  inboundReceiptSourceRelBOS: [{
      sourceDocumentId: 123,
      sourceDocumentDisplay: '345',
      sourceDocumentType: '555'
    },
  ],
  inboundReceiptItemBOs: [],
};

let detailForm = reactive<Partial<AddInbound>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  // inboundReceiptCode: [{ required: true, message: '入库单编号', trigger: 'change' }],
  // projectName: [{ required: true, message: '所属项目名称', trigger: 'change' }],
  // sourceDocumentType: [{ required: true, message: '关联单据类型', trigger: 'change' }],
  // deliveryReceiptId: [{ required: true, message: '关联单据', trigger: 'change' }],
  // receiptDate: [{ required: true, message: '入库日期', trigger: 'change' }],
  // customerCompanyName: [{ required: true, message: '上/下游企业', trigger: 'change' }],
  // warehouseName: [{ required: true, message: '仓库名称', trigger: 'change' }],
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const init = async (data: any) => {
  if (data.id) {
    const res = await inboundDetailApi(data.id); // 调用真实 API

    // 深度复制确保响应性
    Object.keys(res).forEach(key => {
      detailForm[key] = res[key];
    });

    // 强制校验并转换inboundReceiptItemBOs字段
    if (!Array.isArray(res.inboundReceiptItemBOs) || res.inboundReceiptItemBOs === null) {
      detailForm.inboundReceiptItemBOs = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.inboundReceiptItemBOs = [...res.inboundReceiptItemBOs];
    }

    // 强制刷新表格
    if (gridApiLocation?.grid) {
      gridApiLocation.grid.reloadData(detailForm.inboundReceiptItemBOs);
    }
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
    detailForm.inboundReceiptItemBOs = defaultForm.inboundReceiptItemBOs ? [...defaultForm.inboundReceiptItemBOs] : [];
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();

    changeOkLoading(true);
    // 显式类型断言确保类型正确
    const submitData = detailForm as Required<AddInbound>;

    // 根据是否存在id判断是新增还是编辑
    const res = detailForm.id
      ? await editInboundApi(submitData)
      : await addInboundApi(submitData);

    // 使用国际化提示保存成功
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

const labelCol = { style: { width: '150px' } };

// 在表格配置中添加key属性确保强制刷新
const gridLocation: VxeTableGridOptions = {
  data: detailForm.inboundReceiptItemBOs,
  props: {
    key: computed(() => detailForm.id || 'new') // 添加key属性
  },
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      editRender: {},
      slots: { edit: 'edit_product_name' },
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      editRender: {},
      slots: { edit: 'edit_product_alias' },
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      editRender: {},
      slots: { edit: 'edit_product_code' },
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      editRender: {},
      slots: { edit: 'edit_specifications' },
      minWidth: '150px',
    },
    { field: 'brandName', title: '商品品牌', editRender: {}, slots: { edit: 'edit_brand_name' }, minWidth: '150px' },
    {
      field: 'originName',
      title: '生产厂家',
      editRender: {},
      slots: { edit: 'edit_origin_name' },
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      editRender: {},
      slots: { edit: 'edit_measure_unit' },
      minWidth: '150px',
    },
    // { field: 'locationType', title: '已入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, //  后台反
    // { field: 'locationType', title: '未入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, // 源单减去已入库
    {
      field: 'quantity',
      title: '本次入库重量',
      editRender: {},
      slots: { edit: 'edit_quantity' },
      minWidth: '150px',
    }, // 自己填，得小于未入库
    {
      field: 'sourceDocumentItemNumber',
      title: '订单商品行号',
      editRender: {},
      slots: { edit: 'edit_source_document_item_number' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentDisplay',
      title: '采购订单编号',
      editRender: {},
      slots: { edit: 'edit_source_document_display' },
      minWidth: '150px',
    },
    { field: 'serialNumbers', title: 'SN码', editRender: {}, slots: { edit: 'edit_serial_numbers' }, minWidth: '150px' },
    { field: 'remarks', title: '备注', editRender: {}, slots: { edit: 'edit_remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

// 新增行
const addLocationRow = async (gridApi: GridApi) => {
  const newRecord = {
    id: undefined,
    createBy: 0,
    createTime: '',
    updateBy: 0,
    updateTime: '',
    version: 0,
    inboundReceiptId: 0,
    sourceDocumentItemNumber: '0',
    productName: '',
    productCode: '',
    productAlias: '',
    measureUnit: '',
    specifications: '',
    originName: '',
    brandName: '',
    quantity: 0,
    warehouseId: 0,
    serialNumbers: '',
    sourceDocumentType: '',
    sourceDocumentId: 0,
    sourceDocumentDisplay: '',
    sourceDocumentItemId: 0,
    sourceDocumentItemDisplay: '0',
    remarks: '',
  };
  const $grid = gridApi.grid;
  if ($grid) {
    try {
      // 插入新行前强制同步数据源
      if (!Array.isArray(detailForm.inboundReceiptItemBOs)) {
        detailForm.inboundReceiptItemBOs = [];
      }

      // 使用扩展运算符确保数组响应性
      detailForm.inboundReceiptItemBOs = [...detailForm.inboundReceiptItemBOs, newRecord];

      // 替换为官方支持的刷新方式
      if (typeof $grid.reloadData === 'function') {
        $grid.reloadData(detailForm.inboundReceiptItemBOs);
      }
    } catch (error) {
      console.error('插入行失败:', error);
      message.error('添加行失败，请检查字段类型或网络状态');
    }
  }
};

// 删除行
const removeLocationRow = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (!Array.isArray(selectRecords)) {
      message.warning('请选择要删除的数据');
      return;
    }

    if (selectRecords.length > 0) {
      const { deleteInboundReceiptItemBOs } = detailForm;

      // 确保 deleteInboundReceiptItemBOs 是数组
      if (!Array.isArray(deleteInboundReceiptItemBOs)) {
        detailForm.deleteInboundReceiptItemBOs = [...selectRecords];
      } else {
        detailForm.deleteInboundReceiptItemBOs = [...deleteInboundReceiptItemBOs, ...selectRecords];
      }

      $grid.remove(selectRecords);
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

// 注册表格
const [GridLocation, gridApiLocation] = useVbenVxeGrid({
  gridOptions: gridLocation,
  watch: {
    // 监听detailForm.inboundReceiptItemBOs变化
    data: {
      handler: (newVal) => {
        if (gridApiLocation?.grid && newVal) {
          gridApiLocation.grid.reloadData(newVal);
        }
      },
      deep: true,
      immediate: true
    }
  }
});
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <!-- 入库单编号 -->
        <Col v-bind="colSpan">
          <FormItem label="入库单编号" name="inboundReceiptCode">
            <Input v-model:value="detailForm.inboundReceiptCode" disabled />
          </FormItem>
        </Col>

        <!-- 关联收货单编号 -->
        <Col v-bind="colSpan">
          <FormItem label="关联收货单编号" name="deliveryReceiptDisplay">
            <Select v-model:value="detailForm.deliveryReceiptDisplay" :options="businessStructureOptions" />
          </FormItem>
        </Col>

        <!-- 所属项目名称 -->
        <Col v-bind="colSpan">
          <FormItem label="所属项目名称" name="projectName">
            <Select v-model:value="detailForm.projectName" :options="projectModelOptions" />
          </FormItem>
        </Col>

        <!-- 所属项目编号 -->
        <Col v-bind="colSpan">
          <FormItem label="所属项目编号" name="projectCode">
            <Input v-model:value="detailForm.projectCode" disabled />
          </FormItem>
        </Col>

        <!-- 关联单据类型 -->
        <Col v-bind="colSpan">
          <FormItem label="关联单据类型" name="sourceDocumentType">
            <Select v-model:value="detailForm.sourceDocumentType" :options="executorCompanyOpations" />
          </FormItem>
        </Col>

        <!-- 关联单据 -->
        <Col v-bind="colSpan">
          <FormItem label="关联单据" name="deliveryReceiptId">
            <Select v-model:value="detailForm.deliveryReceiptId" :options="executorCompanyOptions" />
          </FormItem>
        </Col>

        <!-- 入库日期 -->
        <Col v-bind="colSpan">
          <FormItem label="入库日期" name="receiptDate">
            <DatePicker
              v-model:value="detailForm.receiptDate"
              value-format="YYYY-MM-DD"
              :options="executorCompanyOptions"
            />
          </FormItem>
        </Col>

        <!-- 上/下游企业 -->
        <Col v-bind="colSpan">
          <FormItem label="上/下游企业" name="customerCompanyName">
            <Select v-model:value="detailForm.customerCompanyName" :options="executorCompanyOptions" />
          </FormItem>
        </Col>

        <!-- 仓库名称 -->
        <Col v-bind="colSpan">
          <FormItem label="仓库名称" name="warehouseName">
            <Select v-model:value="detailForm.warehouseName" :options="executorCompanyOptions" />
          </FormItem>
        </Col>

        <!-- 备注 -->
        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>

      <!-- 仓位信息 -->
      <BasicCaption content="商品信息" />
      <div>
        <GridLocation>
          <template #toolbar_location_tools>
            <Button class="mr-2" type="primary" @click="() => addLocationRow(gridApiLocation)">增行</Button>
            <Button class="mr-2" danger @click="() => removeLocationRow(gridApiLocation)">删行</Button>
          </template>

          <template #edit_product_name="{ row }">
            <Input v-model:value="row.productName" placeholder="请输入商品名称" />
          </template>

          <template #edit_product_alias="{ row }">
            <Input v-model:value="row.productAlias" placeholder="请输入商品别名" />
          </template>

          <template #edit_product_code="{ row }">
            <Input v-model:value="row.productCode" placeholder="请输入商品编码" />
          </template>

          <template #edit_specifications="{ row }">
            <Input v-model:value="row.specifications" placeholder="请输入规格型号" />
          </template>

          <template #edit_brand_name="{ row }">
            <Input v-model:value="row.brandName" placeholder="请输入商品品牌" />
          </template>

          <template #edit_origin_name="{ row }">
            <Input v-model:value="row.originName" placeholder="请输入生产厂家" />
          </template>

          <template #edit_measure_unit="{ row }">
            <Input v-model:value="row.measureUnit" placeholder="请输入计量单位" />
          </template>

          <template #edit_quantity="{ row }">
            <Input v-model:value="row.quantity" placeholder="请输入本次入库重量" />
          </template>

          <template #edit_source_document_item_number="{ row }">
            <Input v-model:value="row.sourceDocumentItemNumber" placeholder="请输入订单商品行号" />
          </template>

          <template #edit_source_document_display="{ row }">
            <Input v-model:value="row.sourceDocumentDisplay" placeholder="请输入采购订单编号" />
          </template>

          <template #edit_serial_numbers="{ row }">
            <Input v-model:value="row.serialNumbers" placeholder="请输入SN码" />
          </template>

          <template #edit_remarks="{ row }">
            <Input v-model:value="row.remarks" placeholder="请输入备注" />
          </template>
        </GridLocation>
      </div>
    </Form>
  </BasicPopup>
</template>

<style scoped>
:where(.css-dev-only-do-not-override-1gaak89).ant-picker {
  width: 100%;
}
</style>
