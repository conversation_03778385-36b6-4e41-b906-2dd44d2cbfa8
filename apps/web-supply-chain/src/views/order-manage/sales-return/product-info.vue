<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrderProductInfo } from '#/api';

import { ref } from 'vue';

import { defaultsDeep } from '@vben/utils';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { exportProductApi } from '#/api';
import { calculateTaxAmount, dividedByFun, formatFun, minusFun, plusFun, timesFun } from '#/utils/calculate';

import BatchSettingModal from '../components/batchSettingModal.vue';
import { baseGridOptions } from '../components/config';

const productList = ref<OrderProductInfo[]>([]);
const batchSettingModal = ref();
const originalProductList = ref<OrderProductInfo[]>([]);
// 新增初始化方法
const setProductData = (data: OrderProductInfo[]) => {
  productList.value = data;
  originalProductList.value = defaultsDeep(data); // 备份原始数据
  if (gridApi.grid) {
    gridApi.grid.reloadData(productList.value);
  }
};
// 表格配置
const gridOptions: VxeTableGridOptions = {
  ...baseGridOptions,
  data: productList.value,
  editRules: {
    returnQuantity: [{ required: true, message: '请输入重量' }],
    priceWithTax: [{ required: true, message: '请输入单价' }],
    taxRate: [{ required: true, message: '请输入税率' }],
  },
  columns: [
    { field: 'checkbox', type: 'checkbox', width: '50px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      slots: { edit: 'edit_productName' },
      minWidth: '100px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      slots: { edit: 'edit_productAlias' },
      minWidth: '160px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { edit: 'edit_skuName' },
      minWidth: '160px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      slots: { edit: 'edit_spuCode' },
      minWidth: '160px',
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      slots: { edit: 'edit_measureUnit' },
      minWidth: '160px',
    },
    {
      field: 'brandName',
      title: '商品品牌',
      slots: { edit: 'edit_brandName' },
      minWidth: '160px',
    },
    {
      field: 'originName',
      title: '生产厂家',
      slots: { edit: 'edit_originName' },
      minWidth: '160px',
    },
    {
      field: 'originalQuantity',
      title: '销售重量',
      slots: { edit: 'edit_originalQuantity' },
      minWidth: '160px',
    },
    {
      field: 'returnQuantity',
      title: '退货重量',
      slots: { default: 'edit_returnQuantity' },
      minWidth: '160px',
    },
    {
      field: 'priceWithTax',
      title: '含税单价',
      slots: { default: 'edit_priceWithTax' },
      minWidth: '160px',
    },
    {
      field: 'taxRate',
      title: '税率(%)',
      slots: { default: 'edit_taxRate' },
      minWidth: '160px',
    },
    {
      field: 'amountWithTax',
      title: '含税金额',
      slots: { edit: 'edit_amountWithTax' },
      minWidth: '160px',
    },
    {
      field: 'taxAmount',
      title: '税额',
      slots: { edit: 'edit_taxAmount' },
      minWidth: '160px',
    },
    {
      field: 'amountWithoutTax',
      title: '不含税金额',
      slots: { edit: 'edit_amountWithoutTax' },
      minWidth: '160px',
    },
    {
      field: 'priceWithoutTax',
      title: '不含税单价',
      slots: { default: 'edit_priceWithoutTax' },
      minWidth: '160px',
    },
    {
      field: 'itemNumber',
      title: '采购订单行号',
      type: 'seq',
      minWidth: '160px',
    },
    {
      field: 'remarks',
      title: '备注',
      slots: { default: 'edit_remarks' },
      minWidth: '160px',
    },
  ],
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let amountWithTax = 0;
    let taxAmount = 0;
    let amountWithoutTax = 0;
    data.forEach((item) => {
      amountWithTax = plusFun(amountWithTax, item.amountWithTax || 0);
      taxAmount = plusFun(taxAmount, item.taxAmount || 0);
      amountWithoutTax = plusFun(amountWithoutTax, item.amountWithoutTax || 0);
    });
    const footerRow = {
      checkbox: '合计',
      amountWithTax: formatFun(amountWithTax),
      taxAmount: formatFun(taxAmount),
      amountWithoutTax: formatFun(amountWithoutTax),
    };
    return [footerRow];
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
// 删除行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  getProductData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const { visibleData } = gridApi.grid.getTableData();
      // 插入序列
      const processedData = visibleData.map((item, index) => ({
        ...item,
        itemNumber: index + 1,
      }));
      return processedData;
    } else {
      return [];
    }
  },
  setProductData,
});
const useTaxPriceCalc = ref(false); // 控制计算方式

const calculateRow = (row: OrderProductInfo) => {
  const quantity = row.returnQuantity || 0; // 数量
  const taxRate = dividedByFun(row.taxRate || 0, 100); // 税率
  if (useTaxPriceCalc.value) {
    const priceWithoutTax = row.priceWithoutTax || 0; // 不含税单价

    // 不含税金额
    const amountWithoutTax = timesFun(priceWithoutTax, quantity);
    // 含税金额
    const amountWithTax = timesFun(amountWithoutTax, plusFun(1, taxRate));
    // 税额
    const taxAmount = minusFun(amountWithTax, amountWithoutTax);
    // 含税单价
    const priceWithTax = dividedByFun(amountWithTax, quantity);
    // 最终展示值
    row.amountWithoutTax = formatFun(amountWithoutTax);
    row.amountWithTax = formatFun(amountWithTax);
    row.taxAmount = formatFun(taxAmount);
    row.priceWithTax = formatFun(priceWithTax);
  } else {
    const priceWithTax = row.priceWithTax || 0; // 含税单价

    // 含税金额
    const amountWithTax = timesFun(quantity, priceWithTax);
    // 税额
    const taxAmount = calculateTaxAmount(amountWithTax, taxRate);
    // 不含税金额
    const amountWithoutTax = minusFun(amountWithTax, taxAmount);
    // 不含税单价
    const priceWithoutTax = dividedByFun(amountWithoutTax, quantity);
    // 最终展示值
    row.amountWithTax = formatFun(amountWithTax);
    row.taxAmount = formatFun(taxAmount);
    row.amountWithoutTax = formatFun(amountWithoutTax);
    row.priceWithoutTax = formatFun(priceWithoutTax);
  }
  if (gridApi.grid) {
    gridApi.grid.updateFooter();
  }
};
// 批量设置
const batchSetting = () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      batchSettingModal.value.openModal(useTaxPriceCalc.value);
    } else {
      message.warning('请选择数据');
    }
  }
};
// 批量设置提交
const batchSettingConfirm = (data: { inputValue: number; radioType: number }) => {
  const $grid = gridApi.grid;
  const selectRecords = $grid.getCheckboxRecords();
  const visibleData = $grid.getTableData().visibleData;
  const updatedData = visibleData.map((row) => {
    if (selectRecords.includes(row)) {
      const updatedRow = { ...row };

      if (useTaxPriceCalc.value) {
        // 按不含税单价计算模式
        switch (data.radioType) {
          case 1: {
            updatedRow.priceWithoutTax = data.inputValue;
            break;
          }
          case 2: {
            updatedRow.priceWithoutTax = plusFun(
              updatedRow.priceWithoutTax,
              timesFun(updatedRow.priceWithoutTax, dividedByFun(data.inputValue, 100)),
            );
            break;
          }
          case 3: {
            updatedRow.priceWithoutTax = plusFun(updatedRow.priceWithoutTax, data.inputValue);
            break;
          }
        }
      } else {
        // 按含税单价计算模式
        switch (data.radioType) {
          case 1: {
            updatedRow.priceWithTax = data.inputValue;
            break;
          }
          case 2: {
            updatedRow.priceWithTax = plusFun(
              updatedRow.priceWithTax,
              timesFun(updatedRow.priceWithTax, dividedByFun(data.inputValue, 100)),
            );
            break;
          }
          case 3: {
            updatedRow.priceWithTax = plusFun(updatedRow.priceWithTax, data.inputValue);
            break;
          }
        }
      }
      // 触发行计算
      calculateRow(updatedRow);
      return updatedRow;
    }
    return row;
  });
  // 更新数据源并刷新表格
  productList.value = updatedData;
  $grid.reloadData(updatedData);
};
// 重置
const resetTableData = () => {
  const $grid = gridApi.grid;
  if ($grid) {
    // 重新加载原始数据
    const resetData = defaultsDeep(originalProductList.value);
    productList.value = resetData;
    $grid.reloadData(resetData);
  }
};
</script>

<template>
  <div>
    <Grid>
      <template #toolbar-actions>
        <a-checkbox class="mr-2" v-model:checked="useTaxPriceCalc"> 按不含税单价计算 </a-checkbox>
      </template>
      <template #toolbarTools>
        <a-space>
          <a-button class="mr-2" type="primary" @click="exportProductApi(productList)"> 导出商品 </a-button>
          <a-button class="mr-2" type="primary" @click="batchSetting">批量设置</a-button>
          <a-button class="mr-2" @click="resetTableData"> 重置 </a-button>
          <a-button class="mr-2" danger @click="removeAccount">删行</a-button>
        </a-space>
      </template>
      <template #edit_productName="{ row }">
        <a-input v-model:value="row.productName" />
      </template>
      <template #edit_productAlias="{ row }">
        <a-input v-model:value="row.productAlias" />
      </template>
      <template #edit_skuName="{ row }">
        <a-input v-model:value="row.specifications" />
      </template>
      <template #edit_spuCode="{ row }">
        <a-input v-model:value="row.productCode" />
      </template>
      <template #edit_measureUnit="{ row }">
        <Input v-model:value="row.measureUnit" />
      </template>
      <template #edit_brandName="{ row }">
        <a-input v-model:value="row.brandName" />
      </template>
      <template #edit_originName="{ row }">
        <a-input v-model:value="row.originName" />
      </template>
      <template #edit_originalQuantity="{ row }">
        <a-input-number v-model:value="row.originalQuantity" />
      </template>
      <template #edit_returnQuantity="{ row }">
        <a-input-number v-model:value="row.returnQuantity" placeholder="请输入重量" @change="calculateRow(row)" />
      </template>
      <template #edit_priceWithTax="{ row }">
        <a-input-number v-model:value="row.priceWithTax" placeholder="请输入含税单价" @change="calculateRow(row)" />
      </template>
      <template #edit_taxRate="{ row }">
        <a-input-number v-model:value="row.taxRate" placeholder="请输入税率" @change="calculateRow(row)" />
      </template>
      <template #edit_amountWithTax="{ row }">
        <a-input-number v-model:value="row.amountWithTax" />
      </template>
      <template #edit_taxAmount="{ row }">
        <a-input-number v-model:value="row.taxAmount" />
      </template>
      <template #edit_amountWithoutTax="{ row }">
        <a-input-number v-model:value="row.amountWithoutTax" />
      </template>
      <template #edit_priceWithoutTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithoutTax"
          placeholder="请输入不含税单价"
          @change="calculateRow(row)"
        />
      </template>
      <template #edit_remarks="{ row }">
        <a-input v-model:value="row.remarks" placeholder="请输入备注" />
      </template>
    </Grid>
    <Modal />
    <BatchSettingModal ref="batchSettingModal" @confirm="batchSettingConfirm" />
  </div>
</template>

<style scoped></style>
