<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { OrderInfo } from '#/api';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { defaultsDeep } from '@vben/utils';

import { message } from 'ant-design-vue';

import {
  addPurchaseReturnListApi,
  editPurchaseReturnListApi,
  getPurchaseListApi,
  infoPurchaseListApi,
  infoPurchaseReturnListApi,
} from '#/api';

import ProductInfo from './product-info.vue';

const emit = defineEmits(['register', 'ok']);

// 根据接口定义初始化信息
const defaultForm: Partial<OrderInfo> = {
  projectId: 0,
  supplierId: 0,
  operationManagerId: 0,
  purchaseReturnOrderCode: '',
  purchaseReturnOrderName: '',
  supplierName: '',
  executorCompanyName: '',
  projectName: '',
  projectCode: '',
  businessStructure: '',
  projectModel: '',
  salesOrderId: '',
  salesOrderCode: '',
  businessDate: '',
  estimatedEndDate: '',
  prepaymentRatio: '',
  prepaymentAmount: '',
  remarks: '',
  businessManagerName: '',
  operationManagerName: '',
  depositAmount: '',
  advanceRatio: '',
  paymentTermDays: '',
};

const orderInfo = ref<Partial<OrderInfo>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
// 更新验证规则以匹配接口字段
const rules: Record<string, Rule[]> = {
  purchaseReturnOrderCode: [{ required: true, message: '请输入采购退货订单编号', trigger: 'blur' }],
  purchaseReturnOrderName: [{ required: true, message: '请输入采购退货订单名称', trigger: 'blur' }],
  projectName: [{ required: true, message: '请选择所属项目名称', trigger: 'change' }],
  projectCode: [{ required: true, message: '请输入所属项目编号', trigger: 'blur' }],
  businessStructure: [{ required: true, message: '请输入业务结构', trigger: 'blur' }],
  projectModel: [{ required: true, message: '请输入项目模式', trigger: 'blur' }],
  // 关联采购订单
  purchaseOrderCode: [{ required: true, message: '请选择关联采购订单', trigger: 'change' }],
  executorCompanyName: [{ required: true, message: '请输入贸易执行企业名称', trigger: 'blur' }],
  supplierName: [{ required: false, message: '请选择上游企业', trigger: 'change' }],
  businessManagerName: [{ required: true, message: '请输入业务负责人名称', trigger: 'blur' }],
  operationManagerName: [{ required: true, message: '请输入运营负责人名称', trigger: 'blur' }],
  businessDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  estimatedEndDate: [{ required: false, message: '请选择预计结束日期', trigger: 'change' }],
  remarks: [{ required: false, message: '请输入备注', trigger: 'blur' }],
};
const title = computed(() => {
  return orderInfo.value.id ? '编辑采购退货订单' : '新增采购退货订单';
});
const productGridRef = ref();
const codesOptions = ref<{ label: string; record: object; value: number }[]>([]);
const init = async (data: OrderInfo) => {
  if (data.id) {
    orderInfo.value = await infoPurchaseReturnListApi({ id: data.id });
    productGridRef.value.setProductData(orderInfo.value.purchaseReturnOrderItemVOS);
  } else {
    orderInfo.value = defaultsDeep(defaultForm);
  }
  loadCodesOptions();
};

// 加载关联订单选项
const loadCodesOptions = async () => {
  const { records } = await getPurchaseListApi({ current: 1, size: 1000 });
  const options = records.map((item: OrderInfo) => {
    return {
      label: `${item.purchaseOrderCode}(${item.purchaseOrderName})`,
      value: item.purchaseOrderCode,
      record: item,
    };
  });
  codesOptions.value = options;
};
// 处理关联订单选择事件的函数
const handleCodesSelect = async (value: number) => {
  const selectedCode = codesOptions.value.find((option) => option.value === value);
  if (selectedCode) {
    const record = selectedCode.record as OrderInfo;
    orderInfo.value.purchaseOrderId = record.id;
    orderInfo.value.purchaseOrderName = record.purchaseOrderName;
    orderInfo.value.projectName = record.projectName;
    orderInfo.value.projectCode = record.projectCode;
    orderInfo.value.executorCompanyName = record.executorCompanyName;
    orderInfo.value.supplierName = record.supplierName;
    // 选择关联订单后，查询订单详情接口获取商品信息传给商品表格
    const itemRecord = await infoPurchaseListApi({ id: record.id });
    // 去掉id字段
    const processedData = itemRecord.purchaseOrderItemVOS
      ? itemRecord.purchaseOrderItemVOS.map(({ id: _id, ...rest }) => ({
          ...rest,
          originalQuantity: rest.quantity,
          returnQuantity: rest.quantity,
        }))
      : [];
    productGridRef.value.setProductData(processedData);
  }
};
const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  orderInfo.value.purchaseReturnOrderItemBOS = productGridRef.value?.getProductData() || [];
  changeOkLoading(true);
  let api = addPurchaseReturnListApi;
  if (orderInfo.value.id) {
    api = editPurchaseReturnListApi;
  }
  try {
    const res = await api(orderInfo.value as OrderInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <a-form
      ref="formRef"
      :colon="false"
      :model="orderInfo"
      :rules="rules"
      :label-col="{ style: { width: '150px' } }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="采购退货单编号" name="purchaseReturnOrderCode">
            <a-input v-model:value="orderInfo.purchaseReturnOrderCode" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="采购退货订单名称" name="purchaseReturnOrderName">
            <a-input v-model:value="orderInfo.purchaseReturnOrderName" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="关联采购订单" name="purchaseOrderCode">
            <a-select v-model:value="orderInfo.purchaseOrderCode" :options="codesOptions" @select="handleCodesSelect" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目名称" name="projectName">
            <a-input v-model:value="orderInfo.projectName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目编号" name="projectCode">
            <a-input v-model:value="orderInfo.projectCode" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="贸易执行企业" name="executorCompanyName">
            <a-input v-model:value="orderInfo.executorCompanyName" disabled />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="上游企业" name="supplierName">
            <a-input v-model:value="orderInfo.supplierName" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remarks">
            <a-textarea v-model:value="orderInfo.remarks" :rows="4" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 业务信息 -->
      <BasicCaption content="业务信息" />
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="业务日期" name="businessDate">
            <a-date-picker v-model:value="orderInfo.businessDate" value-format="YYYY-MM-DD" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="预计结束日期" name="estimatedEndDate">
            <a-date-picker v-model:value="orderInfo.estimatedEndDate" value-format="YYYY-MM-DD" class="w-full" />
          </a-form-item>
        </a-col>
      </a-row>
      <!-- 商品信息 -->
      <BasicCaption content="商品信息" />
      <ProductInfo ref="productGridRef" />
    </a-form>
  </BasicPopup>
</template>
