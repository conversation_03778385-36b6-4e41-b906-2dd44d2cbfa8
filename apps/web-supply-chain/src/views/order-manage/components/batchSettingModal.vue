<script setup lang="ts">
import { computed, reactive, ref } from 'vue';

import { Modal, Segmented } from 'ant-design-vue';

const emit = defineEmits(['confirm']);

const radioStyle = reactive({
  display: 'flex',
  height: '50px',
  lineHeight: '50px',
});

const visible = ref(false);
// 是否含税单价
const useTaxPriceCalc = ref(false);

const openModal = (data: boolean) => {
  useTaxPriceCalc.value = data;
  segmentedValue.value = segmentedData.value[0] || '';
  visible.value = true;
};

const handleOk = () => {
  emit('confirm', {
    radioType: radioType.value,
    inputValue: inputValue.value,
  });
  visible.value = false;
};

const handleCancel = () => {
  visible.value = false;
};
const inputChange = (value: number) => {
  inputValue.value = value;
};
const segmentedData = computed(() =>
  useTaxPriceCalc.value ? ['调整不含税单价', '调整税率'] : ['调整含税单价', '调整税率'],
);
const segmentedValue = ref('');
const radioType = ref<number>(1);
const inputValue = ref<number>();

defineExpose({ openModal });
</script>

<template>
  <Modal v-model:visible="visible" title="批量设置" @ok="handleOk" @cancel="handleCancel">
    <div>
      <div><Segmented v-model:value="segmentedValue" :options="segmentedData" /></div>
      <br />
      <a-radio-group v-model:value="radioType">
        <a-radio :value="1" :style="radioStyle">
          <div class="flex items-center">
            <span class="w-40">设为固定值</span>
            <a-input-number
              class="w-40"
              placeholder="请输入固定值"
              :disabled="radioType !== 1"
              v-model="inputValue"
              @input="inputChange"
            />
          </div>
        </a-radio>
        <a-radio :value="2" :style="radioStyle" v-if="segmentedValue !== '调整税率'">
          <div class="flex items-center">
            <span class="w-40">基价上/下浮动比率(%)</span>
            <a-input-number
              class="w-40"
              placeholder="请输入比率"
              v-model="inputValue"
              :disabled="radioType !== 2"
              @input="inputChange"
            />
          </div>
        </a-radio>
        <a-radio :value="3" :style="radioStyle">
          <div class="flex items-center">
            <span class="w-40">基价上/下浮动值</span>
            <a-input-number
              class="w-40"
              placeholder="请输入值"
              v-model="inputValue"
              :disabled="radioType !== 3"
              @input="inputChange"
            />
          </div>
        </a-radio>
      </a-radio-group>
    </div>
  </Modal>
</template>

<style scoped></style>
