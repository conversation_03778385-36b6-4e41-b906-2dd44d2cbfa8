<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OrderInfo } from '#/api';

import { ref, watch } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';

import { infoSalesListApi } from '#/api';

defineEmits(['register']);
const orderDetail = ref<OrderInfo>({});

const init = async (data: OrderInfo) => {
  if (data?.id) {
    orderDetail.value = await infoSalesListApi({ id: data.id });
  }
};
const [registerPopup] = usePopupInner(init);

const baseGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  border: 'inner',
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const bankGridOptions = {
  columns: [
    { field: 'productName', title: '商品名称', minWidth: '160px' },
    { field: 'productAlias', title: '商品别名', minWidth: '160px' },
    { field: 'specifications', title: '规格型号', minWidth: '160px' },
    { field: 'productCode', title: '商品编码', minWidth: '160px' },
    { field: 'measureUnit', title: '计量单位', minWidth: '160px' },
    { field: 'brandName', title: '商品品牌', minWidth: '160px' },
    { field: 'originName', title: '生产厂家', minWidth: '160px' },
    { field: 'quantity', title: '销售重量', minWidth: '160px' },
    { field: 'priceWithTax', title: '含税单价', minWidth: '160px' },
    { field: 'taxRate', title: '税率(%)', minWidth: '160px' },
    { field: 'amountWithTax', title: '含税金额', minWidth: '160px' },
    { field: 'taxAmount', title: '税额', minWidth: '160px' },
    { field: 'amountWithoutTax', title: '不含税金额', minWidth: '160px' },
    { field: 'priceWithoutTax', title: '不含税单价', minWidth: '160px' },
    { field: 'itemNumber', title: '采购订单行号', minWidth: '160px' },
    { field: 'remarks', title: '备注', minWidth: '160px' },
  ],
  ...baseGridOptions,
} as VxeTableGridOptions;
const [ProducGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
watch(
  () => orderDetail.value,
  (val = {}) => {
    bankGridApi.grid.reloadData(val.salesOrderItemVOS ?? []);
  },
  { deep: true },
);
</script>

<template>
  <BasicPopup v-bind="$attrs" title="订单信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
        <a-descriptions-item label="销售订单编号">
          {{ orderDetail.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="销售订单名称">
          {{ orderDetail.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="所属项目名称">
          {{ orderDetail.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="所属项目编号">
          {{ orderDetail.projectCode }}
        </a-descriptions-item>
        <a-descriptions-item label="业务结构">
          {{ orderDetail.businessStructure }}
        </a-descriptions-item>
        <a-descriptions-item label="项目模式">
          {{ orderDetail.projectModel }}
        </a-descriptions-item>
        <a-descriptions-item label="关联采购订单" v-if="orderDetail.businessStructure === '先采后销'">
          {{ orderDetail.salesOrderName }}
        </a-descriptions-item>
        <a-descriptions-item label="采购订单编号" v-if="orderDetail.businessStructure === '先采后销'">
          {{ orderDetail.salesOrderCode }}
        </a-descriptions-item>
        <a-descriptions-item label="贸易执行企业">
          {{ orderDetail.executorCompanyName }}
        </a-descriptions-item>
        <a-descriptions-item label="上游企业">
          {{ orderDetail.supplierName }}
        </a-descriptions-item>
        <a-descriptions-item label="业务负责人">
          {{ orderDetail.businessManagerName }}
        </a-descriptions-item>
        <a-descriptions-item label="运营负责人">
          {{ orderDetail.operationManagerName }}
        </a-descriptions-item>
        <a-descriptions-item label="业务日期">
          {{ orderDetail.businessDate }}
        </a-descriptions-item>
        <a-descriptions-item label="预计结束日期">
          {{ orderDetail.estimatedEndDate }}
        </a-descriptions-item>
        <a-descriptions-item label="预付款比例(%)" v-if="orderDetail.businessStructure === '先销后采'">
          {{ orderDetail.prepaymentRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="预付款金额" v-if="orderDetail.businessStructure === '先销后采'">
          {{ orderDetail.prepaymentAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="保证金金额" v-if="orderDetail.businessStructure === '先采后销'">
          {{ orderDetail.depositAmount }}
        </a-descriptions-item>
        <a-descriptions-item label="垫资比例(%)" v-if="orderDetail.businessStructure === '先采后销'">
          {{ orderDetail.advanceRatio }}
        </a-descriptions-item>
        <a-descriptions-item label="账期（天)" v-if="orderDetail.businessStructure === '先采后销'">
          {{ orderDetail.paymentTermDays }}
        </a-descriptions-item>
        <a-descriptions-item label="备注" v-if="orderDetail.businessStructure === '先采后销'">
          {{ orderDetail.remarks }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="商品信息" />
      <ProducGrid />
    </div>
  </BasicPopup>
</template>

<style></style>
