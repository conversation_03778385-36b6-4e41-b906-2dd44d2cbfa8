<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrderProductInfo } from '#/api';

import { ref } from 'vue';

import { ImportData } from '@vben/base-ui';
import { useVbenModal } from '@vben/common-ui';

import { message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { downloadSalesTemplateApi, exportProductApi, importSalesApi } from '#/api';
import { calculateTaxAmount, dividedByFun, formatFun, minusFun, plusFun, timesFun } from '#/utils/calculate';

import BatchSettingModal from '../components/batchSettingModal.vue';
import { baseGridOptions } from '../components/config';
import ProductPopup from '../components/product-popup.vue';

const props = defineProps<{
  businessType: string;
}>();
const productList = ref<OrderProductInfo[]>([]);
const batchSettingModal = ref();

// 新增初始化方法
const setProductData = (data: OrderProductInfo[]) => {
  productList.value = data;
  if (gridApi.grid) {
    gridApi.grid.reloadData(productList.value);
  }
};
// 表格配置
const gridOptions: VxeTableGridOptions = {
  ...baseGridOptions,
  editRules: {
    quantity: [{ required: true, message: '请输入数量' }],
    priceWithTax: [{ required: true, message: '请输入单价' }],
    taxRate: [{ required: true, message: '请输入税率' }],
  },
  columns: [
    { field: 'checkbox', type: 'checkbox', width: '50px', fixed: 'left' },
    {
      field: 'productName',
      title: '商品名称',
      slots: { edit: 'edit_productName' },
      minWidth: '100px',
    },
    {
      field: 'productAlias',
      title: '商品别名',
      slots: { edit: 'edit_productAlias' },
      minWidth: '160px',
    },
    {
      field: 'specifications',
      title: '规格型号',
      slots: { edit: 'edit_skuName' },
      minWidth: '160px',
    },
    {
      field: 'productCode',
      title: '商品编码',
      slots: { edit: 'edit_spuCode' },
      minWidth: '160px',
    },
    {
      field: 'measureUnit',
      title: '计量单位',
      slots: { edit: 'edit_measureUnit' },
      minWidth: '160px',
    },
    {
      field: 'brandName',
      title: '商品品牌',
      slots: { edit: 'edit_brandName' },
      minWidth: '160px',
    },
    {
      field: 'originName',
      title: '生产厂家',
      slots: { edit: 'edit_originName' },
      minWidth: '160px',
    },
    {
      field: 'quantity',
      title: '销售重量',
      slots: { default: 'edit_quantity' },
      minWidth: '160px',
    },
    {
      field: 'priceWithTax',
      title: '含税单价',
      slots: { default: 'edit_priceWithTax' },
      minWidth: '160px',
    },
    {
      field: 'taxRate',
      title: '税率(%)',
      slots: { default: 'edit_taxRate' },
      minWidth: '160px',
    },
    {
      field: 'amountWithTax',
      title: '含税金额',
      slots: { edit: 'edit_amountWithTax' },
      minWidth: '160px',
    },
    {
      field: 'taxAmount',
      title: '税额',
      slots: { edit: 'edit_taxAmount' },
      minWidth: '160px',
    },
    {
      field: 'amountWithoutTax',
      title: '不含税金额',
      slots: { edit: 'edit_amountWithoutTax' },
      minWidth: '160px',
    },
    {
      field: 'priceWithoutTax',
      title: '不含税单价',
      slots: { default: 'edit_priceWithoutTax' },
      minWidth: '160px',
    },
    {
      field: 'itemNumber',
      title: '销售订单行号',
      type: 'seq',
      minWidth: '160px',
    },
    {
      field: 'remarks',
      title: '备注',
      slots: { default: 'edit_remarks' },
      minWidth: '160px',
    },
  ],
  footerMethod({ $grid }) {
    const data = $grid?.getTableData().visibleData || [];
    let amountWithTax = 0;
    let taxAmount = 0;
    let amountWithoutTax = 0;
    data.forEach((item) => {
      amountWithTax = plusFun(amountWithTax, item.amountWithTax || 0);
      taxAmount = plusFun(taxAmount, item.taxAmount || 0);
      amountWithoutTax = plusFun(amountWithoutTax, item.amountWithoutTax || 0);
    });
    const footerRow = {
      checkbox: '合计',
      amountWithTax: formatFun(amountWithTax),
      taxAmount: formatFun(taxAmount),
      amountWithoutTax: formatFun(amountWithoutTax),
    };
    return [footerRow];
  },
  data: productList.value,
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});

const [Modal, modalApi] = useVbenModal({
  connectedComponent: ProductPopup,
  onOpenChange: (isOpen) => {
    if (!isOpen) {
      // 弹窗关闭时获取返回的数据
      const selectedProducts = modalApi.getData() || []; // 获取 onConfirm 返回的数据
      if (Array.isArray(selectedProducts)) {
        gridApi.grid.reloadData([...productList.value, ...selectedProducts]);
      }
    }
  },
});
// 删除账户行
const removeAccount = async () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      $grid.removeCheckboxRow();
      message.success('删除成功');
    } else {
      message.warning('请选择数据');
    }
  }
};

// 暴露方法给父组件
defineExpose({
  getProductData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const { visibleData } = gridApi.grid.getTableData();
      const processedData = visibleData.map((item, index) => ({
        ...item,
        itemNumber: index + 1,
      }));
      // 获取表格footer合计数据
      const footerData: any = $grid.getTableData().footerData?.[0];
      return {
        items: processedData,
        totalAmountWithTax: footerData.amountWithTax,
        totalTaxAmount: footerData.taxAmount,
        totalAmountWithoutTax: footerData.amountWithoutTax,
      };
    } else {
      return [];
    }
  },
  setProductData,
});

const useTaxPriceCalc = ref(false); // 控制计算方式

const calculateRow = (row: OrderProductInfo) => {
  const quantity = row.quantity || 0; // 数量
  const taxRate = dividedByFun(row.taxRate || 0, 100); // 税率
  if (useTaxPriceCalc.value) {
    const priceWithoutTax = row.priceWithoutTax || 0; // 不含税单价

    // 不含税金额
    const amountWithoutTax = timesFun(priceWithoutTax, quantity);
    // 含税金额
    const amountWithTax = timesFun(amountWithoutTax, plusFun(1, taxRate));
    // 税额
    const taxAmount = minusFun(amountWithTax, amountWithoutTax);
    // 含税单价
    const priceWithTax = dividedByFun(amountWithTax, quantity);
    // 最终展示值
    row.amountWithoutTax = formatFun(amountWithoutTax);
    row.amountWithTax = formatFun(amountWithTax);
    row.taxAmount = formatFun(taxAmount);
    row.priceWithTax = formatFun(priceWithTax);
  } else {
    const priceWithTax = row.priceWithTax || 0; // 含税单价

    // 含税金额
    const amountWithTax = timesFun(quantity, priceWithTax);
    // 税额
    const taxAmount = calculateTaxAmount(amountWithTax, taxRate);
    // 不含税金额
    const amountWithoutTax = minusFun(amountWithTax, taxAmount);
    // 不含税单价
    const priceWithoutTax = dividedByFun(amountWithoutTax, quantity);
    // 最终展示值
    row.amountWithTax = formatFun(amountWithTax);
    row.taxAmount = formatFun(taxAmount);
    row.amountWithoutTax = formatFun(amountWithoutTax);
    row.priceWithoutTax = formatFun(priceWithoutTax);
  }
  if (gridApi.grid) {
    gridApi.grid.updateFooter();
  }
};
const batchSetting = () => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      batchSettingModal.value.openModal(useTaxPriceCalc.value); // 打开批量设置弹窗
    } else {
      message.warning('请选择数据');
    }
  }
};
const importSuccess = (data: any) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const newData = data;
    $grid.reloadData(newData);
  }
};
</script>

<template>
  <div>
    <Grid>
      <template #toolbar-actions>
        <a-checkbox class="mr-2" v-model:checked="useTaxPriceCalc"> 按不含税单价计算 </a-checkbox>
      </template>
      <template #toolbarTools>
        <a-space>
          <ImportData
            v-show="props.businessType === '先销后采'"
            title="导入商品"
            :upload-api="importSalesApi"
            :download-template-api="downloadSalesTemplateApi"
            @import-success="importSuccess"
          />
          <a-button
            v-show="props.businessType === '先销后采'"
            class="mr-2"
            type="primary"
            @click="modalApi.setData({}).open()"
          >
            选择商品
          </a-button>
          <a-button
            v-show="props.businessType === '先采后销'"
            class="mr-2"
            type="primary"
            @click="exportProductApi(productList, 'sales')"
          >
            导出商品
          </a-button>
          <a-button class="mr-2" type="primary" @click="batchSetting">批量设置</a-button>
          <a-button v-show="props.businessType === '先采后销'" class="mr-2" danger @click="removeAccount">
            重置
          </a-button>
          <a-button class="mr-2" danger @click="removeAccount">删行</a-button>
        </a-space>
      </template>
      <template #edit_productName="{ row }">
        <a-input v-model:value="row.productName" placeholder="请输入商品名称" />
      </template>
      <template #edit_productAlias="{ row }">
        <a-input v-model:value="row.productAlias" placeholder="请输入商品别名" />
      </template>
      <template #edit_skuName="{ row }">
        <a-input v-model:value="row.specifications" placeholder="请输入规格型号" />
      </template>
      <template #edit_spuCode="{ row }">
        <a-input v-model:value="row.productCode" placeholder="请输入商品编码" />
      </template>
      <template #edit_measureUnit="{ row }">
        <Input v-model:value="row.measureUnit" placeholder="请输入计量单位" />
      </template>
      <template #edit_brandName="{ row }">
        <a-input v-model:value="row.brandName" placeholder="请输入商品品牌" />
      </template>
      <template #edit_originName="{ row }">
        <a-input v-model:value="row.originName" placeholder="请输入生产厂家" />
      </template>
      <template #edit_quantity="{ row }">
        <a-input-number v-model:value="row.quantity" placeholder="请输入采购数量" @change="calculateRow(row)" />
      </template>
      <template #edit_priceWithTax="{ row }">
        <a-input-number v-model:value="row.priceWithTax" placeholder="请输入含税单价" @change="calculateRow(row)" />
      </template>
      <template #edit_taxRate="{ row }">
        <a-input-number v-model:value="row.taxRate" placeholder="请输入税率" @change="calculateRow(row)" />
      </template>
      <template #edit_amountWithTax="{ row }">
        <a-input-number v-model:value="row.amountWithTax" placeholder="请输入含税金额" />
      </template>
      <template #edit_taxAmount="{ row }">
        <a-input-number v-model:value="row.taxAmount" placeholder="请输入税额" />
      </template>
      <template #edit_amountWithoutTax="{ row }">
        <a-input-number v-model:value="row.amountWithoutTax" placeholder="请输入不含税金额" />
      </template>
      <template #edit_priceWithoutTax="{ row }">
        <a-input-number
          v-model:value="row.priceWithoutTax"
          placeholder="请输入不含税单价"
          @change="calculateRow(row)"
        />
      </template>
      <template #edit_remarks="{ row }">
        <a-input v-model:value="row.remarks" placeholder="请输入备注" />
      </template>
    </Grid>
    <Modal />
    <BatchSettingModal ref="batchSettingModal" />
  </div>
</template>

<style scoped></style>
