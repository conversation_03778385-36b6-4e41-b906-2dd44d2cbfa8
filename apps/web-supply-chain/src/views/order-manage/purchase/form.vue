<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { OrderInfo } from '#/api';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { message } from 'ant-design-vue';

import {
  addPurchaseListApi,
  editPurchaseListApi,
  getBaseCodesApi,
  getProjectListApi,
  infoPurchaseListApi,
  infoSalesListApi,
} from '#/api';

import ProductInfo from './product-info.vue';

const emit = defineEmits(['register', 'ok']);

const { getDictList } = useDictStore();
// 根据接口定义初始化信息
const defaultForm: Partial<OrderInfo> = {
  projectId: '',
  supplierId: '',
  operationManagerId: '',
  purchaseOrderCode: '',
  purchaseOrderName: '',
  supplierCompanyName: '',
  executorCompanyName: '',
  projectName: '',
  projectCode: '',
  businessStructure: '',
  projectModel: '',
  salesOrderId: '',
  salesOrderCode: '',
  businessDate: '',
  estimatedEndDate: '',
  prepaymentRatio: '',
  prepaymentAmount: '',
  remarks: '',
  businessManagerName: '',
  operationManagerName: '',
  depositAmount: '',
  advanceRatio: '',
  paymentTermDays: '',
  projectPartners: [],
};

const orderInfo = ref<Partial<OrderInfo>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
const rules: Record<string, Rule[]> = {
  purchaseOrderCode: [{ required: true, message: '请输入采购订单编号', trigger: 'blur' }],
  purchaseOrderName: [{ required: true, message: '请输入采购订单名称', trigger: 'blur' }],
  projectName: [{ required: true, message: '请选择所属项目名称', trigger: 'change' }],
  projectCode: [{ required: true, message: '请输入所属项目编号', trigger: 'blur' }],
  businessStructure: [{ required: true, message: '请输入业务结构', trigger: 'blur' }],
  projectModel: [{ required: true, message: '请输入项目模式', trigger: 'blur' }],
  salesOrderId: [{ required: true, message: '请选择关联销售订单', trigger: 'change' }],
  salesOrderCode: [{ required: true, message: '请输入销售订单编号', trigger: 'blur' }],
  executorCompanyName: [{ required: true, message: '请输入贸易执行企业名称', trigger: 'blur' }],
  supplierCompanyName: [{ required: false, message: '请选择上游企业', trigger: 'change' }],
  businessManagerName: [{ required: true, message: '请输入业务负责人名称', trigger: 'blur' }],
  operationManagerName: [{ required: true, message: '请输入运营负责人名称', trigger: 'blur' }],
  businessDate: [{ required: true, message: '请选择业务日期', trigger: 'change' }],
  estimatedEndDate: [{ required: true, message: '请选择预计结束日期', trigger: 'change' }],
  prepaymentRatio: [{ required: true, message: '请输入预付款比例', trigger: 'blur' }],
  prepaymentAmount: [{ required: true, message: '请输入预付款金额', trigger: 'blur' }],
  depositAmount: [{ required: true, message: '请输入保证金金额', trigger: 'blur' }],
  advanceRatio: [{ required: true, message: '请输入垫资比例', trigger: 'blur' }],
  paymentTermDays: [{ required: true, message: '请输入垫资比例', trigger: 'blur' }],
  remarks: [{ required: false, message: '请输入备注', trigger: 'blur' }],
};
const title = computed(() => {
  return orderInfo.value.id ? '编辑采购订单' : '新增采购订单';
});
const productGridRef = ref();
const projectOptions = ref<{ label: string; value: number }[]>([]);
const projectMap = ref<Record<string, OrderInfo>>({});
const codesOptions = ref<{ label: string; record: object; value: number }[]>([]);
const companyOptions = ref();
const init = async (data: OrderInfo) => {
  if (data.id) {
    orderInfo.value = await infoPurchaseListApi({ id: data.id });
    productGridRef.value.setProductData(orderInfo.value.purchaseOrderItemVOS);
    loadCodesOptions();
  } else {
    orderInfo.value = defaultsDeep(defaultForm);
  }
  loadProjectOptions();
};
// 加载项目选项
const loadProjectOptions = async () => {
  const data = await getProjectListApi({ state: '' });
  const options = data.map((item: OrderInfo) => {
    const id = item.id || '';
    projectMap.value[id] = item;
    return {
      label: item.projectName,
      value: id,
    };
  });
  projectOptions.value = options;
};
// 加载关联订单选项
const loadCodesOptions = async () => {
  const data = await getBaseCodesApi({ orderType: 'salesOrder' });
  const options = data.map((item: OrderInfo) => {
    return {
      label: `${item.orderCode}(${item.orderName})`,
      value: item.id,
      record: item,
    };
  });
  codesOptions.value = options;
};
// 处理项目选择事件的函数
const handleProjectSelect = (value: number) => {
  const selectedProject = projectMap.value[value];
  if (selectedProject && selectedProject.businessStructure) {
    orderInfo.value.projectId = selectedProject.id; // 项目ID
    orderInfo.value.projectName = selectedProject.projectName; // 项目名称
    orderInfo.value.projectCode = selectedProject.projectCode; // 项目编号

    orderInfo.value.executorCompanyName = selectedProject.executorCompanyName; // 贸易执行企业名称
    orderInfo.value.executorCompanyCode = selectedProject.executorCompanyCode; // 贸易执行企业代码
    orderInfo.value.businessManagerId = selectedProject.businessManagerId; // 业务负责人ID
    orderInfo.value.businessManagerName = selectedProject.businessManagerName; // 业务负责人名称
    orderInfo.value.operationManagerId = selectedProject.operationManagerId; // 运营负责人ID
    orderInfo.value.operationManagerName = selectedProject.operationManagerName; // 运营负责人名称

    orderInfo.value.businessStructure = selectedProject.businessStructure; // 业务结构
    orderInfo.value.projectModel = selectedProject.projectModel; // 项目模式 (建材模式, 产业模式等)
    const projectPartners = selectedProject.projectPartners || []; // 合作企业
    companyOptions.value = projectPartners
      .filter((item) => item.partnerType === '1')
      .map((v) => ({ label: v.companyName, value: v.id, code: v.companyCode }));
  }
  loadCodesOptions();
};
// 处理关联订单选择事件的函数
const handleCodesSelect = async (value: number) => {
  const selectedCode = codesOptions.value.find((option) => option.value === value);
  if (selectedCode) {
    const record = selectedCode.record as OrderInfo;
    orderInfo.value.salesOrderCode = record.orderCode;
    orderInfo.value.salesOrderName = record.orderName;
    // 选择关联订单后，查询订单详情接口获取商品信息传给商品表格
    const itemRecord = await infoSalesListApi({ id: record.id });
    // 去掉id字段
    const processedData = itemRecord.salesOrderItemVOS
      ? itemRecord.salesOrderItemVOS.map(({ id: _id, ...rest }) => rest)
      : [];
    productGridRef.value.setProductData(processedData);
  }
};
const handleCompanySelect = (value: string, record: { code: string; label: string }) => {
  orderInfo.value.supplierCompanyCode = record.code;
  orderInfo.value.supplierCompanyName = record.label;
};

const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  const productRecord = productGridRef.value?.getProductData();
  orderInfo.value.purchaseOrderItemRequests = productRecord.items;
  orderInfo.value.totalAmountWithTax = productRecord.totalAmountWithTax;
  orderInfo.value.totalTaxAmount = productRecord.totalTaxAmount;
  orderInfo.value.totalAmountWithoutTax = productRecord.totalAmountWithoutTax;
  changeOkLoading(true);
  let api = addPurchaseListApi;
  if (orderInfo.value.id) {
    api = editPurchaseListApi;
  }
  try {
    const res = await api(orderInfo.value as OrderInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <!-- <template #centerToolbar>
      <a-button>22</a-button>
    </template> -->
    <a-form
      ref="formRef"
      :colon="false"
      :model="orderInfo"
      :rules="rules"
      :label-col="{ style: { width: '150px' } }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="采购订单编号" name="purchaseOrderCode">
            <a-input v-model:value="orderInfo.purchaseOrderCode" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="采购订单名称" name="purchaseOrderName">
            <a-input v-model:value="orderInfo.purchaseOrderName" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目名称" name="projectName">
            <a-select v-model:value="orderInfo.projectName" :options="projectOptions" @select="handleProjectSelect" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="所属项目编号" name="projectCode">
            <a-input v-model:value="orderInfo.projectCode" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务结构" name="businessStructure">
            <a-input v-model:value="orderInfo.businessStructure" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目模式" name="projectModel">
            <a-input v-model:value="orderInfo.projectModel" />
          </a-form-item>
        </a-col>

        <a-col v-bind="colSpan" v-if="orderInfo.businessStructure === '先销后采'">
          <a-form-item label="关联销售订单" name="salesOrderId">
            <a-select v-model:value="orderInfo.salesOrderId" :options="codesOptions" @select="handleCodesSelect" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="orderInfo.businessStructure === '先销后采'">
          <a-form-item label="销售订单编号" name="salesOrderCode">
            <a-input v-model:value="orderInfo.salesOrderCode" />
          </a-form-item>
        </a-col>

        <a-col v-bind="colSpan">
          <a-form-item label="贸易执行企业" name="executorCompanyName">
            <a-input v-model:value="orderInfo.executorCompanyName" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="上游企业" name="supplierCompanyName">
            <a-select
              v-model:value="orderInfo.supplierCompanyName"
              :options="companyOptions"
              @select="handleCompanySelect"
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务负责人" name="businessManagerName">
            <a-input v-model:value="orderInfo.businessManagerName" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="运营负责人" name="operationManagerName">
            <a-input v-model:value="orderInfo.operationManagerName" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="业务日期" name="businessDate">
            <a-date-picker v-model:value="orderInfo.businessDate" value-format="YYYY-MM-DD" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="预计结束日期" name="estimatedEndDate">
            <a-date-picker v-model:value="orderInfo.estimatedEndDate" value-format="YYYY-MM-DD" class="w-full" />
          </a-form-item>
        </a-col>

        <a-col v-bind="colSpan" v-if="orderInfo.businessStructure === '先销后采'">
          <a-form-item label="预付款比例(%)" name="prepaymentRatio">
            <a-input-number v-model:value="orderInfo.prepaymentRatio" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="orderInfo.businessStructure === '先销后采'">
          <a-form-item name="prepaymentAmount">
            <template #label>
              预付款金额
              <VbenTooltip title="默认预付款金额=采购订单总金额*预付款比例">
                <VbenIcon icon="ant-design:question-circle-outlined" class="mr-1 cursor-pointer text-base" />
              </VbenTooltip>
            </template>
            <a-input-number v-model:value="orderInfo.prepaymentAmount" class="w-full" />
          </a-form-item>
        </a-col>

        <a-col v-bind="colSpan" v-if="orderInfo.businessStructure === '先采后销'">
          <a-form-item name="depositAmount">
            <template #label>
              保证金金额
              <VbenTooltip title="默认保证金金额=销售订单总金额*保证金比例">
                <VbenIcon icon="ant-design:question-circle-outlined" class="mr-1 cursor-pointer text-base" />
              </VbenTooltip>
            </template>
            <a-input-number v-model:value="orderInfo.depositAmount" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="orderInfo.businessStructure === '先采后销'">
          <a-form-item label="垫资比例(%)" name="advanceRatio">
            <a-input-number v-model:value="orderInfo.advanceRatio" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan" v-if="orderInfo.businessStructure === '先采后销'">
          <a-form-item label="账期（天)" name="paymentTermDays">
            <a-input-number v-model:value="orderInfo.paymentTermDays" class="w-full" />
          </a-form-item>
        </a-col>

        <a-col :span="24">
          <a-form-item label="备注" name="remarks">
            <a-textarea v-model:value="orderInfo.remarks" :rows="4" />
          </a-form-item>
        </a-col>
      </a-row>

      <!-- 商品信息 -->
      <BasicCaption content="商品信息" />
      <ProductInfo ref="productGridRef" :business-type="orderInfo.businessStructure || ''" />
    </a-form>
  </BasicPopup>
</template>
