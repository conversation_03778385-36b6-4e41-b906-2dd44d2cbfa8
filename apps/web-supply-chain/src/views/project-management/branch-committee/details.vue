<script setup lang="ts">
import {computed, ref, reactive} from 'vue';
import type { Rule } from 'ant-design-vue/es/form';

import {
  type BranchBaseInfo,
  type ProjectPartners,
  projectBranchEditApi,
  projectBranchDetailApi
} from '#/api';
import type { VxeTableGridOptions, GridApi } from '#/adapter/vxe-table';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { PlusOutlined } from '@ant-design/icons-vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import {defaultsDeep} from '@vben/utils';

import { type UploadChangeParam, type UploadProps} from 'ant-design-vue';

import {
  Button,
  Col,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Row,
  Select,
  Textarea,
  Upload,
} from 'ant-design-vue';

const emit = defineEmits(['register', 'ok']);

const businessStructureOptions = ref([
  {
    label: '先采后销',
    value: '1',
  },
  {
    label: '先销后采',
    value: '2',
  },
]);
const projectModelOptions = ref([
  {
    label: '建材模式',
    value: '1',
  },
  {
    label: '产业模式',
    value: '2',
  },
]);
const executorCompanyOptions = ref([
  {
    label: '上海负责人',
    value: '1',
  },
  {
    label: '北京负责人',
    value: '2',
  }
]);
const isDepositOptions = ref([
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '2',
  }
]);
const purchaseModeOptions = ref([
  {
    label: '预付款',
    value: '1',
  },
  {
    label: '货到付款',
    value: '2',
  },
  {
    label: '货到票开付款',
    value: '3',
  }
]);
const isGoodsControlModeOptions = ref([
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '2',
  }
]);
const { getDictList } = useDictStore();
// 根据接口定义初始化产品信息
const defaultForm: Partial<BranchBaseInfo> = {
  id: undefined,
  createTime: "",
  createBy: 0,
  updateTime: "",
  updateBy: 0,
  version: 0,
  projectCode: "",
  projectName: "",
  executorCompanyCode: "",
  executorCompanyName: "",
  businessManagerId: "",
  businessManagerName: "",
  businessStructure: "",
  projectModel: "",
  purchaseMode: "",
  isGoodsControlMode: 0,
  paymentTermDays: 0,
  businessDate: "",
  estimatedEndDate: "",
  expectedProjectScale: 0,
  serviceFeeRate: 0,
  projectAddress: "",
  remarks: "",
  isDeposit: 0,
  mortgageInfoDesc: "",
  pledgeInfoDesc: "",
  guaranteeInfoDesc: "",
  decisionMaterialsDesc: "",
  status: "",
  approvalStatus: "",
  projectPartners: [
    {
      id: undefined,
      createBy: 0,
      createTime: "",
      updateBy: 0,
      updateTime: "",
      version: 0,
      projectId: 0,
      partnerType: "",
      companyCode: "",
      companyName: ""
    }
  ],
  attachmentFiles: [
    {
      id: undefined,
      createBy: 0,
      createTime: "",
      updateBy: 0,
      updateTime: "",
      bizId: 0,
      bizType: "",
      category: "",
      folderId: 0,
      fileName: "",
      fileId: 0,
      fileType: "",
      fileSize: 0
    }
  ]
};

const detailForm = reactive<Partial<BranchBaseInfo>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  // projectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  // businessStructure: [{ required: true, message: '请选择业务结构', trigger: 'change' }],
  // projectModel: [{ required: true, message: '请选择项目模式', trigger: 'change' }],
  // executorCompanyName: [{ required: true, message: '请输入贸易执行企业', trigger: 'change' }],
  // businessManagerName: [{ required: true, message: '请选择业务负责人', trigger: 'change' }],
  // supplierCompanyName: [{ required: true, message: '请选择上游企业', trigger: 'change' }],
  // purchaserCompanyName: [{ required: true, message: '请选择下游企业', trigger: 'change' }],
  // creditCompanyName: [{ required: true, message: '请选择终端企业', trigger: 'change' }],
  // projectAddress: [{ required: true, message: '请输入项目地点', trigger: 'change' }],
  // isDeposit: [{ required: true, message: '请选择是否有保证金', trigger: 'change' }],
  // purchaseMode: [{ required: true, message: '请选择采购模式', trigger: 'change' }],
  // isGoodsControlMode: [{ required: true, message: '请选择是否是控货模式', trigger: 'change' }],
  // serviceFeeRate: [{ required: true, message: '请输入服务费率', trigger: 'change' }],
  // paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'change' }],
};
const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});
const init = async (data: BranchBaseInfo) => {
  if(data.reviewNodeId){
    const res: Partial<BranchBaseInfo> = await projectBranchDetailApi(data.reviewNodeId);

    // 深度复制确保响应性
    Object.keys(res).forEach((key: string ) => {
      detailForm[key] = res[key];
    });

    // 强制校验并转换projectPartners字段
    if (!Array.isArray(res.projectPartners) || res.projectPartners === null) {
      detailForm.projectPartners = [];
    } else {
      // 创建全新数组实例确保响应性
      detailForm.projectPartners = [...res.projectPartners];
    }

    // 强制刷新表格
    if (gridApiSupplier?.grid) {
      gridApiSupplier.grid.reloadData(detailForm.projectPartners.filter(item => item.partnerType === '1'));
    }
    if (gridApiPurchaser?.grid) {
      gridApiPurchaser.grid.reloadData(detailForm.projectPartners.filter(item => item.partnerType === '2'));
    }
    if (gridApiCredit?.grid) {
      gridApiCredit.grid.reloadData(detailForm.projectPartners.filter(item => item.partnerType === '3'));
    }
    if (gridApiQuota?.grid) {
      gridApiQuota.grid.reloadData(detailForm.projectPartners.filter(item => item.partnerType === '4'));
    }
  } else {
    Object.assign(detailForm, defaultsDeep(defaultForm));
    detailForm.projectPartners = defaultForm.projectPartners ? [...defaultForm.projectPartners] : [];
  }
};
const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  changeOkLoading(true);

  try {
    // 获取各表格数据并打标签
    const supplierData = gridApiSupplier.grid.getTableData().tableData.map(row => ({
      ...row,
      partnerType: '1', // 标记为上游企业
    }));
    const purchaserData = gridApiPurchaser.grid.getTableData().tableData.map(row => ({
      ...row,
      partnerType: '2', // 标记为下游企业
    }));
    const creditData = gridApiCredit.grid.getTableData().tableData.map(row => ({
      ...row,
      partnerType: '3', // 标记为终端企业
    }));
    const quotaData = gridApiQuota.grid.getTableData().tableData.map(row => ({
      ...row,
      partnerType: '4', // 标记为额度测算
    }));

    // 合并所有数据到 detailForm.projectPartners
    detailForm.projectPartners = [
      ...supplierData,
      ...purchaserData,
      ...creditData,
      ...quotaData,
    ];

    // 调用接口提交
    const res = await projectBranchEditApi(detailForm as BranchBaseInfo);

    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const labelCol = { style: { width: '150px' } };

const accountList = ref<ProjectPartners[]>([]);

// 新增初始化方法
const setAccountData = (data: ProjectPartners[]) => {
  accountList.value = data;
  if (gridApi.grid) {
    gridApi.grid.reloadData(accountList.value);
  }
};

const gridOptions: VxeTableGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};

const gridOptionsSupplier: VxeTableGridOptions = {
  ...gridOptions,
  editRules: {
    companyName: [{ required: true, message: '请输入企业名称' }],
    productName: [{ required: true, message: '请输入统一社会信用代码' }],
  },
  columns: [{
    children: [
      { type: 'checkbox', width: '60px', fixed: 'left' },
      { field: 'companyName', title: '企业名称', editRender: {}, slots: { edit: 'edit_product_type' }, minWidth: '160px' },
      { field: 'productName', title: '统一社会信用代码', editRender: {}, slots: { edit: 'edit_product_name' }, minWidth: '160px' }
    ]
  }],
  data: ref<ProjectPartners[]>([])
};

const gridOptionsPurchaser: VxeTableGridOptions = {
  ...gridOptions,
  editRules: {
    companyName: [{ required: true, message: '请输入企业名称' }],
    productName: [{ required: true, message: '请输入统一社会信用代码' }],
  },
  columns: [{
    children: [
      { type: 'checkbox', width: '60px', fixed: 'left' },
      { field: 'companyName', title: '企业名称', editRender: {}, slots: { edit: 'edit_product_type' }, minWidth: '160px' },
      { field: 'productName', title: '统一社会信用代码', editRender: {}, slots: { edit: 'edit_product_name' }, minWidth: '160px' }
    ]
  }],
  data: ref<ProjectPartners[]>([])
};

const gridOptionsCredit: VxeTableGridOptions = {
  ...gridOptions,
  editRules: {
    companyName: [{ required: true, message: '请输入企业名称' }],
    productName: [{ required: true, message: '请输入统一社会信用代码' }],
    availableQuota: [{ required: true, message: '请输入企业额度上限' }],
  },
  columns: [{
    children: [
      { type: 'checkbox', width: '60px', fixed: 'left' },
      { field: 'companyName', title: '企业名称', editRender: {}, slots: { edit: 'edit_product_type' }, minWidth: '160px' },
      { field: 'productName', title: '统一社会信用代码', editRender: {}, slots: { edit: 'edit_product_name' }, minWidth: '160px' },
      { field: 'availableQuota', title: '企业额度上限（元）', editRender: {}, slots: { edit: 'edit_available_quota' }, minWidth: '160px' },
      { field: 'creditExpireDate', title: '额度到日期', editRender: {}, slots: { edit: 'edit_credit_expire_date' }, minWidth: '160px' },
      { field: 'creditType', title: '授信类型', editRender: {}, slots: { edit: 'edit_credit_type' }, minWidth: '160px' }
    ]
  }],
  data: ref<ProjectPartners[]>([])
};

const gridOptionsQuota: VxeTableGridOptions = {
  ...gridOptions,
  editRules: {
    companyName: [{ required: true, message: '请输入企业名称' }],
    saleAmount: [{ required: true, message: '请输入测算分值' }],
  },
  columns: [{
    children: [
      { type: 'checkbox', width: '60px', fixed: 'left' },
      { field: 'companyName', title: '企业名称', editRender: {}, slots: { edit: 'edit_product_type' }, minWidth: '160px' },
      { field: 'productName', title: '统一社会信用代码', editRender: {}, slots: { edit: 'edit_product_name' }, minWidth: '160px' },
      { field: 'saleAmount', title: '测算分值', editRender: {}, slots: { edit: 'edit_sale_amount' }, minWidth: '160px' },
      {
        field: 'saleAmount',
        title: '操作',
        editRender: {},
        slots: { edit: 'edit_operation_buttons' },
        minWidth: '160px'
      }
    ]
  }],
  data: ref<ProjectPartners[]>([])
};

// 新增行
const addAccount = async (gridApi: GridApi) => {
  const record = {};
  const $grid = gridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};

// 删除行
const removeAccount = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (!Array.isArray(selectRecords)) {
      message.warning('请选择要删除的数据');
      return;
    }

    if (selectRecords.length > 0) {
      const { deleteProjectPartners } = detailForm;

      // 确保 deleteProjectPartners 是数组
      if (!Array.isArray(deleteProjectPartners)) {
        detailForm.deleteProjectPartners = [...selectRecords];
      } else {
        detailForm.deleteProjectPartners = [...deleteProjectPartners, ...selectRecords];
      }

      $grid.remove(selectRecords);
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

const [GridSupplier, gridApiSupplier] = useVbenVxeGrid({
  gridOptions: gridOptionsSupplier,
  tableTitle: '上游企业',
  watch: {
    data: {
      handler: (newVal) => {
        if (gridApiSupplier?.grid && newVal) {
          gridApiSupplier.grid.reloadData(newVal);
        }
      },
      deep: true,
      immediate: true
    }
  }
});
const [GridPurchaser, gridApiPurchaser] = useVbenVxeGrid({
  gridOptions: gridOptionsPurchaser,
  tableTitle: '下游企业',
  watch: {
    data: {
      handler: (newVal) => {
        if (gridApiPurchaser?.grid && newVal) {
          gridApiPurchaser.grid.reloadData(newVal);
        }
      },
      deep: true,
      immediate: true
    }
  }
});
const [GridCredit, gridApiCredit] = useVbenVxeGrid({
  gridOptions: gridOptionsCredit,
  tableTitle: '终端企业',
  watch: {
    data: {
      handler: (newVal) => {
        if (gridApiCredit?.grid && newVal) {
          gridApiCredit.grid.reloadData(newVal);
        }
      },
      deep: true,
      immediate: true
    }
  }
});
const [GridQuota, gridApiQuota] = useVbenVxeGrid({
  gridOptions: gridOptionsQuota,
  tableTitle: '额度测算',
  watch: {
    data: {
      handler: (newVal) => {
        if (gridApiQuota?.grid && newVal) {
          gridApiQuota.grid.reloadData(newVal);
        }
      },
      deep: true,
      immediate: true
    }
  }
});

const addNewCalculations = (row: any) => {
  console.log(111)
};

const viewDetails = (row: any) => {
  console.log(222)
};

function getBase64(img: Blob, callback: (base64Url: string) => void) {
  const reader = new FileReader();
  reader.addEventListener('load', () => callback(reader.result as string));
  reader.readAsDataURL(img);
}

const fileList = ref([]);
const loading = ref<boolean>(false);
const imageUrl = ref<string>('');

const handleChange = (info: UploadChangeParam) => {
  if (info.file.status === 'uploading') {
    loading.value = true;
    return;
  }
  if (info.file.status === 'done') {
    // Get this url from response in real world.
    getBase64(info.file.originFileObj, (base64Url: string) => {
      imageUrl.value = base64Url;
      loading.value = false;
    });
  }
  if (info.file.status === 'error') {
    loading.value = false;
    message.error('上传失败');
  }
};

const beforeUpload = (file: UploadProps['fileList'][number]) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('只能上传 JPG/PNG 文件!');
    return false;
  }
  const isLt2M = file.size / 1024 / 1024 < 2;
  if (!isLt2M) {
    message.error('图片必须小于 2MB!');
    return false;
  }
  return true;
};

// 暴露方法给父组件
defineExpose({
  getAccountData() {
    const $grid = gridApi.grid;
    if ($grid) {
      const { visibleData } = gridApi.grid.getTableData();
      return visibleData;
    } else {
      return [];
    }
  },
  setAccountData,
});

</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <BasicCaption content="基本信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="项目编号" name="projectCode">
            <Input v-model:value="detailForm.projectCode" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目名称" name="projectName">
            <Input v-model:value="detailForm.projectName"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务结构" name="businessStructure">
            <!--            <Select v-model:value="detailForm.businessStructure" :options="getDictList('businessStructure')" />-->
            <Select v-model:value="detailForm.businessStructure" :options="businessStructureOptions" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目模式" name="projectModel">
            <!--            <Select v-model:value="detailForm.projectModel" :options="getDictList('projectModel')" />-->
            <Select v-model:value="detailForm.projectModel" :options="projectModelOptions" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="贸易执行企业" name="executorCompanyName">
            <!--            <Select v-model:value="detailForm.executorCompanyName" :options="getDictList('executorCompanyName')" />-->
            <Select v-model:value="detailForm.executorCompanyName" :options="executorCompanyOptions" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务负责人" name="businessManagerName">
            <!--            <Select v-model:value="detailForm.businessManagerName" :options="getDictList('businessManagerName')" multiple />-->
            <Select v-model:value="detailForm.businessManagerName" :options="executorCompanyOptions" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否有保证金" name="isDeposit">
            <!--            <Select v-model:value="detailForm.isDeposit" :options="getDictList('isDeposit')" />-->
            <Select v-model:value="detailForm.isDeposit" :options="isDepositOptions" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="采购模式" name="purchaseMode">
            <!--            <Select v-model:value="detailForm.purchaseMode" :options="getDictList('purchaseMode')" multiple />-->
            <Select v-model:value="detailForm.purchaseMode" :options="purchaseModeOptions" multiple />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否是控货模式" name="isGoodsControlMode">
            <!--            <Select v-model:value="detailForm.isGoodsControlMode" :options="getDictList('isGoodsControlMode')" />-->
            <Select v-model:value="detailForm.isGoodsControlMode" :options="isGoodsControlModeOptions" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="合作费率（年%）" name="serviceFeeRate">
            <InputNumber v-model:value="detailForm.serviceFeeRate" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="账期（天)" name="paymentTermDays">
            <InputNumber v-model:value="detailForm.paymentTermDays"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目总额度(元)" name="expectedProjectScale">
            <InputNumber v-model:value="detailForm.expectedProjectScale"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="预计结束日期" name="estimatedEndDate">
            <ADatePicker v-model:value="detailForm.estimatedEndDate"/>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目地点" name="projectAddress">
            <Row>
              <Col v-bind="colSpan">
                <Select v-model:value="detailForm.projectAddress" :options="getDictList('projectAddress')" />
              </Col>
              <Col v-bind="colSpan">
                <Input v-model:value="detailForm.projectAddress" />
              </Col>
            </Row>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="担保企业" name="isGoodsControlMode">
            <!--            <Select v-model:value="detailForm.isGoodsControlMode" :options="getDictList('isGoodsControlMode')" />-->
            <Select v-model:value="detailForm.isGoodsControlMode" :options="isGoodsControlModeOptions" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="付款方式" name="isGoodsControlMode">
            <Select v-model:value="detailForm.isGoodsControlMode" :options="getDictList('isGoodsControlMode')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="回款方式" name="isGoodsControlMode">
            <Select v-model:value="detailForm.isGoodsControlMode" :options="getDictList('isGoodsControlMode')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否支持重点产业链" name="isGoodsControlMode">
            <Select v-model:value="detailForm.isGoodsControlMode" :options="getDictList('isGoodsControlMode')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否支持实体企业" name="isGoodsControlMode">
            <Select v-model:value="detailForm.isGoodsControlMode" :options="getDictList('isGoodsControlMode')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="备注" name="remarks">
            <Textarea v-model:value="detailForm.remarks" :rows="3" />
          </FormItem>
        </Col>
      </Row>
      <!-- 关联企业及敞口信息 -->
      <BasicCaption content="关联企业及敞口信息" />
      <div>
        <GridSupplier>
          <template #toolbarTools>
            <Button class="mr-2" type="primary" @click="() => addAccount(gridApiSupplier)">增行</Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiSupplier)">删行</Button>
          </template>
          <template #edit_product_type="{ row }">
            <Input v-model:value="row.companyName" placeholder="请输入企业名称" />
          </template>
          <template #edit_product_name="{ row }">
            <Input v-model:value="row.productName" placeholder="请输入统一社会信用代码" />
          </template>
        </GridSupplier>
      </div>
      <div>
        <GridPurchaser>
          <template #toolbarTools>
            <Button class="mr-2" type="primary" @click="() => addAccount(gridApiPurchaser)">增行</Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiPurchaser)">删行</Button>
          </template>
          <template #edit_product_type="{ row }">
            <Input v-model:value="row.companyName" placeholder="请输入企业名称" />
          </template>
          <template #edit_product_name="{ row }">
            <Input v-model:value="row.productName" placeholder="请输入统一社会信用代码" />
          </template>
        </GridPurchaser>
      </div>
      <div>
        <GridCredit>
          <template #toolbarTools>
            <Button class="mr-2" type="primary" @click="() => addAccount(gridApiCredit)">增行</Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiCredit)">删行</Button>
          </template>
          <template #edit_product_type="{ row }">
            <Input v-model:value="row.companyName" placeholder="请输入企业名称" />
          </template>
          <template #edit_product_name="{ row }">
            <Input v-model:value="row.productName" placeholder="请输入统一社会信用代码" />
          </template>
          <template #edit_available_quota="{ row }">
            <InputNumber v-model:value="row.availableQuota" placeholder="请输入企业额度上限" class="w-full" />
          </template>
          <template #edit_credit_expire_date="{ row }">
            <ADatePicker v-model:value="row.creditExpireDate" placeholder="选择额度到期日" />
          </template>
          <template #edit_credit_type="{ row }">
            <Select v-model:value="row.creditType" :options="getDictList('creditType')" />
          </template>
        </GridCredit>
      </div>
      <div>
        <GridQuota>
          <template #toolbarTools>
            <Button class="mr-2" type="primary" @click="() => addAccount(gridApiQuota)">增行</Button>
            <Button class="mr-2" danger @click="() => removeAccount(gridApiQuota)">删行</Button>
          </template>
          <template #edit_product_type="{ row }">
            <Input v-model:value="row.companyName" placeholder="请输入企业名称" />
          </template>
          <template #edit_purchase_amount="{ row }">
            <InputNumber v-model:value="row.purchaseAmount" placeholder="请输入采购金额" class="w-full" />
          </template>
          <template #edit_sale_amount="{ row }">
            <InputNumber v-model:value="row.saleAmount" placeholder="请输入销售金额" class="w-full" />
          </template>
          <template #edit_available_quota="{ row }">
            <InputNumber v-model:value="row.availableQuota" placeholder="请输入可用额度" class="w-full" />
          </template>
          <template #edit_remarks="{ row }">
            <Input v-model:value="row.remarks" placeholder="请输入备注" />
          </template>
          <template #edit_operation_buttons="{ row }">
            <div class="flex space-x-2">
              <Button size="small" type="link" @click="addNewCalculations(row)">
                新增测算
              </Button>
              <Button size="small" type="link" @click="viewDetails(row)">
                查看详情
              </Button>
            </div>
          </template>
        </GridQuota>
      </div>

      <!-- 高级设置 -->
      <BasicCaption content="增信措施" />
      <div class="mt-5">
        <Row :gutter="16">
          <Col :span="12">
            <FormItem label="抵押物信息附件">
              <Upload
                v-model:file-list="fileList"
                name="avatar"
                list-type="picture-card"
                class="avatar-uploader"
                :show-upload-list="true"
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                :before-upload="beforeUpload"
                @change="handleChange"
              >
                <div v-if="!imageUrl && !loading" class="ant-upload ant-upload-select-picture-card">
                  <plus-outlined></plus-outlined>
                  <div class="ant-upload-text">上传</div>
                </div>
              </Upload>
            </FormItem>
            <FormItem label="抵押物信息描述">
              <Textarea v-model:value="detailForm.mortgageInfoDesc" :rows="3" placeholder="请输入抵押物详细信息" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="质押物信息附件">
              <Upload
                v-model:file-list="fileList"
                name="avatar"
                list-type="picture-card"
                class="avatar-uploader"
                :show-upload-list="true"
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                :before-upload="beforeUpload"
                @change="handleChange"
              >
                <div v-if="!imageUrl && !loading" class="ant-upload ant-upload-select-picture-card">
                  <plus-outlined></plus-outlined>
                  <div class="ant-upload-text">上传</div>
                </div>
              </Upload>
            </FormItem>
            <FormItem label="质押物信息描述">
              <Textarea v-model:value="detailForm.pledgeInfoDesc" :rows="3" placeholder="请输入质押物详细信息" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="担保信息附件">
              <Upload
                v-model:file-list="fileList"
                name="avatar"
                list-type="picture-card"
                class="avatar-uploader"
                :show-upload-list="true"
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                :before-upload="beforeUpload"
                @change="handleChange"
              >
                <div v-if="!imageUrl && !loading" class="ant-upload ant-upload-select-picture-card">
                  <plus-outlined></plus-outlined>
                  <div class="ant-upload-text">上传</div>
                </div>
              </Upload>
            </FormItem>
            <FormItem label="担保信息描述">
              <Textarea v-model:value="detailForm.guaranteeInfoDesc" :rows="3" placeholder="请输入担保信息详细内容" />
            </FormItem>
          </Col>
          <Col :span="12">
            <FormItem label="决策资料">
              <Upload
                v-model:file-list="fileList"
                name="avatar"
                list-type="picture-card"
                class="avatar-uploader"
                :show-upload-list="true"
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                :before-upload="beforeUpload"
                @change="handleChange"
              >
                <div v-if="!imageUrl && !loading" class="ant-upload ant-upload-select-picture-card">
                  <plus-outlined></plus-outlined>
                  <div class="ant-upload-text">上传</div>
                </div>
              </Upload>
            </FormItem>
            <FormItem label="决策资料描述">
              <Textarea v-model:value="detailForm.decisionMaterialsDesc" :rows="3" placeholder="请输入决策资料详细内容" />
            </FormItem>
            <FormItem label="用印资料">
              <Upload
                v-model:file-list="fileList"
                name="avatar"
                list-type="picture-card"
                class="avatar-uploader"
                :show-upload-list="true"
                action="https://www.mocky.io/v2/5cc8019d300000980a055e76"
                :before-upload="beforeUpload"
                @change="handleChange"
              >
                <div v-if="!imageUrl && !loading" class="ant-upload ant-upload-select-picture-card">
                  <plus-outlined></plus-outlined>
                  <div class="ant-upload-text">上传</div>
                </div>
              </Upload>
            </FormItem>
          </Col>
        </Row>
      </div>
    </Form>
  </BasicPopup>
</template>

<style scoped>
.avatar-uploader > .ant-upload {
  width: 128px;
  height: 128px;
}
.ant-upload-select-picture-card i {
  font-size: 32px;
  color: #999;
}

.ant-upload-select-picture-card .ant-upload-text {
  margin-top: 8px;
  color: #666;
}

:where(.css-dev-only-do-not-override-1gaak89).ant-input-number{
  width: 100%;
}
</style>
