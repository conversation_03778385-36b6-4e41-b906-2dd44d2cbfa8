<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';
import { VbenIcon } from '@vben-core/shadcn-ui';

import { projectReviewPageApi, projectProposalDeleteApi, projectProposalCancelApi } from '#/api';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { Button, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import Create from './create.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    // {
    //   component: 'Select',
    //   fieldName: 'supplierCompanyName',
    //   label: '上游企业',
    //   componentProps: {
    //     options: dictStore.getDictList('supplier_company'), // 补全字典类型
    //   },
    // },
    // {
    //   component: 'Select',
    //   fieldName: 'purchaserCompanyName',
    //   label: '下游企业',
    //   componentProps: {
    //     options: dictStore.getDictList('purchaser_company'), // 补全字典类型
    //   },
    // },
    {
      component: 'Select',
      fieldName: 'executorCompanyName',
      label: '贸易执行企业',
      componentProps: {
        options: dictStore.getDictList('executor_company_name'), // 补全字典类型
      },
    },
    {
      component: 'Select',
      fieldName: 'businessStructure',
      label: '业务结构',
      componentProps: {
        options: dictStore.getDictList('business_structure'), // 补全字典类型
      },
    },
    {
      component: 'Select',
      fieldName: 'projectModel',
      label: '项目模式',
      componentProps: {
        options: dictStore.getDictList('project_model'), // 补全字典类型
      },
    },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('approval_status'), // 补全字典类型
      },
    },
    {
      component: 'Select',
      fieldName: 'purchaseMode',
      label: '采购模式',
      componentProps: {
        options: dictStore.getDictList('purchase_mode'), // 补全字典类型
      },
    },
  ],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: '', title: '项目评审编号'},
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    { field: 'businessStructure', title: '业务结构' },
    { field: 'projectModel', title: '项目模式' },
    // { field: 'supplierCompanyName', title: '上游企业' },
    // { field: 'purchaserCompanyName', title: '下游企业' },
    { field: 'executorCompanyName', title: '贸易执行企业' },
    {
      field: 'purchaseMode',
      title: '采购模式',
      // formatter: ({ cellValue }) => {
      //   return dictStore.getDictLabel('project_status', cellValue); // 使用字典转换
      // }
    },
    {
      field: 'approvalStatus',
      title: '审批状态',
      // formatter: ({ cellValue }) => {
      //   return dictStore.getDictLabel('approval_status', cellValue); // 使用字典转换
      // }
    },
    { field: 'businessManagerName', title: '业务负责人' },
    { field: 'businessDate', title: '业务日期' },
    { field: 'approvalStatus', title: '审批状态' },
    { field: 'projectCode', title: '项目状态' },
    { field: 'createTime', title: '创建时间' },
    { field: 'createBy', title: '创建人' },
    // { field: 'createDeptName', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await projectReviewPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const add = () => {
  openFormPopup(true, {});
};

// 定义正确的类型
interface ProjectItem {
  id: number;
  projectCode: string;
  projectName: string;
  businessStructure: string;
  projectModel: string;
  supplierCompanyName: string;
  purchaserCompanyName: string;
  executorCompanyName: string;
  status: string; // 修复字段名
  approvalStatus: string;
  businessManagerName: string;
  businessDate: string;
  createTime: string;
  createBy: string;
  createDeptName: string;
}

const edit = (row: ProjectItem) => {
  openFormPopup(true, row);
};

const details = (row: ProjectItem) => {
  openFormPopup(true, row);
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();

const del = async (row: ProjectItem) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      try {
        await projectProposalDeleteApi(row.id);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch (error) {
        message.error('删除失败: ' + error.message);
      }
    },
  });
};

const cancel = async (row: ProjectItem) => {
  AntdModal.confirm({
    title: $t('base.confirmCancelTitle'),
    content: $t('base.confirmCancelContent'),
    async onOk() {
      try {
        await projectProposalCancelApi(row.id);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch (error) {
        message.error('作废失败: ' + error.message);
      }
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="details(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="cancel(row)">
            作废
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
