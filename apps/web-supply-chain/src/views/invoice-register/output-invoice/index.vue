<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { type OutputInvoice, outputInvoicePageApi } from '#/api';

import Details from './details.vue';

const dictStore = useDictStore();

const isHcOptions = [
  {
    label: '是',
    value: 1,
  },
  {
    label: '否',
    value: 0,
  },
];

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'buyerCompanyName',
      label: '购买方公司名称',
    },
    {
      component: 'Input',
      fieldName: 'sellerCompanyName',
      label: '销售方公司名称',
    },
    {
      component: 'Select',
      fieldName: 'invoiceType',
      label: '发票类型',
      // componentProps: {
      //   options: dictStore.getDictList('supplier_company'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'invoiceCode',
      label: '发票代码',
    },
    {
      component: 'Input',
      fieldName: 'invoiceNumber',
      label: '发票号码',
    },
    {
      component: 'Select',
      fieldName: 'isHc',
      label: '红冲状态',
      componentProps: {
        options: isHcOptions
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'invoiceDate',
      label: '开票实际日期',
    },
  ],
  fieldMappingTime: [
    ['invoiceDate', ['invoiceStartDate', 'invoiceEndDate'], 'YYYY-MM-DD'],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    // { field: '', title: '销项税票记录编号'},
    { field: 'sellerCompanyName', title: '销售方' },
    { field: 'buyerCompanyName', title: '采购方' },
    { field: 'invoiceType', title: '发票类型' },
    { field: 'invoiceNumber', title: '发票号码' },
    { field: 'invoiceCode', title: '发票代码' },
    // { field: '', title: '发票属性' },
    { field: 'totalAmountTax', title: '含税总金额' },
    { field: 'totalAmount', title: '不含税总金额' },
    // { field: '', title: '发票价税合计' },
    // { field: '', title: '业务类型' },
    // { field: '', title: '审批状态' },
    // { field: '', title: '业务状态' },
    { field: 'isHc', title: '红冲状态', formatter: (value) => { return value == 1 ? '是' : value == 0 ? '否' : '' } },
    // { field: '', title: '关联蓝字发票号码' },
    // { field: '', title: '申请开票日期' },
    // { field: '', title: '实际开票日期' },
    // { field: '', title: '开票申请编号' },
    // { field: '', title: '所属项目编号' },
    // { field: '', title: '所属项目名称' },
    { field: 'createTime', title: '创建时间' },
    { field: 'createBy', title: '创建人' },
    // { field: '', title: '创建部门' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await outputInvoicePageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// // 创建
// const add = () => {
//   openFormPopup(true, {});
// };

// 查看
const handleDetail = (row: OutputInvoice) => {
  openFormPopup(true, row);
  console.log('查看', row);
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};

</script>

<template>
  <Page auto-content-height>
    <Grid>
<!--      <template #toolbar-actions>-->
<!--        <Button class="mr-2" type="primary" @click="add">-->
<!--          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />-->
<!--          {{ $t('base.add') }}-->
<!--        </Button>-->
<!--      </template>-->
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="handleDetail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Details @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
