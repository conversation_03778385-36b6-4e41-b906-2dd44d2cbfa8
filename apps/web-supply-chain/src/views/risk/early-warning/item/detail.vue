<script setup lang="ts">
import type { EarlyWarningItemInfo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { StatusTag } from '@vben/base-ui';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';

const emit = defineEmits(['ok']);

const state = reactive({
  pageType: 'edit',
});

const pageTitle = computed(() => {
  return state.pageType === 'detail' ? '预警详情' : '预警处置';
});
const createDefaultWarningItem = () => {
  return {
    id: 0,
    ruleId: 0,
    alertCode: '',
    alertRelationId: '',
    alertRelationName: '',
    ruleName: '',
    ruleDescription: '',
    projectId: '',
    projectCode: '',
    projectName: '',
    companyCode: '',
    companyName: '',
    ruleLevel: '',
    status: '',
    disposalStatus: '',
    deleteFlag: 0,
    createTime: 0,
    triggerReason: '',
    triggerCondition: '',
  };
};
const warningItemForm = ref<EarlyWarningItemInfo>(createDefaultWarningItem());
const init = ({ data, pageType }: { data: EarlyWarningItemInfo; pageType: 'detail' | 'edit' }) => {
  state.pageType = pageType;
  warningItemForm.value = data ?? {};
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const save = async () => {
  changeOkLoading(true);
  try {
    // await api(unref(ruleForm) as EarlyWarningRuleInfo);
    message.success('保存成功');
    closePopup();
    emit('ok');
  } finally {
    changeOkLoading(false);
  }
};
const { getDictList } = useDictStore();
const gridOptions = {
  showOverflow: true,
  keepSource: true,
  columns: [
    {
      field: 'seq',
      type: 'seq',
      width: 80,
    },
    { field: 'createTime', title: '触发时间', formatter: 'formatDateTime', width: 180 },
    { field: 'projectName', title: '触发记录' },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const handleForm = ref({});
</script>

<template>
  <BasicPopup
    v-bind="$attrs"
    :show-ok-btn="state.pageType !== 'detail'"
    :title="pageTitle"
    @register="registerPopup"
    @ok="save"
  >
    <div class="mx-4">
      <BasicCaption content="预警规则信息" />
      <a-descriptions bordered :column="2" class="mt-4" size="small">
        <a-descriptions-item label="预警规则名称">
          {{ warningItemForm.ruleName }}
        </a-descriptions-item>
        <a-descriptions-item label="预警记录编号">
          {{ warningItemForm.alertCode }}
        </a-descriptions-item>
        <a-descriptions-item label="风险等级">
          <StatusTag code="earlyWarningLevel" :value="warningItemForm.ruleLevel" />
        </a-descriptions-item>
        <a-descriptions-item label="关联业务编号">
          {{ warningItemForm.alertRelationId }}
        </a-descriptions-item>
        <a-descriptions-item label="关联业务">
          {{ warningItemForm.alertRelationName }}
        </a-descriptions-item>
        <a-descriptions-item label="预警企业名称">
          {{ warningItemForm.companyName }}
        </a-descriptions-item>
        <a-descriptions-item label="触发原因" :span="2">
          {{ warningItemForm.triggerReason }}
        </a-descriptions-item>
        <a-descriptions-item label="预警规则描述" :span="2">
          {{ warningItemForm.ruleDescription }}
        </a-descriptions-item>
        <a-descriptions-item label="预警触发条件" :span="2">
          {{ warningItemForm.triggerCondition }}
        </a-descriptions-item>
      </a-descriptions>
      <BasicCaption content="预警触发记录" />
      <Grid />
      <BasicCaption content="预警处置" />
      <a-form v-if="state.pageType === 'edit'" class="mt-4">
        <a-row :gutter="24">
          <a-col :span="12">
            <a-form-item label="处置措施" name="disposalStatus">
              <a-select
                v-model:value="handleForm.disposalStatus"
                :options="getDictList('earlyWarningDisposalStatus')"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="处置人" name="disposalUser">
              <a-input v-model:value="handleForm.disposalUser" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="处置说明" name="disposalDescription">
              <a-textarea v-model:value="handleForm.disposalDescription" :rows="4" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-descriptions v-else bordered :column="2" class="mt-4" size="small">
        <a-descriptions-item label="处置措施">
          {{ warningItemForm.ruleName }}
        </a-descriptions-item>
        <a-descriptions-item label="处置人">
          {{ warningItemForm.alertCode }}
        </a-descriptions-item>
        <a-descriptions-item label="处置说明" :span="2">
          {{ warningItemForm.ruleDescription }}
        </a-descriptions-item>
      </a-descriptions>
    </div>
  </BasicPopup>
</template>

<style scoped></style>
