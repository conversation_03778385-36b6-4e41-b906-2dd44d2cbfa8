<script setup lang="ts">
import type { EarlyWarningRuleInfo } from '#/api';

import { ref, unref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { FeUserSelect } from '@vben/fe-ui';
import { useDictStore } from '@vben/stores';

import { message } from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import {
  addEarlyWarningRuleApi,
  editEarlyWarningRuleApi,
  getUserInfoByIdsApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
} from '#/api';

const { getDictList } = useDictStore();
const ruleFormRef = ref();
const ruleForm = ref<Partial<EarlyWarningRuleInfo>>({});

const modalTitle = ref('告警规则配置');
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {
    const data = modalApi.getData() ?? {};
    ruleForm.value = defaultsDeep(data, {});
  },
  onConfirm: async () => {
    await ruleFormRef.value.validate();
    let api = addEarlyWarningRuleApi;
    if (ruleForm.value.id) {
      api = editEarlyWarningRuleApi;
    }
    try {
      modalApi.lock();
      await api(unref(ruleForm) as EarlyWarningRuleInfo);
      message.success('保存成功');
      await modalApi.close();
    } finally {
      modalApi.unlock();
    }
  },
  onBeforeClose: () => {
    ruleForm.value = {};
    ruleFormRef.value.resetFields();
    return true;
  },
});

const ruleRules = {
  triggerConditionValue: [{ required: true, message: '请输入预警触发参数' }],
  ruleLevel: [{ required: true, message: '请选择预警等级', trigger: 'change' }],
};
</script>

<template>
  <Modal :title="modalTitle">
    <a-form
      ref="ruleFormRef"
      :model="ruleForm"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      :colon="false"
      :rules="ruleRules"
    >
      <a-form-item label="预警触发参数" name="triggerConditionValue">
        <a-input v-model:value="ruleForm.triggerConditionValue">
          <template #addonBefore> 提前 </template>
          <template #addonAfter> 天 </template>
        </a-input>
      </a-form-item>
      <a-form-item label="预警等级" name="ruleLevel">
        <a-select v-model:value="ruleForm.ruleLevel" :options="getDictList('earlyWarningLevel')" />
      </a-form-item>
      <a-form-item label="信息接收人" name="">
        <FeUserSelect
          v-model:value="ruleForm.adminList"
          multiple
          :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi }"
        />
      </a-form-item>
      <a-form-item label="短信提醒" name="">
        <a-switch v-model:checked="ruleForm.enable" :checked-value="1" :un-checked-value="0" />
      </a-form-item>
    </a-form>
  </Modal>
</template>

<style></style>
