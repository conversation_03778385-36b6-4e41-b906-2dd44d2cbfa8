<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

// import type { FactoringProduct } from '#/api';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import {
  Col,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Radio,
  RadioGroup,
  Row,
  Select,
  Textarea,
} from 'ant-design-vue';

// import { addProductApi, editProductApi, infoProductApi } from '#/api';

const emit = defineEmits(['register', 'ok']);
const { getDictList } = useDictStore();
// 根据接口定义初始化产品信息
const defaultForm: Partial<FactoringProduct> = {
  productName: '',
  productCode: '',
  repaymentType: '',
  factoringType: '',
  factoringMode: undefined,
  factoringDirection: undefined,
  factoringAutoRights: undefined,
  rightOfRecourse: undefined,
  repaymentPeriodsMin: 0,
  repaymentPeriodsMax: 0,
  annualizedInterestRateMin: 0,
  annualizedInterestRateMax: 0,
  financingRatioMin: 0,
  financingRatioMax: 0,
  productDesc: '',
  signConfig: undefined,
  repaymentMethod: '',
  riskCoefficientMin: 0,
  riskCoefficientMax: 0,
};
const productInfo = ref<Partial<FactoringProduct>>(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
  productType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  repaymentType: [{ required: true, message: '请选择还款方', trigger: 'change' }],
  factoringType: [{ required: true, message: '请选择保理类型', trigger: 'change' }],
  factoringMode: [{ required: true, message: '请选择支持操作模式', trigger: 'change' }],
  factoringDirection: [{ required: true, message: '请选择支持保理方向', trigger: 'change' }],
  factoringAutoRights: [{ required: true, message: '请选择是否脱核', trigger: 'change' }],
  rightOfRecourse: [{ required: true, message: '请选择追索权要求', trigger: 'change' }],
  signConfig: [{ required: true, message: '请选择签署协议配置（默认）', trigger: 'change' }],
  isGracePeriod: [{ required: true, message: '请选择是否有宽限期', trigger: 'change' }],
  isInterestAdd: [{ required: true, message: '请选择是否加息', trigger: 'change' }],
  interestAddMethod: [{ required: true, message: '请选择加息方式', trigger: 'change' }],
  penaltyInterestType: [{ required: true, message: '请选择罚息类型', trigger: 'change' }],
  gracePeriodMin: [{ required: true, message: '请输融资宽限期最小值', trigger: 'blur' }],
  gracePeriodMax: [{ required: true, message: '请输融资宽限期最大值', trigger: 'blur' }],
  annualizedInterestRateMin: [{ required: true, message: '请输入年化利率最小值', trigger: 'blur' }],
  annualizedInterestRateMax: [{ required: true, message: '请输入年化利率最大值', trigger: 'blur' }],
  financingRatioMin: [{ required: true, message: '请输入融资比例最小值', trigger: 'blur' }],
  financingRatioMax: [{ required: true, message: '请输入融资比例最大值', trigger: 'blur' }],
  interestAddRateMin: [{ required: true, message: '请输入固定加息率最小值', trigger: 'blur' }],
  interestAddRateMax: [{ required: true, message: '请输入固定加息率最大值', trigger: 'blur' }],
  productDesc: [{ required: false, message: '请输入产品介绍', trigger: 'blur' }],
  repaymentMethod: [{ required: true, message: '请输入还款方式', trigger: 'blur' }],
};
const title = computed(() => {
  return productInfo.value.id ? '编辑产品' : '新增产品';
});
const init = async (data: FactoringProduct) => {
  if (data.id) {
    productInfo.value = await infoProductApi({ id: data.id as string });
  }
};
const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  changeOkLoading(true);
  let api = addProductApi;
  if (productInfo.value.id) {
    api = editProductApi;
  }
  try {
    const res = await api(productInfo.value as FactoringProduct);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const labelCol = { style: { width: '150px' } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="productInfo"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基础信息 -->
      <BasicCaption content="基础信息" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="产品名称" name="productName">
            <Input v-model:value="productInfo.productName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="产品编码" name="productCode">
            <Input v-model:value="productInfo.productCode" disabled />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务类型" name="productType">
            <Select v-model:value="productInfo.productType" :options="getDictList('productType')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="还款方" name="repaymentType">
            <Select v-model:value="productInfo.repaymentType" :options="getDictList('repaymentType')" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="保理类型" name="factoringType">
            <RadioGroup v-model:value="productInfo.factoringType">
              <Radio v-for="item in getDictList('factoringType')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="支持操作模式" name="factoringMode">
            <RadioGroup v-model:value="productInfo.factoringMode">
              <Radio v-for="item in getDictList('factoringMode')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="支持保理方向" name="factoringDirection">
            <RadioGroup v-model:value="productInfo.factoringDirection">
              <Radio v-for="item in getDictList('factoringDirection')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否脱核" name="factoringAutoRights">
            <RadioGroup v-model:value="productInfo.factoringAutoRights">
              <Radio v-for="item in getDictList('yesNo')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="追索权要求" name="rightOfRecourse">
            <RadioGroup v-model:value="productInfo.rightOfRecourse">
              <Radio v-for="item in getDictList('yesNo')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="签署协议配置（默认）" name="signConfig">
            <RadioGroup v-model:value="productInfo.signConfig">
              <Radio v-for="item in getDictList('signConfigType')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="产品介绍" />
      <Row class="mt-5">
        <Col :span="24">
          <FormItem :label-col="{ span: 0 }" :wrapper-col="{ span: 24 }" name="productDesc">
            <Textarea v-model:value="productInfo.productDesc" :rows="4" />
          </FormItem>
        </Col>
      </Row>
      <!-- 高级设置 -->
      <BasicCaption content="产品方案设计" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="还款方式" name="repaymentMethod">
            <Input v-model:value="productInfo.repaymentMethod" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="还款期数" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="repaymentPeriodsMin" class="mr-2 w-1/2">
                <InputNumber v-model:value="productInfo.repaymentPeriodsMin" :controls="false" class="w-full" />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="repaymentPeriodsMax" class="ml-2 w-1/2">
                <InputNumber v-model:value="productInfo.repaymentPeriodsMax" :controls="false" class="w-full" />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="年化利率（%）" required class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="annualizedInterestRateMin" class="mr-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.annualizedInterestRateMin"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="annualizedInterestRateMax" class="ml-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.annualizedInterestRateMax"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="融资比例（%）" required class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="financingRatioMin" class="mr-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.financingRatioMin"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="financingRatioMax" class="ml-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.financingRatioMax"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="手续费率（%）" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="serviceFeeFixedMin" class="mr-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.serviceFeeFixedMin"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="serviceFeeFixedMax" class="ml-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.serviceFeeFixedMax"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="固定手续费（元）" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="serviceFeeMin" class="mr-2 w-1/2">
                <InputNumber v-model:value="productInfo.serviceFeeMin" :controls="false" class="w-full" />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="serviceFeeMax" class="ml-2 w-1/2">
                <InputNumber v-model:value="productInfo.serviceFeeMax" :controls="false" class="w-full" />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="是否有宽限期" name="isGracePeriod">
            <RadioGroup v-model:value="productInfo.isGracePeriod">
              <Radio v-for="item in getDictList('isGracePeriod')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="宽限期类型" name="gracePeriodType">
            <RadioGroup v-model:value="productInfo.gracePeriodType">
              <Radio v-for="item in getDictList('gracePeriodType')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="融资宽限期（天）" required class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="gracePeriodMin" class="mr-2 w-1/2">
                <InputNumber v-model:value="productInfo.gracePeriodMin" :controls="false" class="w-full" />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="gracePeriodMax" class="ml-2 w-1/2">
                <InputNumber v-model:value="productInfo.gracePeriodMax" :controls="false" class="w-full" />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="宽限期类型" name="isInterestAdd">
            <RadioGroup v-model:value="productInfo.isInterestAdd">
              <Radio v-for="item in getDictList('isInterestAdd')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="加息天数（天）" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="interestAddDaysMin" class="mr-2 w-1/2">
                <InputNumber v-model:value="productInfo.interestAddDaysMin" :controls="false" class="w-full" />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="interestAddDaysMax" class="ml-2 w-1/2">
                <InputNumber v-model:value="productInfo.interestAddDaysMax" :controls="false" class="w-full" />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="加息方式" name="interestAddMethod">
            <RadioGroup v-model:value="productInfo.interestAddMethod">
              <Radio v-for="item in getDictList('interestAddMethod')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="固定加息率（%）" required class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="interestAddRateMin" class="mr-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.interestAddRateMin"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="interestAddRateMax" class="ml-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.interestAddRateMax"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="罚息类型" name="penaltyInterestType">
            <RadioGroup v-model:value="productInfo.penaltyInterestType">
              <Radio v-for="item in getDictList('penaltyInterestType')" :key="item.value" :value="item.value">
                {{ item.label }}
              </Radio>
            </RadioGroup>
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="风险系数" class="mb-0">
            <div class="flex items-center">
              <FormItem :label-col="{ span: 0 }" name="riskCoefficientMin" class="mr-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.riskCoefficientMin"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
              <FormItem :label-col="{ span: 0 }">~</FormItem>
              <FormItem :label-col="{ span: 0 }" name="riskCoefficientMax" class="ml-2 w-1/2">
                <InputNumber
                  v-model:value="productInfo.riskCoefficientMax"
                  :controls="false"
                  :precision="2"
                  class="w-full"
                />
              </FormItem>
            </div>
          </FormItem>
        </Col>
      </Row>
    </Form>
  </BasicPopup>
</template>

<style scoped></style>
