import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface QueryCondition extends BaseDataParams {
  // 项目编码
  projectCode: string;
  // 项目名称
  projectName: string;
  // 业务结构(先采后销, 先销后采)
  businessStructure: string;
  // 项目模式(建材模式, 产业模式)
  projectModel: string;
  // 采购模式(预付款, 货到付款)
  purchaseMode: string;
  // 当前页
  current: string;
  // 每页的数量
  size: string;
  // 正排序规则
  ascs: string;
  // 倒排序规则
  descs: string;
  // 主键
  id: string;
  // 创建时间
  createTime: string;
  // 创建人
  createBy: string;
  // 更新时间
  updateTime: string;
  // 更新人
  updateBy: string;
  // 版本号
  version: string;
  // 贸易执行企业编码
  executorCompanyCode: string;
  // 贸易执行企业名称
  executorCompanyName: string;
  // 业务负责人ID
  businessManagerId: string;
  // 业务负责人名称
  businessManagerName: string;
  // 是否货权管控模式 (1:是, 0:否)
  isGoodsControlMode: string;
  // 账期天数（天)
  paymentTermDays: string;
  // 业务日期起
  businessStartDate: string;
  // 业务日期止
  businessEndDate: string;
  // 预计结束日期
  estimatedEndDate: string;
  // 预期项目规模(元)
  expectedProjectScale: string;
  // 服务费率（年/%)
  serviceFeeRate: string;
  // 项目地点
  projectAddress: string;
  // 项目备注
  remarks: string;
  // 是否有保证金 (1:是, 0:否)
  isDeposit: string;
  // 抵押信息描述
  mortgageInfoDesc: string;
  // 质押信息描述
  pledgeInfoDesc: string;
  // 担保信息描述
  guaranteeInfoDesc: string;
  // 决策材料描述
  decisionMaterialsDesc: string;
  // 操作状态
  status: string;
  // 审批状态
  approvalStatus: string;
  // 上游企业
  suppliers: string;
  // 下游企业
  purchasers: string;
}

export interface InitiationBaseInfo extends BaseDataParams {
  // 主键
  id: number;
  // 创建时间
  createTime: string;
  // 创建人ID
  createBy: number;
  // 修改时间
  updateTime: string;
  // 修改人ID
  updateBy: number;
  // 版本号
  version: number;
  // 项目编码
  projectCode: string;
  // 项目名称
  projectName: string;
  // 贸易执行企业编码
  executorCompanyCode: string;
  // 贸易执行企业名称
  executorCompanyName: string;
  // 业务负责人ID
  businessManagerId: string;
  // 业务负责人名称
  businessManagerName: string;
  // 业务结构(先采后销, 先销后采)
  businessStructure: string;
  // 项目模式(建材模式, 产业模式)
  projectModel: string;
  // 采购模式(预付款, 货到付款)
  purchaseMode: string;
  // 是否货权管控模式 (1:是, 0:否)
  isGoodsControlMode: number;
  // 账期天数（天)
  paymentTermDays: number;
  // 业务日期
  businessDate: string;
  // 预计结束日期
  estimatedEndDate: string;
  // 预期项目规模(元)
  expectedProjectScale: number; // double
  // 服务费率（年/%)
  serviceFeeRate: number; // double
  // 项目地点
  projectAddress: string;
  // 项目备注
  remarks: string;
  // 是否有保证金 (1:是, 0:否)
  isDeposit: number;
  // 抵押信息描述
  mortgageInfoDesc: string;
  // 质押信息描述
  pledgeInfoDesc: string;
  // 担保信息描述
  guaranteeInfoDesc: string;
  // 决策材料描述
  decisionMaterialsDesc: string;
  // 操作状态
  status: string;
  // 审批状态
  approvalStatus: string;
  projectPartners: ProjectPartners[];
  deleteProjectPartners: ProjectPartners[];
  attachmentFiles: AttachmentFiles[];
}

export interface ProjectPartners {
  // 主键
  id: number | undefined;
  // 创建人
  createBy: number;
  // 创建时间
  createTime: string;
  // 更新人
  updateBy: number;
  // 更新时间
  updateTime: string;
  // 版本号
  version: number;
  // 关联的项目ID
  projectId: number;
  // 合作方类型
  partnerType: string;
  // 企业编码
  companyCode: string;
  // 企业名称
  companyName: string;
}

export interface AttachmentFiles {
  // 主键
  id: number | undefined;
  // 创建人
  createBy: number;
  // 创建时间
  createTime: string;
  // 更新人
  updateBy: number;
  // 更新时间
  updateTime: string;
  // 业务ID (例如：项目ID)
  bizId: number;
  // 业务类型 (例如：PROJECT:项目)
  bizType: string;
  // 附件类别
  category: string;
  // 文件夹ID
  folderId: number;
  // 文件名称
  fileName: string;
  // 文件ID
  fileId: number;
  // 文件类型(如: image/jpeg)
  fileType: string;
  // 文件大小(字节)
  fileSize: number;
}

// 项目分页查询
export async function projectProposalPageApi(params: PageListParams) {
  return requestClient.get<QueryCondition[]>('/scm/project/proposal/page', { params });
}

// 创建项目信息
export async function projectProposalAddApi(data: InitiationBaseInfo) {
  return requestClient.post<InitiationBaseInfo>('/scm/project/proposal/add', data);
}

// 编辑项目信息
export async function projectProposalEditApi(data: InitiationBaseInfo) {
  return requestClient.post<InitiationBaseInfo>('/scm/project/proposal/edit', data);
}

// 项目详情查看
export async function projectProposalDetailApi(id: number) {
  return requestClient.get<InitiationBaseInfo>(`/scm/project/proposal/detail/${id}`);
}

// 项目信息删除
export async function projectProposalDeleteApi(id: number) {
  return requestClient.post(`/scm/project/proposal/delete/${id}`);
}

// 项目信息作废
export async function projectProposalCancelApi(id: number) {
  return requestClient.post(`/scm/project/proposal/cancel/${id}`);
}

// 项目信息提交
export async function projectProposalSubmitApi(id: number) {
  return requestClient.post(`/scm/project/proposal/submit/${id}`);
}

// 项目条件查询
export async function projectProposalListApi(params: PageListParams) {
  return requestClient.get<QueryCondition[]>('/scm/project/proposal/list', { params });
}
