import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface projectInfo extends BaseDataParams {
  id: number | string;
  createBy: number;
  createTime: string;
  updateBy: number;
  updateTime: string;
  deleteFlag: number;
  version: number;
  projectCode: string;
  projectName: string;
  supplierCompanyId: string;
  supplierCompanyName: string;
  purchaserCompanyId: string;
  purchaserCompanyName: string;
  creditCompanyId: string;
  creditCompanyName: string;
  executorCompanyId: string;
  executorCompanyName: string;
  businessManagerId: string;
  businessManagerName: string;
  businessStructure: string;
  projectModel: string;
  purchaseMode: string;
  isGoodsControlMode: number;
  paymentTermDays: number;
  businessDate: string;
  estimatedEndDate: string;
  expectedProjectScale: number;
  serviceFeeRate: number;
  projectAddress: string;
  remarks: string;
  isDeposit: number;
  mortgageInfoDesc: string;
  pledgeInfoDesc: string;
  guaranteeInfoDesc: string;
  decisionMaterialsDesc: string;
  status: string;
  approvalStatus: string;
  supplierCompanyIdList: string[];
  purchaserCompanyIdList: string[];
  creditCompanyIdList: string[];
  executorCompanyIdList: string[];
  businessManagerIdList: string[];
}

interface projectItemInfo extends BaseDataParams {
  id: number;
  createBy: number;
  createTime: string;
  updateBy: number;
  updateTime: string;
  deleteFlag: number;
  projectId: number;
  productType: string; // 用于标识企业类型
  productName: string;
  productCode: string;
  measureUnit: string;
  specifications: string;
  brandName: string;
  manufacturerName: string;
  quantity: number;
  taxRate: number;
  purchasePriceWithTax: number;
  purchaseAmountWithTax: number;
  purchasePriceWithoutTax: number;
  purchaseAmountWithoutTax: number;
  salePriceWithTax: number;
  saleAmountWithTax: number;
  salePriceWithoutTax: number;
  saleAmountWithoutTax: number;
  remarks: string;
}

export interface attachmentFileInfo extends BaseDataParams {
  id: number | string;
  createBy: number;
  createTime: string;
  updateBy: number;
  updateTime: string;
  deleteFlag: number;
  bizId: number;
  bizType: string;
  category: string;
  folderId: number;
  fileName: string;
  fileId: number;
  fileType: string;
  fileSize: number;
}

export interface InitiationBaseInfo extends BaseDataParams {
  project: projectInfo;
  projectItems: projectItemInfo[];
  attachmentFiles: attachmentFileInfo[];
}

// 项目分页查询
export async function getProjectPageApi(params: PageListParams) {
  return requestClient.get<InitiationBaseInfo[]>('/scm/project/manage/page', { params });
}

// 创建项目信息
export async function addProjectApi(data: InitiationBaseInfo) {
  return requestClient.post<InitiationBaseInfo>('/scm/project/manage/add', data);
}

// 编辑项目信息
export async function editProjectApi(data: InitiationBaseInfo) {
  return requestClient.post<InitiationBaseInfo>('/scm/project/manage/edit', data);
}

// 项目详情查看
export async function projectDetailApi(id: string) {
  return requestClient.get<InitiationBaseInfo>(`/scm/project/manage/detail/${id}`);
}

// 项目信息提交
export async function projectSubmitApi(id: string) {
  return requestClient.post(`/scm/project/manage/submit/${id}`);
}

// 项目信息删除
export async function projectDeleteApi(id: string) {
  return requestClient.post(`/scm/project/manage/delete/${id}`);
}

// 项目信息作废
export async function projectChangeApi(id: string) {
  return requestClient.post(`/scm/project/manage/change/${id}`);
}

