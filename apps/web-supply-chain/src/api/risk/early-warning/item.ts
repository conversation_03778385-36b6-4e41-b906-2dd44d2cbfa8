import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * 预警信息数据接口
 */
export interface EarlyWarningItemInfo {
  // 预警记录的唯一ID
  id: number;
  // 预警编号
  alertCode: string;
  // 关联业务对象ID
  alertRelationId: string;
  // 关联业务对象名称
  alertRelationName: string;
  // 触发预警的规则ID
  ruleId: number;
  // 规则名称
  ruleName: string;
  // 规则描述
  ruleDescription: string;
  // 规则级别
  ruleLevel: string;
  // 项目ID
  projectId: string;
  // 项目编号
  projectCode: string;
  // 项目名称
  projectName: string;
  // 公司编号
  companyCode: string;
  // 公司名称
  companyName: string;
  // 创建时间/预警时间（毫秒级时间戳）
  createTime: number;
  // 预警状态
  status: string;
  // 处理状态
  disposalStatus: string;
  // 删除标志（0：未删除，1：已删除）
  deleteFlag: number;
  // 触发原因
  triggerReason?: string;
  // 预警触发条件
  triggerCondition?: string;
}

export async function getEarlyWarningItemPageListApi(params: PageListParams) {
  return requestClient.get<EarlyWarningItemInfo[]>('/early-warning/item/page-list', { params });
}
