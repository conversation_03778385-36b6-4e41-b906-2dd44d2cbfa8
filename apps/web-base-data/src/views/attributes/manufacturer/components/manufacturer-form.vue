<script setup lang="ts">
import type { ManufacturerInfo } from '#/api';

import { ref } from 'vue';

import { defaultsDeep } from 'lodash-es';

const formModel = ref<Partial<ManufacturerInfo>>({
  manufacturerName: '',
  factoryNumber: '',
  remarks: '',
});

const rules = {
  manufacturerName: [{ required: true, message: '请输入生产厂家名称' }],
};

const formModelRef = ref();

const init = (data: Partial<ManufacturerInfo>) => {
  formModelRef.value.clearValidate();
  formModel.value = defaultsDeep(data, {});
};

const submit = () => {
  formModelRef.value.validate();
  return formModel.value;
};

defineExpose({ init, submit });
</script>

<template>
  <a-form ref="formModelRef" :model="formModel" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
    <a-form-item label="生产厂家名称" name="manufacturerName">
      <a-input v-model:value="formModel.manufacturerName" />
    </a-form-item>
    <a-form-item label="厂商" name="factoryNumber">
      <a-input v-model:value="formModel.factoryNumber" />
    </a-form-item>
    <a-form-item label="备注" name="remarks">
      <a-textarea v-model:value="formModel.remarks" :rows="4" />
    </a-form-item>
  </a-form>
</template>

<style></style>
