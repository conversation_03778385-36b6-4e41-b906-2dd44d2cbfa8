<script setup lang="ts">
import { h, ref, unref, watch } from 'vue';

import { DictSelect } from '@vben/base-ui';
import { useVbenModal } from '@vben/common-ui';

import { DeleteOutlined } from '@ant-design/icons-vue';
import { defaultsDeep } from 'lodash-es';

interface AttrInfo {
  attrName: string;
  formType: string;
  attrValueJson: string;
}

const attrFormRef = ref();
const attrForm = ref<AttrInfo>({
  attrName: '',
  formType: '',
  attrValueJson: '',
});
const attrValueList = ref<{ value: string }[]>([]);

watch(
  () => attrForm.value.attrValueJson,
  (newVal) => {
    if (newVal) {
      const valueList = newVal.split(',');
      attrValueList.value = valueList.map((item) => ({ value: item }));
    } else {
      attrValueList.value = [];
    }
  },
  { immediate: true },
);

watch(
  attrValueList,
  (newVal) => {
    const list = newVal.map((item) => item.value);
    attrForm.value.attrValueJson = list.join(',');
  },
  { deep: true },
);
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {
    const { data } = modalApi.getData();
    attrForm.value = defaultsDeep(data, {
      attrName: '',
      formType: '',
      attrValueJson: '',
    });
  },
  onConfirm: async () => {
    const { insert } = modalApi.getData();
    await attrFormRef.value.validate();
    await insert(unref(attrForm));
    await modalApi.close();
  },
  onBeforeClose: () => {
    attrForm.value = {
      attrName: '',
      formType: '',
      attrValueJson: '',
    };
    attrFormRef.value.resetFields();
    return true;
  },
});
const addLabel = () => {
  attrValueList.value.push({ value: '' });
};

const removeAttr = (index: number) => {
  attrValueList.value.splice(index, 1);
};
const attrRules = {
  attrName: [{ required: true, message: '属性名称' }],
  formType: [{ required: true, message: '表单方式' }],
};
</script>

<template>
  <Modal title="新增属性">
    <a-form
      ref="attrFormRef"
      :model="attrForm"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
      :colon="false"
      :rules="attrRules"
    >
      <a-form-item label="属性名称" name="attrName">
        <a-input v-model:value="attrForm.attrName" />
      </a-form-item>
      <a-form-item label="表单方式" name="formType">
        <DictSelect v-model:value="attrForm.formType" code="formType" @change="attrForm.attrValueJson = ''" />
      </a-form-item>
      <a-form-item v-if="attrForm.formType && attrForm.formType !== 'input'" label="属性值">
        <a-button @click="addLabel">添加</a-button>
        <a-input-group v-for="(item, index) in attrValueList" :key="index" class="mt-2" compact>
          <a-input v-model:value="item.value" style="width: calc(100% - 32px)" />
          <a-button
            danger
            :icon="h(DeleteOutlined)"
            :disabled="attrValueList.length === 1"
            @click="removeAttr(index)"
          />
        </a-input-group>
      </a-form-item>
    </a-form>
  </Modal>
</template>

<style></style>
