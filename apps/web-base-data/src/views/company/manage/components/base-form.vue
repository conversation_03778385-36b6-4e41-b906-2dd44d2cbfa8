<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { CompanyBankBO, CompanyInfo, CompanyInvoiceBO } from '#/api';

import { computed, ref } from 'vue';

import { BaseUpload } from '@vben/base-ui';
import { COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicCaption, FeUserSelect } from '@vben/fe-ui';
import { $t } from '@vben/locales';

import { Modal as AntdModal, Input, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, importMaterialApi } from '#/api';

const companyForm = defineModel<CompanyInfo>({ type: Object, required: true });
const rules = {
  companyName: [{ required: true, message: '请输入企业名称' }],
  companyCode: [{ required: true, message: '请输入统一社会信用代码' }],
  legalPersonName: [{ required: true, message: '请输入法人' }],
};
const colSpan = COL_SPAN_PROP;
const formProp = FORM_PROP;
const businessTerm = computed({
  get() {
    return [companyForm.value.fromTime, companyForm.value.toTime];
  },
  set(value) {
    companyForm.value.fromTime = value[0];
    companyForm.value.toTime = value[1];
  },
});
const init = (data: CompanyInfo) => {
  bankGridApi.grid.reloadData(data.companyBankList ?? []);
  invoiceGridApi.grid.reloadData(data.companyInvoiceList ?? []);
};
const handleBusinessTermChange = (val?: [Date, Date]) => {
  companyForm.value.fromTime = val ? val[0] : undefined;
  companyForm.value.toTime = val ? val[1] : undefined;
};
const baseGridOptions = {
  showOverflow: true,
  keepSource: true,
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const bankGridOptions = {
  data: companyForm.value.companyBankList,
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'accountName',
      title: '账户名称',
      slots: { default: 'edit_account_name' },
      minWidth: '160px',
    },
    {
      field: 'account',
      title: '银行账号',
      slots: { default: 'edit_account' },
      minWidth: '160px',
    },
    {
      field: 'bank',
      title: '开户银行',
      slots: { default: 'edit_bank' },
      minWidth: '160px',
    },
    {
      field: 'branchNumber',
      title: '开户行号',
      slots: { default: 'edit_branch_number' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '是否默认',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
  ],
  editRules: {
    accountName: [{ required: true, content: '请输入账户名称' }],
    account: [{ required: true, content: '请输入银行账号' }],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [BankGrid, bankGridApi] = useVbenVxeGrid({
  gridOptions: bankGridOptions,
});
const addBank = async () => {
  const record: CompanyBankBO = {};
  const { visibleData } = bankGridApi.grid.getTableData();
  if (visibleData.length === 0) {
    record.isDefault = 1;
  }
  const $grid = bankGridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};
const removeBank = async () => {
  const $grid = bankGridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      AntdModal.confirm({
        title: '确认删除',
        content: '确认删除此数据吗？',
        async onOk() {
          $grid.removeCheckboxRow();
          message.success($t('base.resSuccess'));
        },
      });
    } else {
      message.warning('请选择数据');
    }
  }
};
const handleBankIsDefaultChange = (_value: boolean, row: CompanyInvoiceBO) => {
  const { visibleData } = bankGridApi.grid.getTableData();
  visibleData.forEach((item) => {
    item.isDefault = 0;
  });
  row.isDefault = 1;
};

const invoiceGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'title', title: '抬头名称', slots: { default: 'edit_title' }, minWidth: '160px' },
    {
      field: 'taxNumber',
      title: '纳税人识别号',
      slots: { default: 'edit_tax_number' },
      minWidth: '160px',
    },
    {
      field: 'bank',
      title: '开户银行',
      slots: { default: 'edit_bank' },
      minWidth: '160px',
    },
    {
      field: 'account',
      title: '开户银行账号',
      slots: { default: 'edit_account' },
      minWidth: '160px',
    },
    {
      field: 'phone',
      title: '电话',
      slots: { default: 'edit_phone' },
      minWidth: '160px',
    },
    {
      field: 'address',
      title: '地址',
      slots: { default: 'edit_address' },
      minWidth: '160px',
    },
    {
      field: 'isDefault',
      title: '是否默认',
      slots: { default: 'edit_is_default' },
      minWidth: '160px',
      fixed: 'right',
    },
  ],
  editRules: {
    title: [{ required: true, content: '请输入抬头名称' }],
    taxNumber: [{ required: true, content: '请输入纳税人识别号' }],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [InvoiceGrid, invoiceGridApi] = useVbenVxeGrid({
  gridOptions: invoiceGridOptions,
});
const addInvoice = async () => {
  const record: CompanyInvoiceBO = {};
  const { visibleData } = invoiceGridApi.grid.getTableData();
  if (visibleData.length === 0) {
    record.isDefault = 1;
  }
  const $grid = invoiceGridApi.grid;
  if ($grid) {
    const { row } = await $grid.insertAt(record, -1);
    await $grid.setEditRow(row);
  }
};
const removeInvoice = async () => {
  const $grid = invoiceGridApi.grid;
  if ($grid) {
    const selectRecords = $grid.getCheckboxRecords();
    if (selectRecords.length > 0) {
      AntdModal.confirm({
        title: '确认删除',
        content: '确认删除此数据吗？',
        async onOk() {
          $grid.removeCheckboxRow();
          message.success($t('base.resSuccess'));
        },
      });
    } else {
      message.warning('请选择数据');
    }
  }
};
const handleInvoiceIsDefaultChange = (_value: boolean, row: CompanyInvoiceBO) => {
  const { visibleData } = invoiceGridApi.grid.getTableData();
  visibleData.forEach((item) => {
    item.isDefault = 0;
  });
  row.isDefault = 1;
};
const CompanyFormRef = ref();
const save = async () => {
  await CompanyFormRef.value.validate();
  const bankErrMap = await bankGridApi.grid.validate(true);
  if (bankErrMap) {
    const bankErrMessage = getCombinedErrorMessagesString(bankErrMap);
    if (bankErrMessage) {
      message.error(bankErrMessage);
      return new Error(bankErrMessage);
    }
  }
  const { visibleData: bankData } = bankGridApi.grid.getTableData();
  companyForm.value.companyBankList = bankData;
  const invoiceErrMap = await invoiceGridApi.grid.validate(true);
  if (invoiceErrMap) {
    const invoiceErrMessage = getCombinedErrorMessagesString(invoiceErrMap);
    if (invoiceErrMessage) {
      message.error(invoiceErrMessage);
      return new Error(invoiceErrMessage);
    }
  }
  const { visibleData: invoiceData } = invoiceGridApi.grid.getTableData();
  companyForm.value.companyInvoiceList = invoiceData;
  return companyForm.value;
  // console.error(bankErrMap, invoiceErrMap);
};
/**
 * 提取所有字段的错误信息，并格式化成一个单一的字符串。
 */
function getCombinedErrorMessagesString(
  errorObject: Record<string, any[]>,
  separator = '；',
  prefix = '以下字段存在问题：',
  defaultMessage = '未知错误，请重试。',
) {
  // 1. 确保 errorObject 存在且是对象
  if (!errorObject || typeof errorObject !== 'object' || Object.keys(errorObject).length === 0) {
    return defaultMessage;
  }

  const messages = [];

  // 2. 遍历 errorObject 的所有键（字段名）
  const fieldKeys = Object.keys(errorObject);

  for (const key of fieldKeys) {
    // 3. 使用可选链操作符安全地访问嵌套的 message
    // errorObject[key] 对应 accountName 或 phoneNumber 数组
    // [0] 取数组的第一个元素 (通常错误对象数组只有一个元素)
    // .rule 访问 rule 对象
    // .message 访问最终的错误消息
    const message = errorObject[key]?.[0]?.rule?.message;

    // 4. 如果找到了有效的 message，就添加到 messages 数组中
    if (message) {
      messages.push(message);
    }
  }

  // 5. 判断是否收集到任何错误信息
  return messages.length > 0 ? prefix + messages.join(separator) : defaultMessage;
}
defineExpose({ init, save });
</script>

<template>
  <a-form ref="CompanyFormRef" class="" :model="companyForm" :rules="rules" v-bind="formProp">
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="企业名称" name="companyName">
          <a-input v-model:value="companyForm.companyName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="统一社会信用代码" name="companyCode">
          <a-input v-model:value="companyForm.companyCode" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="注册资本" name="regCapital">
          <a-input v-model:value="companyForm.regCapital" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="实缴资本" name="actualCapital">
          <a-input v-model:value="companyForm.actualCapital" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="成立日期" name="establishTime">
          <a-date-picker v-model:value="companyForm.establishTime" value-format="x" class="w-full" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="经营状态" name="regStatus">
          <a-select v-model:value="companyForm.regStatus" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="工商注册号" name="regNumber">
          <a-input v-model:value="companyForm.regNumber" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="组织机构代码" name="orgNumber">
          <a-input v-model:value="companyForm.orgNumber" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="纳税人识别号" name="taxNumber">
          <a-input v-model:value="companyForm.taxNumber" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="纳税人资质" name="taxpayerQualification">
          <a-input v-model:value="companyForm.taxpayerQualification" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="公司类型" name="companyOrgType">
          <a-select v-model:value="companyForm.companyOrgType" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="营业期限" name="fromTime">
          <a-range-picker
            :value="businessTerm"
            class="w-full"
            value-format="x"
            @calendar-change="handleBusinessTermChange"
            @change="handleBusinessTermChange"
          />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="所属行业" name="industry">
          <a-input v-model:value="companyForm.industry" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="行政区划" name="district">
          <a-input v-model:value="companyForm.district" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="注册地址" name="regLocation">
          <a-input v-model:value="companyForm.regLocation" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="经营地址" name="bizLocation">
          <a-input v-model:value="companyForm.bizLocation" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="经营范围" name="bizScope" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
          <a-textarea v-model:value="companyForm.bizScope" :rows="4" class="w-full" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="营业执照" name="" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
          <BaseUpload :upload-api="importMaterialApi" />
        </a-form-item>
      </a-col>
      <a-col :span="24">
        <a-form-item label="其他备注" name="remark" :label-col="{ span: 2 }" :wrapper-col="{ span: 22 }">
          <a-textarea v-model:value="companyForm.remark" :rows="4" class="w-full" />
        </a-form-item>
      </a-col>
    </a-row>
    <BasicCaption content="法人代表信息" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="法定代表人" name="legalPersonName">
          <a-input v-model:value="companyForm.legalPersonName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="证件号码" name="legalPersonIdNo">
          <a-input v-model:value="companyForm.legalPersonIdNo" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="联系电话" name="legalPersonPhone">
          <a-input v-model:value="companyForm.legalPersonPhone" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan" />
      <a-col v-bind="colSpan">
        <a-form-item label="法人证件正面" name="legalPersonIdCardFront">
          <BaseUpload :upload-api="importMaterialApi" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="法人证件背面" name="legalPersonIdCardBack">
          <BaseUpload :upload-api="importMaterialApi" />
        </a-form-item>
      </a-col>
    </a-row>
    <BasicCaption content="企业联系人信息" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="联系人姓名" name="contactPersonName">
          <a-input v-model:value="companyForm.contactPersonName" />
        </a-form-item>
      </a-col>
      <a-col v-bind="colSpan">
        <a-form-item label="联系人电话" name="contactPersonPhone">
          <a-input v-model:value="companyForm.contactPersonPhone" />
        </a-form-item>
      </a-col>
    </a-row>
    <BasicCaption content="客户负责人" />
    <a-row class="mt-5">
      <a-col v-bind="colSpan">
        <a-form-item label="业务经理" name="">
          <FeUserSelect
            v-model:value="companyForm.managerList"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi }"
          />
        </a-form-item>
      </a-col>
    </a-row>
    <BasicCaption content="银行账户" />
    <BankGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="addBank">增行</a-button>
          <a-button danger @click="removeBank">删行</a-button>
        </a-space>
      </template>
      <template #edit_account_name="{ row }">
        <Input v-model:value="row.accountName" placeholder="请输入账户名称" />
      </template>
      <template #edit_account="{ row }">
        <Input v-model:value="row.account" placeholder="请输入银行账号" />
      </template>
      <template #edit_bank="{ row }">
        <Input v-model:value="row.bank" placeholder="请输入开户银行" />
      </template>
      <template #edit_branch_number="{ row }">
        <Input v-model:value="row.branchNumber" placeholder="请输入开户行号" />
      </template>
      <template #edit_is_default="{ row }">
        <a-checkable-tag :checked="!!row.isDefault" @change="(value: boolean) => handleBankIsDefaultChange(value, row)">
          默认
        </a-checkable-tag>
      </template>
    </BankGrid>
    <BasicCaption content="开票信息" />
    <InvoiceGrid>
      <template #toolbarTools>
        <a-space>
          <a-button type="primary" @click="addInvoice">增行</a-button>
          <a-button danger @click="removeInvoice">删行</a-button>
        </a-space>
      </template>
      <template #edit_title="{ row }">
        <Input v-model:value="row.title" placeholder="请输入抬头名称" />
      </template>
      <template #edit_tax_number="{ row }">
        <Input v-model:value="row.taxNumber" placeholder="请输入纳税人识别号" />
      </template>
      <template #edit_bank="{ row }">
        <Input v-model:value="row.bank" placeholder="请输入开户银行" />
      </template>
      <template #edit_account="{ row }">
        <Input v-model:value="row.account" placeholder="请输入开户银行账号" />
      </template>
      <template #edit_phone="{ row }">
        <Input v-model:value="row.phone" placeholder="请输入电话" />
      </template>
      <template #edit_address="{ row }">
        <Input v-model:value="row.address" placeholder="请输入开户地址" />
      </template>
      <template #edit_is_default="{ row }">
        <a-checkable-tag
          :checked="!!row.isDefault"
          @change="(value: boolean) => handleInvoiceIsDefaultChange(value, row)"
        >
          默认
        </a-checkable-tag>
      </template>
    </InvoiceGrid>
  </a-form>
</template>

<style scoped></style>
