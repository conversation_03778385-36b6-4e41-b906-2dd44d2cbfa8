<script setup lang="ts">
import type { CompanyInfo } from '#/api';

import { reactive, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getCompanyInfoApi } from '#/api';
import BaseDetail from '#/views/company/manage/components/base-detail.vue';

defineEmits(['register']);
const init = async (data: CompanyInfo) => {
  if (data?.id) {
    companyForm.value = await getCompanyInfoApi({ id: data.id as number });
    // BaseFormRef.value.init();
  }
};
const [registerPopup] = usePopupInner(init);

const companyForm = ref<CompanyInfo>({
  companyCode: '',
  companyName: '',
});
const state = reactive({
  activeKey: 'base',
});
</script>

<template>
  <BasicPopup v-bind="$attrs" title="企业信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-tabs v-model:active-key="state.activeKey">
        <a-tab-pane key="base" tab="主体信息">
          <BaseDetail :company-info="companyForm" />
        </a-tab-pane>
      </a-tabs>
    </div>
  </BasicPopup>
</template>

<style></style>
