<script setup lang="ts">
import type { BlackListAddInfo, CompanyInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicCaption, BasicPopup, usePopupInner } from '@vben/fe-ui';

import { AutoComplete, message } from 'ant-design-vue';

import { addBlackListApi, editBlackListApi, getBlackListDetailApi, getCompanyListApi } from '#/api';

const emit = defineEmits(['ok', 'register']);
const init = async (data: BlackListAddInfo) => {
  blacklistForm.value = data.id
    ? await getBlackListDetailApi({ id: data.id })
    : {
        ...data,
      };
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const AccessFormRef = ref();
const blacklistForm = ref<BlackListAddInfo>({});
const save = async () => {
  await AccessFormRef.value.validate();
  changeOkLoading(true);
  let api = addBlackListApi;
  if (blacklistForm.value.id) {
    api = editBlackListApi;
  }
  try {
    const res = await api(blacklistForm.value as BlackListAddInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const selectCompany = (_value: number, data: CompanyInfo) => {
  blacklistForm.value.companyCode = data.companyCode;
};
const rules = {
  companyName: [{ required: true, message: '请输入企业名称', trigger: 'change' }],
  companyCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'change' }],
  remark: [{ required: true, message: '请输入添加原因', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="黑名单信息" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <BasicCaption content="基本信息" />
      <a-form ref="AccessFormRef" class="mt-5" :model="blacklistForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="企业名称" name="companyName">
              <ApiComponent
                v-model="blacklistForm.companyName as unknown as string"
                :component="AutoComplete"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyName"
                model-prop-name="value"
                @select="selectCompany"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="统一社会信用代码" name="companyCode">
              <a-input v-model:value="blacklistForm.companyCode" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="添加原因" name="remark" v-bind="{ labelCol: { span: 3 }, wrapperCol: { span: 21 } }">
              <a-textarea v-model:value="blacklistForm.remark" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
