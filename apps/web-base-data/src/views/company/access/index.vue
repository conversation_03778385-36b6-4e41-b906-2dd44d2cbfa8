<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AccessInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent, Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message, Select } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  cancelAccessApi,
  deleteAccessApi,
  getAccessPageListApi,
  getBusinessRoleListApi,
  getCompanyListApi,
} from '#/api';
import AccessDetail from '#/views/company/access/access-detail.vue';
import AccessEdit from '#/views/company/access/access-edit.vue';

const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Input',
      fieldName: 'companyCode',
      label: '统一社会信用代码',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '企业状态',
      componentProps: {
        options: dictStore.getDictList('COMPANY_ACCESS_STATUS'),
      },
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'companyName', title: '企业名称', minWidth: 200 },
    { field: 'companyCode', title: '统一社会信用代码' },
    { field: 'accessRoleName', title: '准入角色', cellRender: { name: 'CellTag' } },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'COMPANY_ACCESS_STATUS',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
    { field: 'applyTime', title: '申请日期', formatter: 'formatDate' },
    { field: 'effectTime', title: '生效日期', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAccessPageListApi({
          ...formValues,
          current: page.currentPage,
          size: page.pageSize,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: AccessInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const access = (row: AccessInfo) => {
  openFormPopup(true, row);
};
const audit = (row: AccessInfo) => {
  const data = { ...row, pageType: 'audit' };
  openDetailPopup(true, data);
};
const viewDetail = (row: AccessInfo) => {
  openDetailPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const AccessFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await AccessFormRef.value.validate();
    await modalApi.close();
    openFormPopup(true, { ...accessForm.value });
  },
  onClosed: () => {
    AccessFormRef.value.resetFields();
    accessForm.value = {};
  },
});
const accessForm = ref<AccessInfo>({});
const rules = {
  companyId: [{ required: true, message: '请选择企业' }],
  accessRole: [{ required: true, message: '请选择业务角色' }],
};
const addAccount = () => {
  modalApi.open();
};
const cancel = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认取消准入',
    content: '此操作将取消该企业的准入，是否继续？',
    async onOk() {
      await cancelAccessApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const del = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该企业的准入信息，是否继续？',
    async onOk() {
      await deleteAccessApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="addAccount">新增准入</a-button>
          <a-button type="primary" danger @click="cancel">取消准入</a-button>
          <a-button type="primary" danger @click="del">删除</a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['EDITING'].includes(row.status)" @click="access(row)">编辑</a-typography-link>
          <a-typography-link v-if="['PENDING'].includes(row.status)" @click="audit(row)">审核</a-typography-link>
          <a-typography-link @click="viewDetail(row)">详情</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <AccessEdit @register="registerForm" @ok="editSuccess" />
    <AccessDetail @register="registerDetail" @ok="editSuccess" />
    <Modal title="新增准入">
      <a-form ref="AccessFormRef" :model="accessForm" :rules="rules">
        <a-form-item label="企业名称" name="companyId">
          <ApiComponent
            v-model="accessForm.companyId as unknown as string"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="id"
            model-prop-name="value"
          />
        </a-form-item>
        <a-form-item label="业务角色" name="accessRole">
          <ApiComponent
            v-model="accessForm.accessRole"
            :component="Select"
            :api="getBusinessRoleListApi"
            label-field="roleName"
            value-field="id"
            model-prop-name="value"
            :after-fetch="
              (res) => {
                return res.filter((item: any) => item.isAccess === 1);
              }
            "
          />
        </a-form-item>
      </a-form>
    </Modal>
  </Page>
</template>

<style></style>
