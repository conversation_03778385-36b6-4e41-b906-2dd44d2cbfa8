<script setup lang="ts">
import { DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';

defineProps({ companyInfo: { type: Object, default: () => ({}) } });
</script>

<template>
  <div>
    <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
      <a-descriptions-item label="企业名称">
        {{ companyInfo.companyName }}
      </a-descriptions-item>
      <a-descriptions-item label="统一社会信用代码">
        {{ companyInfo.companyCode }}
      </a-descriptions-item>
      <a-descriptions-item label="注册资本">
        {{ companyInfo.regCapital }}
      </a-descriptions-item>
      <a-descriptions-item label="实缴资本">
        {{ companyInfo.actualCapital }}
      </a-descriptions-item>
      <a-descriptions-item label="成立日期">
        {{ companyInfo.establishTime }}
      </a-descriptions-item>
      <a-descriptions-item label="经营状态">
        {{ companyInfo.regStatus }}
      </a-descriptions-item>
      <a-descriptions-item label="工商注册号">
        {{ companyInfo.regNumber }}
      </a-descriptions-item>
      <a-descriptions-item label="组织机构代码">
        {{ companyInfo.orgNumber }}
      </a-descriptions-item>
      <a-descriptions-item label="纳税人识别号">
        {{ companyInfo.taxNumber }}
      </a-descriptions-item>
      <a-descriptions-item label="纳税人资质">
        {{ companyInfo.taxpayerQualification }}
      </a-descriptions-item>
      <a-descriptions-item label="公司类型">
        {{ companyInfo.companyOrgType }}
      </a-descriptions-item>
      <a-descriptions-item label="营业期限">
        {{ companyInfo.fromTime }} - {{ companyInfo.toTime ?? '长期' }}
      </a-descriptions-item>
      <a-descriptions-item label="所属行业">
        {{ companyInfo.industry }}
      </a-descriptions-item>
      <a-descriptions-item label="行政区划">
        {{ companyInfo.district }}
      </a-descriptions-item>
      <a-descriptions-item label="注册地址">
        {{ companyInfo.regLocation }}
      </a-descriptions-item>
      <a-descriptions-item label="经营地址">
        {{ companyInfo.bizLocation }}
      </a-descriptions-item>
      <a-descriptions-item label="经营范围" :span="2">
        {{ companyInfo.bizScope }}
      </a-descriptions-item>
      <a-descriptions-item label="营业执照" :span="2" />
      <a-descriptions-item label="其他备注" :span="2">
        {{ companyInfo.remark }}
      </a-descriptions-item>
    </a-descriptions>
  </div>
</template>

<style scoped></style>
