<script setup lang="ts">
import { BasicCaption } from '@vben/fe-ui';
import {DESCRIPTIONS_PROP} from "@vben/constants";

defineProps({ companyInfo: { type: Object, default: () => ({}) } });
</script>

<template>
  <div>
    <BasicCaption content="法人代表信息" />
    <a-descriptions class="mt-4" v-bind="DESCRIPTIONS_PROP">
      <a-descriptions-item label="法定代表人">
        {{ companyInfo.legalPersonName }}
      </a-descriptions-item>
      <a-descriptions-item label="证件号码">
        {{ companyInfo.legalPersonIdNo }}
      </a-descriptions-item>
      <a-descriptions-item label="联系电话" :span="2">
        {{ companyInfo.legalPersonPhone }}
      </a-descriptions-item>
      <a-descriptions-item label="法人证件正面" />
      <a-descriptions-item label="法人证件背面" />
    </a-descriptions>
  </div>
</template>

<style></style>
