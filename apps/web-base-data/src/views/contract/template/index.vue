<script setup lang="ts">
import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AccessInfo, ContractTemplateInfo } from '#/api';

import { ref, unref } from 'vue';

import { BaseUpload } from '@vben/base-ui';
import { ApiComponent, Page, useVbenModal } from '@vben/common-ui';
import { FORM_PROP } from '@vben/constants';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Dropdown, Menu, MenuItem, message, Select, Tooltip } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addContractTemplateApi,
  copyContractTemplateApi,
  deleteContractTemplateApi,
  disableContractTemplateApi,
  editContractTemplateApi,
  enableContractTemplateApi,
  getContractClassifyListApi,
  importMaterialApi,
} from '#/api';
import { getContractTemplatePageListApi } from '#/api/contract/template';
import TemplateEdit from '#/views/contract/template/template-edit.vue';

const dictStore = useDictStore();
const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'templateName',
      label: '模板名称',
    },
    {
      component: 'Select',
      fieldName: 'state',
      label: '状态',
      componentProps: {
        options: dictStore.getDictList('baseEnableType'),
      },
    },
    {
      component: 'ApiSelect',
      fieldName: 'classifyId',
      label: '合同分类',
      componentProps: {
        api: getContractClassifyListApi,
        labelField: 'categoryName',
        valueField: 'id',
      },
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  checkboxConfig: {
    checkStrictly: true,
  },
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'templateName', title: '模板名称' },
    { field: 'categoryName', title: '模板分类', slots: { default: 'categoryName' } },
    { field: 'state', title: '模板状态', formatter: ['formatStatus', 'baseEnableType'] },
    { field: 'updateTime', title: '更新时间' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async (
        { page }: { page: { currentPage: number; pageSize: number } },
        formValues: { categoryName: string },
      ) => {
        return await getContractTemplatePageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: AccessInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const modalTitle = ref('新增模板');
const loading = ref({
  save: false,
});
const TemplateFormRef = ref();
const templateForm = ref<Partial<ContractTemplateInfo>>({});
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await TemplateFormRef.value.validate();
    let api = addContractTemplateApi;
    if (templateForm.value.id) {
      api = editContractTemplateApi;
    }
    await api(unref(templateForm) as ContractTemplateInfo);
    message.success($t('base.resSuccess'));
    await modalApi.close();
    await gridApi.reload();
  },
});
const addClassify = () => {
  templateForm.value = {};
  modalTitle.value = '新增模板';
  modalApi.open();
};
const editTemplate = (row: ContractTemplateInfo) => {
  templateForm.value = cloneDeep(row);
  modalTitle.value = '编辑模板';
  modalApi.open();
};
const delTemplate = async () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '确认删除此分类？',
    async onOk() {
      await deleteContractTemplateApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const changeTemplateStatus = (status: 0 | 1) => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const pidList = res.map((item) => item.id);
  const operation = {
    1: {
      label: '启用',
      api: enableContractTemplateApi,
    },
    0: {
      label: '禁用',
      api: disableContractTemplateApi,
    },
  };
  AntdModal.confirm({
    title: `确认${operation[status].label}`,
    content: `确认${operation[status].label}此模板吗？`,
    async onOk() {
      await operation[status].api(pidList);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const preview = (_row: ContractTemplateInfo) => {};
const download = (_row: ContractTemplateInfo) => {};
const copy = async (row: ContractTemplateInfo) => {
  AntdModal.confirm({
    title: '确认复制',
    content: '确认复制此模板吗？',
    async onOk() {
      await copyContractTemplateApi(row);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const editTemplateDoc = (row: ContractTemplateInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const rules = {
  templateName: [{ required: true, message: '请输入模板名称' }],
  categoryId: [{ required: true, message: '请选择合同分类', trigger: 'change' }],
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button type="primary" @click="addClassify">新增模板</a-button>
          <a-button type="primary" danger @click="changeTemplateStatus(1)">启用</a-button>
          <a-button type="primary" danger @click="changeTemplateStatus(0)">禁用</a-button>
          <a-button type="primary" danger @click="delTemplate">删除</a-button>
        </a-space>
      </template>
      <template #categoryName="{ row }">
        <Tooltip title="点击变更" placement="right">
          <a-typography-link @click="editTemplate(row)">{{ row.categoryName }}</a-typography-link>
        </Tooltip>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="preview(row)">预览</a-typography-link>
          <a-typography-link @click="editTemplateDoc(row)">编辑</a-typography-link>
          <Dropdown>
            <a-typography-link>
              <a-space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </a-space>
            </a-typography-link>
            <template #overlay>
              <Menu>
                <MenuItem key="menu" @click="download(row)"> 下载 </MenuItem>
                <MenuItem key="menu" @click="copy(row)"> 复制 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </a-space>
      </template>
    </Grid>
    <TemplateEdit @register="registerForm" @ok="editSuccess" />
    <Modal :title="modalTitle" :confirm-loading="loading.save">
      <a-form ref="TemplateFormRef" :model="templateForm" v-bind="FORM_PROP" :rules="rules">
        <a-form-item label="模板名称" name="templateName">
          <a-input v-model:value="templateForm.templateName" />
        </a-form-item>
        <a-form-item label="合同分类" name="categoryId">
          <ApiComponent
            v-model="templateForm.categoryId as unknown as string"
            :component="Select"
            :api="getContractClassifyListApi"
            label-field="categoryName"
            value-field="id"
            model-prop-name="value"
          />
        </a-form-item>
        <a-form-item v-if="!templateForm.id" label="模板文件" name="">
          <BaseUpload :upload-api="importMaterialApi" />
        </a-form-item>
      </a-form>
    </Modal>
  </Page>
</template>

<style></style>
