<script setup lang="ts">
import { OnlyOffice } from '@vben/base-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message } from 'ant-design-vue';

import { getContractTemplateInfoApi } from '#/api';

const emit = defineEmits(['ok', 'register']);
const init = async (_data: ContractTemplateInfo) => {};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const save = async () => {
  changeOkLoading(true);
  try {
    // await api(unref(templateForm) as ContractTemplateInfo);
    emit('ok');
    message.success('保存成功');
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="模板信息" @register="registerPopup" @ok="save">
    <OnlyOffice :get-config-api="getContractTemplateInfoApi" />
  </BasicPopup>
</template>

<style></style>
