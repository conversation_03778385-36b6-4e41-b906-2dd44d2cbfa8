<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, h, onBeforeUnmount, ref } from 'vue';
import { useRouter } from 'vue-router';

import { AuthenticationForgetPassword, z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { VbenButton } from '@vben-core/shadcn-ui';

import {
  getEmailByUserNameApi,
  getMobileByUserNameApi,
  resetPasswordApi,
  sendEmailCodeApi,
  sendSmsCodeApi,
  validatePasswordApi,
} from '#/api';
import Title from "@vben/common-ui/src/ui/authentication/auth-title.vue";

defineOptions({ name: 'ForgetPassword' });

const router = useRouter();
const loading = ref(false);
const active = ref<1 | 2 | 3>(2);
interface StepConfig {
  [key: number]: VbenFormSchema[]; // 明确表示可以用数字索引
}
const confirmAccount = ref('');
const findType = ref('');
const userName = ref('');
const captcha = ref('');

// 发送验证码相关状态
const sendCodeLoading = ref(false);
const countdown = ref(0);
const countdownTimer = ref<NodeJS.Timeout | null>(null);
const formSchema = computed((): VbenFormSchema[] => {
  const step: StepConfig = {
    1: [
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请输入找回账号',
        },
        fieldName: 'userName',
        label: $t('authentication.username'),
        rules: z.string().min(1, { message: $t('authentication.userNameTip') }),
      },
      {
        component: 'VbenSelect',
        componentProps: {
          placeholder: '请选择找回方式',
          options: [
            { label: '通过邮箱找回', value: 'email' },
            { label: '通过手机找回', value: 'mobile' },
          ],
        },
        fieldName: 'type',
        label: '找回方式',
        rules: z.string().min(1, { message: '请选择找回方式' }),
      },
    ],
    2: [
      {
        component: 'VbenInput',
        componentProps: {
          disabled: true,
          modelValue: confirmAccount.value,
        },
        fieldName: 'showAccount',
        label: '确认账号',
      },
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请确认邮箱/手机号',
        },
        fieldName: 'confirmAccount',
        label: '确认账号',
        rules: z.string().min(1, { message: '请输入请确认邮箱/手机号' }),
      },
      {
        component: 'Input',
        componentProps: {
          placeholder: '请输入验证码',
          addonAfter: () =>
            h(
              VbenButton,
              {
                disabled: sendCodeLoading.value || countdown.value > 0,
                loading: sendCodeLoading.value,
                size: 'sm',
                variant: 'default',
                onClick: handleSendCode,
              },
              () => (countdown.value > 0 ? `${countdown.value}s` : '发送验证码'),
            ),
        },
        fieldName: 'captcha',
        label: '验证码',
        rules: z.string().min(1, { message: '请输入验证码' }),
      },
    ],
    3: [
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请输入新密码',
          type: 'password',
        },
        fieldName: 'newPassword',
        label: '新密码',
        rules: z
          .string()
          .min(1, { message: '请输入新密码' })
          .refine(async (value) => {
            console.log('value:', value);
            console.log('userName:', userName.value);
            if (!value || !userName.value) return true;
            try {
              const res = await validatePasswordApi({
                password: value,
                userName: userName.value,
              });
              console.log('res:', res);
              if (res.code !== 200) {
                throw new Error(res.msg || '密码强度验证失败');
              }
              return true;
            } catch (error) {
              throw new Error(error instanceof Error ? error.message : '密码强度验证失败');
            }
          }),
      },
      {
        component: 'VbenInput',
        componentProps: {
          placeholder: '请确认新密码',
          type: 'password',
        },
        dependencies: {
          rules(values) {
            const { newPassword } = values;
            return z
              .string({ required_error: '请输入确认新密码' })
              .min(1, { message: '请输入确认新密码' })
              .refine((value) => value === newPassword, {
                message: '两次输入的密码不一致',
              });
          },
          triggerFields: ['newPassword'],
        },
        fieldName: 'confirmPassword',
        label: '确认新密码',
      },
    ],
  };
  return step[active.value]!;
});
// 发送验证码函数
async function handleSendCode() {
  if (!confirmAccount.value || !userName.value || !findType.value) {
    return;
  }

  try {
    sendCodeLoading.value = true;

    // 根据找回方式选择对应的API
    const sendCodeApi = findType.value === 'mobile' ? sendSmsCodeApi : sendEmailCodeApi;
    const params = {
      userName: userName.value,
      [findType.value === 'mobile' ? 'mobile' : 'email']: confirmAccount.value,
    };

    await sendCodeApi(params);

    // 开始倒计时
    startCountdown();
  } catch (error) {
    console.error('发送验证码失败:', error);
  } finally {
    sendCodeLoading.value = false;
  }
}

// 开始倒计时
function startCountdown() {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!);
      countdownTimer.value = null;
    }
  }, 1000);
}

// 清理定时器
function clearCountdownTimer() {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
    countdown.value = 0;
  }
}

const AuthForgetPasswordRef = ref();
async function handleSubmit(value: Recordable<any>) {
  // eslint-disable-next-line no-console
  console.log('value:', value);
  switch (active.value) {
    case 1: {
      userName.value = value.userName;
      findType.value = value.type;
      let api = getMobileByUserNameApi;
      if (value.type === 'email') {
        // 通过邮箱找回
        api = getEmailByUserNameApi;
      }
      confirmAccount.value = await api({ userName: value.userName });
      active.value = 2;

      break;
    }
    case 2: {
      captcha.value = value.captcha;
      // 直接进入下一步，验证码已通过按钮发送
      active.value = 3;
      break;
    }
    case 3: {
      const params = {
        captcha: captcha.value,
        newPassword: value.newPassword,
        userName: userName.value,
      };
      await resetPasswordApi(params);
      await router.push({ name: 'Login' });
      break;
    }
    // No default
  }
}

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  clearCountdownTimer();
});
</script>

<template>
  <div>
    <Title>
      <slot name="title"> {{ title || $t('authentication.forgetPassword') }} 🤦🏻‍♂️ </slot>
      <template #desc>
        <slot name="subTitle">
          {{ subTitle || $t('authentication.forgetPasswordSubtitle') }}
        </slot>
      </template>
    </Title>
    <Form />

    <div>
      <VbenButton
        :class="{
          'cursor-wait': loading,
        }"
        aria-label="submit"
        class="mt-2 w-full"
        @click="handleSubmit"
      >
        <slot name="submitButtonText">
          {{ submitButtonText || $t('authentication.next') }}
        </slot>
      </VbenButton>
      <VbenButton class="mt-4 w-full" variant="outline" @click="goToLogin()">
        {{ $t('common.back') }}
      </VbenButton>
    </div>
  </div>
</template>
