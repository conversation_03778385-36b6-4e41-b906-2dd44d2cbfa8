import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    name: 'Contract',
    path: '/contract',
    meta: {
      icon: 'lucide:layout-dashboard',
      title: 'page.contract.title',
    },
    children: [
      {
        name: 'ContractClassify',
        path: '/contract/classify',
        component: () => import('#/views/contract/classify/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.contract.classify',
        },
      },
      {
        name: 'ContractTemplate',
        path: '/contract/template',
        component: () => import('#/views/contract/template/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.contract.template',
        },
      },
      {
        name: 'ContractSign',
        path: '/contract/sign',
        component: () => import('#/views/contract/sign/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.contract.sign',
        },
      },
    ],
  },
];
export default routes;
