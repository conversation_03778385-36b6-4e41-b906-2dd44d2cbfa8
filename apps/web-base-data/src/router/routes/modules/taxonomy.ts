import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    name: 'Taxonomy',
    path: '/taxonomy',
    meta: {
      icon: 'lucide:layout-dashboard',
      title: 'page.taxonomy.title',
    },
    children: [
      {
        name: 'TaxonomyManage',
        path: '/taxonomy/manage',
        component: () => import('#/views/taxonomy/manage/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.taxonomy.manage',
        },
      },
    ],
  },
];
export default routes;
