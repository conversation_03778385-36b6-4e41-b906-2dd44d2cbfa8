const routes = [
  {
    name: 'Attributes',
    path: '/attributes',
    meta: {
      icon: 'lucide:layout-dashboard',
      title: 'page.attributes.title',
    },
    children: [
      {
        name: 'AttributesUnit',
        path: '/attributes/unit',
        component: () => import('#/views/attributes/unit/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.attributes.unit',
        },
      },
      {
        name: 'AttributesSpecification',
        path: '/attributes/specification',
        component: () => import('#/views/attributes/specification/index.vue'),
        meta: {
          icon: 'carbon:workspace',
          title: 'page.attributes.specification',
        },
      },
      // {
      //   name: 'AttributesBrand',
      //   path: '/attributes/brand',
      //   component: () => import('#/views/attributes/brand/index.vue'),
      //   meta: {
      //     icon: 'carbon:workspace',
      //     title: 'page.attributes.brand',
      //   },
      // },
      // {
      //   name: 'AttributesManufacturer',
      //   path: '/attributes/manufacturer',
      //   component: () => import('#/views/attributes/manufacturer/index.vue'),
      //   meta: {
      //     icon: 'carbon:workspace',
      //     title: 'page.attributes.manufacturer',
      //   },
      // },
    ],
  },
];
export default routes;
