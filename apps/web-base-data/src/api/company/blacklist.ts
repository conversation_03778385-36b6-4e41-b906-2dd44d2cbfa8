import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 *  新增企业黑名单
 */
export interface BlackListAddInfo {
  // 统一社会信用代码
  companyCode: string;
  // 企业名称
  companyName: string;
  // 操作理由
  remarks?: string;
  [property: string]: any;
}

/**
 *  编辑企业黑名单
 */
export interface BlackListEditInfo {
  // 统一社会信用代码
  companyCode: string;
  // 企业名称
  companyName: string;
  // 黑名单ID
  id: number;
  // 操作类型
  operationType: string;
  // 操作理由
  remarks?: string;
  [property: string]: any;
}

export async function getBlackListPageListApi(params: PageListParams) {
  return requestClient.get('/base/company/blackList/page', { params });
}
export async function addBlackListApi(data: BlackListAddInfo) {
  return requestClient.post('/base/company/blackList/add', data);
}
export async function editBlackListApi(data: BlackListAddInfo) {
  return requestClient.post('/base/company/blackList/edit', data);
}
export async function removeBlackListApi(data: { id: number; remark: string }) {
  return requestClient.post('/base/company/blackList/remove', data);
}
export async function getBlackListDetailApi(params: { id: number }) {
  return requestClient.get('/base/company/blackList/detail', { params });
}
