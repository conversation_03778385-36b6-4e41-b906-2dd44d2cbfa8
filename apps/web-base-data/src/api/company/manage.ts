import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * CompanySaveRequest，编辑企业
 */
export interface CompanyInfo {
  // 实缴资本（万元）
  actualCapital?: number;
  // 经营地址
  bizLocation?: string;
  // 经营范围
  bizScope?: string;
  // 企业主体账户信息列表
  companyBankList?: CompanyBankBO[];
  // 统一社会信用代码
  companyCode?: string;
  // 企业主体开票信息列表
  companyInvoiceList?: CompanyInvoiceBO[];
  // 企业名称
  companyName?: string;
  // 企业类型
  companyOrgType?: string;
  // 企业联系人姓名
  contactPersonName?: string;
  // 企业联系人电话
  contactPersonPhone?: string;
  // 创建人
  createBy?: number;
  // 创建时间
  createTime?: Date;
  // 逻辑删除
  deleteFlag?: number;
  // 行政区划
  district?: string;
  // 启用状态
  enabled?: number;
  // 成立日期
  establishTime?: Date;
  // 营业期限开始
  fromTime?: Date;
  // 主键
  id?: number;
  // 所属行业
  industry?: string;
  // 法定代表身份证反面
  legalPersonIdCardBack?: string;
  // 法定代表身份证正面
  legalPersonIdCardFront?: string;
  // 法定代表身份证号
  legalPersonIdNumber?: string;
  // 法定代表姓名
  legalPersonName?: string;
  // 法定代表手机号
  legalPersonPhone?: string;
  // 组织结构代码
  orgNumber?: string;
  // 注册资本（万元）
  regCapital?: number;
  // 注册地址
  regLocation?: string;
  // 工商注册号
  regNumber?: string;
  // 登记状态
  regStatus?: string;
  // 备注
  remark?: string;
  // 状态
  status?: string;
  // 纳税人识别号
  taxNumber?: string;
  // 纳税人资质
  taxpayerQualification?: string;
  // 营业期限结束
  toTime?: Date;
  // 更新人
  updateBy?: number;
  // 更新时间
  updateTime?: Date;
  [property: string]: any;
}

/**
 * CompanyBankBO，企业主体账户信息表
 */
export interface CompanyBankBO {
  // 银行账号
  account?: string;
  // 账户名称
  accountName?: string;
  // 银行名称
  bank?: string;
  // 开户行支行名称
  branch?: string;
  // 开户行银联号
  branchNumber?: string;
  // 企业ID
  companyId?: number;
  // 创建人
  createBy?: number;
  // 创建时间
  createTime?: Date;
  // 逻辑删除
  deleteFlag?: number;
  // 是否启用
  enabled?: number;
  // 主键
  id?: number;
  // 是否默认账户
  isDefault?: number;
  // 更新人
  updateBy?: number;
  // 更新时间
  updateTime?: Date;
  [property: string]: any;
}

/**
 * CompanyInvoiceBO，企业主体开票信息表
 */
export interface CompanyInvoiceBO {
  // 银行账号
  account?: string;
  // 地址
  address?: string;
  // 开户行
  bank?: string;
  // 企业ID
  companyId?: number;
  // 创建人
  createBy?: number;
  // 创建时间
  createTime?: Date;
  // 逻辑删除
  deleteFlag?: number;
  // 是否启用
  enabled?: number;
  // 主键
  id?: number;
  // 是否默认
  isDefault?: number;
  // 电话
  phone?: string;
  // 纳税人识别号
  taxNumber?: string;
  // 抬头名称
  title?: string;
  // 更新人
  updateBy?: number;
  // 更新时间
  updateTime?: Date;
  [property: string]: any;
}

export async function getCompanyPageListApi(params: PageListParams) {
  return requestClient.get('/base/company/page', { params });
}
export async function addCompanyApi(data: CompanyInfo) {
  return requestClient.post('/base/company/add', data);
}
export async function editCompanyApi(data: CompanyInfo) {
  return requestClient.post('/base/company/edit', data);
}
export async function deleteCompanyApi(id: number) {
  return requestClient.post('/base/company/delete', {}, { params: { id } });
}
export async function getCompanyListApi() {
  return requestClient.get('/base/company/list');
}
export async function enableCompanyApi(id: number) {
  return requestClient.post('/base/company/enabled', {}, { params: { id } });
}
export async function disableCompanyApi(id: number) {
  return requestClient.post('/base/company/disabled', {}, { params: { id } });
}
export async function getCompanyInfoApi(params: { id: number }) {
  return requestClient.get('/base/company/detail', { params });
}
export async function getCompanyRolePageApi(params: { id: number }) {
  return requestClient.get('/base/company/role/list', { params });
}
export async function addCompanyRoleApi(data: { companyId: number; role: string }) {
  return requestClient.post('/base/company/role/add', data);
}
export async function deleteCompanyRoleApi(id: number) {
  return requestClient.post('/base/company/role/delete', {}, { params: { id } });
}
