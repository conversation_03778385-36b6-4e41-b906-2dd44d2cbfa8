import type { UserInfo } from '@vben/types';

import { baseRequestClient, requestClient } from '#/api/request';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    access_token: string;
    refresh_token: string;
  }
  export interface LoginErrorResult {
    error_code: string;
    error_description: string;
    success: boolean;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>(
    '/auth/oauth/token',
    {},
    {
      params: { grant_type: 'password', scope: 'all', ...data },
      headers: {
        Authorization: `Basic ${import.meta.env.VITE_LOGIN_AUTHORIZATION}`,
        'Tenant-Id': import.meta.env.VITE_TENANT_ID,
      },
      responseReturn: 'body',
    },
  );
}

/**
 * 获取用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get<UserInfo>('/auth/oauth/user-info', {
    responseReturn: 'body',
  });
}

/**
 * 刷新accessToken
 */
export async function refreshTokenApi(data: { refresh_token: string }) {
  return requestClient.post<AuthApi.LoginResult>(
    '/auth/oauth/token',
    {},
    {
      params: {
        grant_type: 'refresh_token',
        scope: 'all',
        ...data,
      },
      headers: {
        Authorization: `Basic ${import.meta.env.VITE_LOGIN_AUTHORIZATION}`,
        'Tenant-Id': import.meta.env.VITE_TENANT_ID,
      },
      responseReturn: 'body',
    },
  );
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return baseRequestClient.get('/auth/oauth/logout');
}

/**
 * 获取用户权限码
 */
export async function getAccessCodesApi() {
  return requestClient.get<string[]>('/auth/codes');
}
