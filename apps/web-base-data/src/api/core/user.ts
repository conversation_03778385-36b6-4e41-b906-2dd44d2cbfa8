import { requestClient } from '#/api/request';

export async function getUserInfoByIdsApi(ids: string[]) {
  return requestClient.post('/upms/user/selector_by_ids', ids);
}
export async function getUserListByKeywordApi(params: { keyword: string }) {
  return requestClient.post('/upms/user/selector_by_keywords', {}, { params });
}
export async function getUserTreeListApi(params: { name: string; orgId: string }) {
  return requestClient.post('/upms/user/async/selector_by_org_id', {}, { params });
}
export async function validatePassword<PERSON><PERSON>(params: { password: string; userId: number }) {
  return requestClient.post('/upms/user/validate_pwd', {}, { params, responseReturn: 'body' });
}
