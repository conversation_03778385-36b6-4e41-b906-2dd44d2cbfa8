import { requestClient } from '#/api/request';

// 通过账号获取手机号
export async function getMobileByUserNameApi(params: { userName: string }) {
  return requestClient.get('/upms/user/forget/get_mobile', { params });
}
// 通过账号获取邮箱
export async function getEmailByUserNameApi(params: { userName: string }) {
  return requestClient.get('/upms/user/forget/get_email', { params });
}
// 发送验证码短信
export async function sendSmsCodeApi(params: { mobile: string; userName: string }) {
  return requestClient.post('/upms/user/forget/send_sms', {}, { params });
}
// 发送验证码邮件
export async function sendEmailCode<PERSON>pi(params: { email: string; userName: string }) {
  return requestClient.post('/upms/user/forget/send_email', {}, { params });
}
// 验证密码强度（忘记密码流程专用）
export async function validatePasswordForForgetApi(params: { password: string; userName: string }) {
  return requestClient.post('/upms/user/forget/validate_pwd', {}, { params, responseReturn: 'body' });
}
// 重置密码
export async function resetPasswordApi(params: { captcha: string; newPassword: string; userName: string }) {
  return requestClient.post('/upms/user/forget/reset_pwd', {}, { params });
}
