import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * ContractTemplateRequest，模板表
 */
export interface ContractTemplateInfo {
  // 分类ID
  categoryId?: number;
  // 文件Id
  fileId?: string;
  // 主键
  id?: number;
  // 模板状态
  state?: number;
  // 模板Id
  templateId?: string;
  // 模板名称
  templateName?: string;
  // 模板类型
  templateType?: string;
  // 版本号
  version?: number;
  [property: string]: any;
}

export async function getContractTemplatePageListApi(params: PageListParams) {
  return requestClient.get('/base/contract/template/pageList', { params });
}
export async function getContractTemplateListApi() {
  return requestClient.get('/base/contract/template/list');
}
export async function addContractTemplateApi(data: ContractTemplateInfo) {
  return requestClient.post('/base/contract/template/create', data);
}
export async function editContractTemplateApi(data: ContractTemplateInfo) {
  return requestClient.post('/base/contract/template/update', data);
}
export async function deleteContractTemplateApi(id: number) {
  return requestClient.post('/base/contract/template/delete', {}, { params: { id } });
}
export async function enableContractTemplateApi(id: number) {
  return requestClient.post('/base/contract/template/enable', {}, { params: { id } });
}
export async function disableContractTemplateApi(id: number) {
  return requestClient.post('/base/contract/template/disable', {}, { params: { id } });
}
export async function getContractTemplateInfoApi(params: { id: number }) {
  return requestClient.get('/base/contract/template/info', { params });
}
export async function copyContractTemplateApi(data: ContractTemplateInfo) {
  return requestClient.post('/base/contract/template/copy', data);
}
