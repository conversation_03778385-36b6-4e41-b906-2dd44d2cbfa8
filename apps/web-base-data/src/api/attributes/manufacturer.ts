import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface ManufacturerInfo extends BaseDataParams {
  // 生产厂家编码
  manufacturerCode: string;
  // 生产厂家名称
  manufacturerName: string;
  // 厂号
  factoryNumber: string;
  // 状态；已启用=1，已禁用=0
  status: number;
  // 国家/地区名称（字典表）
  countryRegion: string;
  // 备注
  remarks: string;
}

export async function getAttributeManufacturerPageList(params: PageListParams) {
  return requestClient.get('/base/manufacturer/page', { params });
}

export async function addAttributeManufacturerApi(data: ManufacturerInfo) {
  return requestClient.post('/base/manufacturer/add', data);
}

export async function updateAttributeManufacturerApi(data: ManufacturerInfo) {
  return requestClient.post('/base/manufacturer/update', data);
}

export async function deleteAttributeManufacturerApi(ids: number[]) {
  return requestClient.post('/base/manufacturer/delete', ids);
}

export async function enableAttributeManufacturerApi(ids: number[]) {
  return requestClient.post('/base/manufacturer/enable', ids);
}

export async function disableAttributeManufacturerApi(ids: number[]) {
  return requestClient.post('/base/manufacturer/disable', ids);
}

export async function getAttributeManufacturerList(params: PageListParams) {
  return requestClient.get('/base/manufacturer/list', { params });
}
