import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

/**
 * SpecsAddRequest，规格属性
 */
export interface SpecificationInfo {
  // 规格属性名称
  specName: string;
  // 规格属性值
  specValueList?: string[];
  [property: string]: any;
}

export async function getSpecificationPageListApi(params: PageListParams) {
  return requestClient.get('/base/product/specs/page', { params });
}
export async function getSpecificationListApi() {
  return requestClient.get('/base/product/specs/list');
}
export async function addSpecificationApi(data: SpecificationInfo) {
  return requestClient.post('/base/product/specs/add', data);
}
export async function editSpecificationApi(data: SpecificationInfo) {
  return requestClient.post('/base/product/specs/update', data);
}
export async function deleteSpecificationApi(data: string[]) {
  return requestClient.post('/base/product/specs/delete', data);
}
