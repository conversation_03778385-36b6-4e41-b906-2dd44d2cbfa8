export default eventHandler(async () => {
  const listData = [
    {
      createTime: 1_724_832_541_000,
      updateBy: '4M0KUJ0000000',
      updateTime: 1_748_060_765_000,
      id: 1,
      ruleName: '结算周期到期提醒',
      ruleLevel: '1',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      triggerCondition: '结算周期到期日-当前日期＜222',
      triggerConditionValue: '222',
      enable: 0,
      deleteFlag: 0,
      version: 25,
    },
    {
      createTime: 1_724_835_507_000,
      updateBy: '3MESI40000000',
      updateTime: 1_739_156_455_000,
      id: 2,
      ruleName: '认定抵质押价值提醒',
      ruleLevel: '1',
      ruleDescription: '在途资金余额达到认定抵质押价值一定比例时，触发预警',
      triggerCondition: '认定抵质押价值/在途资金余额>3%',
      triggerConditionValue: '3',
      enable: 0,
      deleteFlag: 0,
      version: 21,
    },
    {
      createTime: 1_724_846_803_000,
      updateBy: '3MESI40000000',
      updateTime: 1_739_156_454_000,
      id: 3,
      ruleName: '投后报告提醒(每90天)',
      ruleLevel: '3',
      ruleDescription: '距离投后检查到期＜60，触发预警',
      triggerCondition: '投后检查到期日-当前日期＜60',
      triggerConditionValue: '60',
      enable: 0,
      deleteFlag: 0,
      version: 20,
    },
    {
      createTime: 1_725_615_306_000,
      updateBy: '3MESI40000000',
      updateTime: 1_739_156_452_000,
      id: 4,
      ruleName: '外部融资授信到期预警',
      ruleLevel: '3',
      ruleDescription: '距离授信截止时间<5，触发预警',
      triggerCondition: '授信截止时间-当前日期<5',
      triggerConditionValue: '5',
      enable: 0,
      deleteFlag: 0,
      version: 8,
    },
    {
      createTime: 1_725_615_404_000,
      updateBy: '3MESI40000000',
      updateTime: 1_739_156_452_000,
      id: 5,
      ruleName: '外部融资用信到期预警',
      ruleLevel: '1',
      ruleDescription: '距离用信截止时间<12，触发预警',
      triggerCondition: '用信截止时间-当前日期<12',
      triggerConditionValue: '12',
      enable: 0,
      deleteFlag: 0,
      version: 9,
    },
    {
      createTime: 1_727_170_280_000,
      updateBy: '4M0KUJ0000000',
      updateTime: 1_736_412_924_000,
      id: 6,
      ruleName: '项目立项会议纪要上传预警规则(90天)',
      ruleLevel: '1',
      ruleDescription: '距离会议纪要上传日期<90,且没有完成上传时,触发预警',
      triggerCondition: '会议纪要上传到期日-当前日期<90',
      triggerConditionValue: '90',
      enable: 0,
      deleteFlag: 0,
      version: 14,
    },
  ];
  return useResponseSuccess(listData);
});
