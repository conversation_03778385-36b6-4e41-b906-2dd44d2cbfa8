export default eventHandler(async (event) => {
  const { current, size } = getQuery(event);
  const listData = [
    {
      createTime: 1_752_163_510_000,
      id: 4466,
      alertCode: 'YJ202507110005090040',
      alertRelationId: '5AR9JH0000002',
      alertRelationName: 'MA-333-X',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5AR0NN0000000',
      projectCode: 'XM202504291003350001',
      projectName: 'MMAA',
      companyCode: '23453647586970',
      companyName: 'MMAA下游企业',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4472,
      alertCode: 'YJ202507110005090046',
      alertRelationId: '5BTNP90000002',
      alertRelationName: 'W-ccc-x',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5AGAP90000000',
      projectCode: 'XM202504250847370001',
      projectName: 'HHH项目',
      companyCode: '3213213123',
      companyName: 'HHH企业',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4471,
      alertCode: 'YJ202507110005090045',
      alertRelationId: '5BJJAN0000000',
      alertRelationName: 'W-bbb-x',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5AGAP90000000',
      projectCode: 'XM202504250847370001',
      projectName: 'HHH项目',
      companyCode: '3213213123',
      companyName: 'HHH企业',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4470,
      alertCode: 'YJ202507110005090044',
      alertRelationId: '5BJJ3U0000000',
      alertRelationName: 'W-aaa-x',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5AGAP90000000',
      projectCode: 'XM202504250847370001',
      projectName: 'HHH项目',
      companyCode: '3213213123',
      companyName: 'HHH企业',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4469,
      alertCode: 'YJ202507110005090043',
      alertRelationId: '5BJGKN0000000',
      alertRelationName: 'W-kkk-x',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5AGAP90000000',
      projectCode: 'XM202504250847370001',
      projectName: 'HHH项目',
      companyCode: '3213213123',
      companyName: 'HHH企业',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4468,
      alertCode: 'YJ202507110005090042',
      alertRelationId: '5ARDDD0000002',
      alertRelationName: 'VV-TTT-X',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5ARD5S0000000',
      projectCode: 'XM202504291335560002',
      projectName: 'VV项目',
      companyCode: '*********',
      companyName: 'vvvvv企业',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4467,
      alertCode: 'YJ202507110005090041',
      alertRelationId: '5ARD870000004',
      alertRelationName: 'VV-RRR-X',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5ARD5S0000000',
      projectCode: 'XM202504291335560002',
      projectName: 'VV项目',
      companyCode: '*********',
      companyName: 'vvvvv企业',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4463,
      alertCode: 'YJ202507110005090037',
      alertRelationId: '5AQS1L0000002',
      alertRelationName: 'BB-555-X',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5ANSTM0000000',
      projectCode: 'XM202504280540060001',
      projectName: 'BBB-BBB',
      companyCode: '1234567890876543',
      companyName: 'BBBBBBB',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4461,
      alertCode: 'YJ202507110005090035',
      alertRelationId: '5ANU3F0000002',
      alertRelationName: 'BB-222-X',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5ANSTM0000000',
      projectCode: 'XM202504280540060001',
      projectName: 'BBB-BBB',
      companyCode: '1234567890876543',
      companyName: 'BBBBBBB',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
    {
      createTime: 1_752_163_510_000,
      id: 4462,
      alertCode: 'YJ202507110005090036',
      alertRelationId: '5ANUCM0000002',
      alertRelationName: 'BB-333-X',
      ruleId: 1,
      ruleName: '结算周期到期提醒',
      ruleDescription: '距离结算周期到期日<222，触发预警',
      ruleLevel: '1',
      projectId: '5ANSTM0000000',
      projectCode: 'XM202504280540060001',
      projectName: 'BBB-BBB',
      companyCode: '1234567890876543',
      companyName: 'BBBBBBB',
      status: '1',
      disposalStatus: '2',
      deleteFlag: 0,
    },
  ];
  return usePageResponseSuccess(current as string, size as string, listData);
});
