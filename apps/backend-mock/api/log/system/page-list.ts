export default eventHandler(async (event) => {
  const { page, pageSize } = getQuery(event);
  const listData = [
    {
      pid: '2cf80d51-04f4-4170-922d-8db6b7818662',
      type: '编辑',
      title: '异常信息变更',
      platform: 'User-Type',
      time: 41,
      serviceId: 'app-factoring',
      serverIp: '************',
      serverHost: '************:80',
      status: '200',
      env: 'test',
      remoteIp: '***********',
      remoteAddress: '未知',
      userAgent:
        'Mozilla/5.0 (Windows NT 10.0; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/94.0.4606.71 Safari/537.36 Core/1.94.202.400 QQBrowser/11.9.5355.400',
      client: 'QQ Browser 11.9.5355.400',
      os: 'Windows 10 (x64)',
      device: 'desktop',
      path: '/factoring/errorMsg/modify/3S9N990000000',
      requestUrl: '/factoring/errorMsg/modify/3S9N990000000?_t=**********',
      method: 'GET',
      response: '{\n    "code": 200,\n    "msg": "操作成功"\n}',
      userId: '40FQVT0000000',
      userName: '杨燕',
      account: 'wechat_1360',
      operateTime: 1_703_260_531_000,
      tenantId: '000000',
    },
    {
      pid: '1fc4a0a6-707d-4d11-9079-98290077009c',
      type: '新增',
      title: '新增净利润',
      platform: 'User-Type',
      time: 18,
      serviceId: 'service-principal',
      serverIp: '************',
      serverHost: '************:80',
      status: '200',
      env: 'test',
      remoteIp: '***********',
      remoteAddress: '未知',
      userAgent:
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      client: 'Chrome 120.0.0.0',
      os: 'Mac 10.15.7',
      device: 'Apple',
      path: '/principal/profit/create',
      requestUrl: '/principal/profit/create?_t=**********',
      method: 'POST',
      response:
        '{\n    "code": 200,\n    "msg": "操作成功",\n    "data": "41L6380000001"\n}',
      userId: '3QT2B20000000',
      userName: '夏丽杰',
      account: 'wechat_1345',
      operateTime: 1_703_253_589_000,
      tenantId: '000000',
    },
    {
      pid: 'b5aab7c6-2bff-4f8d-ba00-95e52274690e',
      type: '编辑',
      title: '编辑企业',
      platform: 'User-Type',
      time: 40,
      serviceId: 'service-principal',
      serverIp: '************',
      serverHost: '************:80',
      status: '200',
      env: 'test',
      remoteIp: '***********',
      remoteAddress: '未知',
      userAgent:
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      client: 'Chrome 120.0.0.0',
      os: 'Mac 10.15.7',
      device: 'Apple',
      path: '/principal/enterprise/edit',
      requestUrl: '/principal/enterprise/edit?_t=**********',
      method: 'POST',
      response:
        '{\n    "code": 200,\n    "msg": "操作成功",\n    "data": "41L6380000001"\n}',
      userId: '3QT2B20000000',
      userName: '夏丽杰',
      account: 'wechat_1345',
      operateTime: 1_703_253_589_000,
      tenantId: '000000',
    },
  ];
  return usePageResponseSuccess(page as string, pageSize as string, listData);
});
