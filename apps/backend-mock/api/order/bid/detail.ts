import { fakerZH_CN as faker } from '@faker-js/faker';

function generateMockData() {
  const bindProgressValues = ['01', '02'];
  const bidResultValues = ['01', '02'];
  const relatedTypeValues = ['', '01', '03'];
  return {
    pid: faker.string.uuid(),
    code: faker.string.uuid(),
    name: faker.commerce.productName(),
    auditStatus: faker.helpers.arrayElement(['00', '10', '20']),
    bindProgress: faker.helpers.arrayElement(bindProgressValues),
    bindResult: faker.helpers.arrayElement(bidResultValues),
    relatedType: faker.helpers.arrayElement(relatedTypeValues),
    relatedNumber: faker.string.uuid(),
    tenderNumber: faker.string.uuid(),
    tenderName: faker.commerce.productName(),
    tenderEntity: faker.company.name(),
    tenderType: faker.lorem.words(),
    tenderContent: faker.lorem.lines(),
    competitorAnalysis: faker.lorem.lines(),
    remarks: faker.lorem.lines(),
    biddingInformation: faker.lorem.words(),
    tenderPeriod: faker.date.recent().toISOString(),
    planEndDate: faker.date.recent().toISOString(),
    biddingDate: faker.date.recent().toISOString(),
    biddingBudget: faker.commerce.price({ min: 1000, max: 9999 }),
    biddingDocumentFee: faker.commerce.price({ min: 1000, max: 9999 }),
    biddingDeposit: faker.commerce.price({ min: 1000, max: 9999 }),
    totalQuotedAmount: faker.commerce.price({ min: 1000, max: 9999 }),
    createDepartment: faker.commerce.department(),
    createBy: faker.person.fullName(),
    createTime: faker.date.recent().toISOString(),
    goodsList: Array.from(
      { length: faker.number.int({ min: 1, max: 5 }) },
      () => ({
        goodsName: faker.commerce.productName(),
        models: faker.helpers.arrayElement([
          '规格A',
          '规格B',
          '规格C',
          '规格D',
        ]),
        taxRate: faker.helpers.arrayElement([6, 9, 13]),
        num: faker.number.int({ min: 1, max: 100 }),
        unitPriceTax: faker.commerce.price({ min: 10, max: 1000 }),
        get amountTax() {
          return (this.num * this.unitPriceTax).toFixed(2);
        },
        remark: faker.lorem.sentence(5),
      }),
    ),
  };
}
const mockData = generateMockData();

export default eventHandler(async () => {
  return useResponseSuccess(mockData);
});
