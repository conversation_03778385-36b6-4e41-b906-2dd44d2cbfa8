import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      title: '运营监管',
    },
    name: 'OperationalSupervision',
    path: '/operationalSupervision',
    children: [
      {
        name: 'operationalInspect',
        path: '/operationalSupervision/operationalInspect',
        component: () => import('#/views/operational-supervision/inspect/index.vue'),
        meta: {
          title: '运营检查',
        },
      },
      {
        name: 'operationalSupervise',
        path: '/operationalSupervision/operationalSupervise',
        component: () => import('#/views/operational-supervision/supervise/index.vue'),
        meta: {
          title: '运营监督',
        },
      },
      {
        name: 'operationalEvaluation',
        path: '/operationalSupervision/operationalEvaluation',
        component: () => import('#/views/operational-supervision/evaluation/index.vue'),
        meta: {
          title: '项目后评价',
        },
      },
    ],
  },
];

export default routes;
