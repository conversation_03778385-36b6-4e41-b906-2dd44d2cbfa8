import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: '',
      title: $t('page.project.title'),
    },
    name: 'Project',
    path: '/project',
    children: [
      {
        name: 'ProjectInitiation',
        path: '/project/initiation',
        component: () => import('#/views/project/initiation/index.vue'),
        meta: {
          icon: '',
          title: $t('page.project.initiation'),
        },
      },
      {
        name: 'ProjectPricing',
        path: '/project/pricing',
        component: () => import('#/views/project/pricing/index.vue'),
        meta: {
          icon: '',
          title: $t('page.project.pricing'),
        },
      },
      {
        name: 'ProjectReview',
        path: '/project/review',
        component: () => import('#/views/project/review/index.vue'),
        meta: {
          icon: '',
          title: $t('page.project.review'),
        },
      },
      {
        name: 'ProjectBranch',
        path: '/project/branch',
        component: () => import('#/views/project/branch/index.vue'),
        meta: {
          icon: '',
          title: $t('page.project.branch'),
        },
      },
      {
        name: 'ProjectManager',
        path: '/project/manager',
        component: () => import('#/views/project/manager/index.vue'),
        meta: {
          icon: '',
          title: $t('page.project.manager'),
        },
      },
    ],
  },
];

export default routes;
