import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface PricingInfo {
  /**
   * 区域特性
   */
  areaCharacteristics?: string;
  /**
   * 底层项目
   */
  bottomProject?: string;
  /**
   * 定价综合收益率（%/年）
   */
  comprehensiveRate?: number;
  /**
   * 额度测算结果（元）
   */
  creditCalculateAmount?: number;
  /**
   * 客户类别
   */
  customerCategory?: string;
  /**
   * 客户分级
   */
  customerLevel?: string;
  /**
   * 担保方式描述
   */
  guaranteeMethodDesc?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 行业属性
   */
  industryAttributes?: string;
  /**
   * 定价是否审批
   */
  isReview?: number;
  /**
   * 回款方式
   */
  paymentCollectionMethod?: string;
  /**
   * 基准定价（%）
   */
  pricingBasicRatio?: number;
  /**
   * 授信额度（元）
   */
  pricingCreditAmount?: number;
  /**
   * 授信费率（%）
   */
  pricingCreditRate?: number;
  /**
   * 授信期限（月）
   */
  pricingCreditTerm?: number;
  /**
   * 是否循环额度（授信方式）
   */
  pricingCreditType?: string;
  /**
   * 复核定价方案说明
   */
  pricingDesc?: string;
  /**
   * 复核浮动定价（%）
   */
  pricingFloatingRatio?: number;
  /**
   * 项目定价名称
   */
  pricingName?: string;
  /**
   * 其他情况说明
   */
  pricingOtherDesc?: string;
  /**
   * 项目编号
   */
  projectCode?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 复核是否审批
   */
  recheckIsReview?: number;
  /**
   * 复核审批状态
   */
  recheckReviewStatus?: string;
  /**
   * 复核操作状态
   */
  recheckStatus?: string;
  /**
   * 定价审批状态
   */
  reviewStatus?: string;
  /**
   * 定价操作状态
   */
  status?: string;
  /**
   * 合作企业Code
   */
  targetCompanyCode?: number;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  [property: string]: any;
}

// 获取定价分页列表
export async function getPricingPageListApi(params: PageListParams) {
  return requestClient.get<PricingInfo[]>('/factoring/project/pricing/page', { params });
}

// 添加综合定价
export async function addComprehensivePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/comprehensive/add', data);
}

// 编辑综合定价
export async function editComprehensivePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/comprehensive/edit', data);
}

// 复核综合定价
export async function recheckComprehensivePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/comprehensive/recheck/edit', data);
}

// 添加单一定价
export async function addSinglePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/single/add', data);
}

// 编辑单一定价
export async function editSinglePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/single/edit', data);
}

// 复核单一定价
export async function recheckSinglePricingApi(data: PricingInfo) {
  return requestClient.post<PricingInfo>('/factoring/project/pricing/single/recheck/edit', data);
}

// 获取定价详情
export async function getPricingInfoApi(id: number) {
  return requestClient.post<PricingInfo>(`/factoring/project/pricing/detail/${id}`);
}

// 删除定价
export async function delPricingApi(id: number) {
  return requestClient.post(`/factoring/project/pricing/delete/${id}`);
}
