import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface BranchInfo {
  /**
   * 项目背景说明
   */
  backgroundDesc?: string;
  /**
   * 业务经理ids
   */
  businessUserIds?: string;
  /**
   * 业务经理
   */
  businessUserNames?: string;
  /**
   * 财务经理ids
   */
  financeUserIds?: string;
  /**
   * 财务经理
   */
  financeUserNames?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 是否审批
   */
  isReview?: number;
  /**
   * 上传会议纪要人
   */
  meetingBy?: string;
  /**
   * 上传会议纪要日期
   */
  meetingDate?: Date;
  /**
   * 上传会议纪要文件ID
   */
  meetingFileId?: number;
  /**
   * 上传会议纪要是否审批
   */
  meetingIsReview?: number;
  /**
   * 上传会议纪要审批状态
   */
  meetingReviewStatus?: string;
  /**
   * 上传运营事务告知函人
   */
  notifyFileBy?: string;
  /**
   * 上传运营事务告知函日期
   */
  notifyFileDate?: Date;
  /**
   * 上传运营事务告知函文件ID
   */
  notifyFileId?: number;
  /**
   * 运营经理ids
   */
  operationsUserIds?: string;
  /**
   * 运营经理
   */
  operationsUserNames?: string;
  /**
   * 项目概述说明
   */
  overviewDesc?: string;
  /**
   * 项目编号
   */
  projectCode?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目评审会名称
   */
  projectReviewName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 项目结论说明
   */
  resultDesc?: string;
  /**
   * 评审ID
   */
  reviewId?: number;
  /**
   * 节点会议名称
   */
  reviewNodeName?: string;
  /**
   * 节点类型
   */
  reviewNodeType?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 项目风险点及建议说明
   */
  riskMitigationDesc?: string;
  /**
   * 风控经理ids
   */
  riskUserIds?: string;
  /**
   * 风控经理
   */
  riskUserNames?: string;
  /**
   * 操作状态
   */
  status?: string;
  /**
   * 合作企业编码
   */
  targetCompanyCode?: string;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  [property: string]: any;
}

// 获取党支部会分页列表
export async function getBranchPageListApi(params: PageListParams) {
  return requestClient.get<BranchInfo[]>('/factoring/project/meeting/branch/page', { params });
}

// 添加党支部会
export async function addBranchApi(data: BranchInfo) {
  return requestClient.post<BranchInfo>('/factoring/project/meeting/branch/add', data);
}

// 编辑党支部会
export async function editBranchApi(data: BranchInfo) {
  return requestClient.post<BranchInfo>('/factoring/project/meeting/branch/edit', data);
}

// 获取党支部会详情
export async function getBranchInfoApi(id: number) {
  return requestClient.get(`/factoring/project/meeting/branch/detail/${id}`);
}

// 删除党支部会
export async function delBranchApi(id: number) {
  return requestClient.post(`/factoring/project/meeting/branch/delete/${id}`);
}

// 上传会议纪要
export async function branchMeetingUploadApi(data: { fileId: number; id: number }) {
  return requestClient.post('/factoring/project/meeting/branch/upload/meeting', data);
}
