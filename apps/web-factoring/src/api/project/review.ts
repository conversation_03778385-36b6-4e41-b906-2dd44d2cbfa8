import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface ReviewInfo {
  /**
   * 项目背景说明
   */
  backgroundDesc?: string;
  /**
   * 业务经理ids
   */
  businessUserIds?: string;
  /**
   * 业务经理
   */
  businessUserNames?: string;
  /**
   * 财务经理ids
   */
  financeUserIds?: string;
  /**
   * 财务经理
   */
  financeUserNames?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 是否审批
   */
  isReview?: number;
  /**
   * 上传会议纪要人
   */
  meetingBy?: string;
  /**
   * 上传会议纪要日期
   */
  meetingDate?: Date;
  /**
   * 上传会议纪要文件ID
   */
  meetingFileId?: number;
  /**
   * 上传会议纪要是否审批
   */
  meetingIsReview?: number;
  /**
   * 上传会议纪要审批状态
   */
  meetingReviewStatus?: string;
  /**
   * 上传运营事务告知函人
   */
  notifyFileBy?: string;
  /**
   * 上传运营事务告知函日期
   */
  notifyFileDate?: Date;
  /**
   * 上传运营事务告知函文件ID
   */
  notifyFileId?: number;
  /**
   * 运营经理ids
   */
  operationsUserIds?: string;
  /**
   * 运营经理
   */
  operationsUserNames?: string;
  /**
   * 项目概述说明
   */
  overviewDesc?: string;
  /**
   * 项目编号
   */
  projectCode?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目评审会名称
   */
  projectReviewName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 项目结论说明
   */
  resultDesc?: string;
  /**
   * 评审ID
   */
  reviewId?: number;
  /**
   * 节点会议名称
   */
  reviewNodeName?: string;
  /**
   * 节点类型
   */
  reviewNodeType?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 项目风险点及建议说明
   */
  riskMitigationDesc?: string;
  /**
   * 风控经理ids
   */
  riskUserIds?: string;
  /**
   * 风控经理
   */
  riskUserNames?: string;
  /**
   * 操作状态
   */
  status?: string;
  /**
   * 合作企业编码
   */
  targetCompanyCode?: string;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  [property: string]: any;
}

// 获取评审会分页列表
export async function getReviewPageListApi(params: PageListParams) {
  return requestClient.get<ReviewInfo[]>('/factoring/project/meeting/review/page', { params });
}

// 添加评审会
export async function addReviewApi(data: ReviewInfo) {
  return requestClient.post<ReviewInfo>('/factoring/project/meeting/review/add', data);
}

// 编辑评审会
export async function editReviewApi(data: ReviewInfo) {
  return requestClient.post<ReviewInfo>('/factoring/project/meeting/review/edit', data);
}

// 获取评审会详情
export async function getReviewInfoApi(id: number) {
  return requestClient.get(`/factoring/project/meeting/review/detail/${id}`);
}

// 删除评审会
export async function delReviewApi(id: number) {
  return requestClient.post(`/factoring/project/meeting/review/delete/${id}`);
}

// 上传评审会议纪要
export async function reviewMeetingUploadApi(data: { fileId: number; id: number }) {
  return requestClient.post('/factoring/project/meeting/review/upload/meeting', data);
}

// 上传运营事务告知函
export async function reviewNotifyUploadApi(data: { fileId: number; id: number }) {
  return requestClient.post('/factoring//project/meeting/review/upload/notify', data);
}
