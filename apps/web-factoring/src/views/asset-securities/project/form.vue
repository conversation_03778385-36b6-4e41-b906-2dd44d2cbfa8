<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { AbsProjectInfo } from '#/api';

import { computed, ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { defaultsDeep } from '@vben/utils';

import { message } from 'ant-design-vue';

import { addAbsProjectApi, editAbsProjectApi, infoAbsProjectApi } from '#/api';

// 根据接口定义初始化信息
const defaultForm: Partial<AbsProjectInfo> = {
  projectCode: '',
  projectName: '',
  originalBeneficiaryName: '',
  originalBeneficiaryCode: '',
  status: '',
  reviewStatus: '',
  establishDate: undefined,
  absProjectSummary: '',
  legalRepresentative: '',
  registeredCapitalWan: 0,
  businessScope: '',
  planManager: '',
  qualificationCertificateNo: '',
  projectManager: '',
  custodianBank: '',
  ratingAgency: '',
  lawFirm: '',
  trancheRatio: '',
  trancheInterestRates: '',
  creditEnhancementMethod: '',
  paymentWaterfall: '',
  paymentFrequency: '',
  assetTransferMethod: '',
  issuanceMethod: '',
  legalBasis: '',
  approvalFilingSummary: '',
  creditRiskSummary: '',
  marketRiskSummary: '',
  operationalRiskSummary: '',
};

const projectInfo = ref<Partial<AbsProjectInfo>>(defaultsDeep(defaultForm));
const colSpan = COL_SPAN_PROP;
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
// 验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入ABS项目名称', trigger: 'blur' }],
  originalBeneficiaryName: [{ required: true, message: '请输入原始受益人', trigger: 'blur' }],
  originalBeneficiaryCode: [{ required: true, message: '请输入统一信用代码', trigger: 'blur' }],
  establishDate: [{ required: false, message: '请选择立项日期', trigger: 'change' }],
  legalRepresentative: [{ required: false, message: '请输入法定代表人', trigger: 'blur' }],
  registeredCapitalWan: [{ required: false, message: '请输入注册资本', trigger: 'blur' }],
  businessScope: [{ required: false, message: '请输入经营范围', trigger: 'blur' }],
  planManager: [{ required: false, message: '请输入计划管理人', trigger: 'blur' }],
  qualificationCertificateNo: [{ required: false, message: '请输入资格证书编号', trigger: 'blur' }],
  projectManager: [{ required: false, message: '请输入项目负责人', trigger: 'blur' }],
  custodianBank: [{ required: false, message: '请输入托管银行', trigger: 'blur' }],
  ratingAgency: [{ required: false, message: '请输入评级机构', trigger: 'blur' }],
  lawFirm: [{ required: false, message: '请输入律师事务所', trigger: 'blur' }],
  trancheRatio: [{ required: false, message: '请输入优先级/次级比例', trigger: 'blur' }],
  trancheInterestRates: [{ required: false, message: '请输入各层级利率', trigger: 'blur' }],
  creditEnhancementMethod: [{ required: false, message: '请输入信用增级方式', trigger: 'blur' }],
  paymentWaterfall: [{ required: false, message: '请输入分配顺序', trigger: 'blur' }],
  paymentFrequency: [{ required: false, message: '请输入支付频率', trigger: 'blur' }],
  assetTransferMethod: [{ required: false, message: '请输入基础资产转让方式', trigger: 'blur' }],
  issuanceMethod: [{ required: false, message: '请输入证券发行方式', trigger: 'blur' }],
  legalBasis: [{ required: false, message: '请输入法律法规依据', trigger: 'blur' }],
  approvalFilingSummary: [{ required: false, message: '请输入审批备案情况', trigger: 'blur' }],
  creditRiskSummary: [{ required: false, message: '请输入信用风险评估', trigger: 'blur' }],
  marketRiskSummary: [{ required: false, message: '请输入市场风险评估', trigger: 'blur' }],
  operationalRiskSummary: [{ required: false, message: '请输入操作风险评估', trigger: 'blur' }],
};
const title = computed(() => {
  return projectInfo.value.id ? '编辑ABS项目' : '新增ABS项目';
});

const init = async (data: AbsProjectInfo) => {
  if (data.id) {
    projectInfo.value = await infoAbsProjectApi({ id: data.id as string });
  }
};

const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  changeOkLoading(true);
  let api = addAbsProjectApi;
  if (projectInfo.value.id) {
    api = editAbsProjectApi;
  }
  try {
    const res = await api(projectInfo.value as AbsProjectInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="formRef" :model="projectInfo" :rules="rules" v-bind="formProp" class="mt-5">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="ABS项目名称" name="projectName">
              <a-input v-model:value="projectInfo.projectName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="ABS项目编号" name="projectCode">
              <a-input v-model:value="projectInfo.projectCode" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="立项日期" name="establishDate">
              <a-date-picker v-model:value="projectInfo.establishDate" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item
              label="ABS项目简介"
              name="absProjectSummary"
              v-bind="{ labelCol: { span: 3 }, wrapperCol: { span: 21 } }"
            >
              <a-textarea
                v-model:value="projectInfo.absProjectSummary"
                :rows="4"
                placeholder="对项目的总体情况进行简要描述，包括项目背景、目的、预期效果等。"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <BasicCaption content="参与主体：原始受益人" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="原始受益人" name="originalBeneficiaryName">
              <a-input v-model:value="projectInfo.originalBeneficiaryName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="统一社会信用代码" name="originalBeneficiaryCode">
              <a-input v-model:value="projectInfo.originalBeneficiaryCode" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="法定代表人" name="legalRepresentative">
              <a-input v-model:value="projectInfo.legalRepresentative" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="注册资本（万元）" name="registeredCapitalWan">
              <a-input-number v-model:value="projectInfo.registeredCapitalWan" :controls="false" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="经营范围" name="businessScope">
              <a-textarea
                v-model:value="projectInfo.businessScope"
                :rows="4"
                placeholder="明确公司的业务范围，以确定其开展保理业务的合法性和专业性。"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <BasicCaption content="参与主体：计划管理人" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="计划管理人" name="planManager">
              <a-input v-model:value="projectInfo.planManager" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="资格证书编号" name="qualificationCertificateNo">
              <a-input v-model:value="projectInfo.qualificationCertificateNo" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目负责人" name="projectManager">
              <a-input v-model:value="projectInfo.projectManager" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="参与主体：其他中介机构信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="托管银行" name="custodianBank">
              <a-input v-model:value="projectInfo.custodianBank" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="评级机构" name="ratingAgency">
              <a-input v-model:value="projectInfo.ratingAgency" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="律师事务所" name="lawFirm">
              <a-input v-model:value="projectInfo.lawFirm" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="会计师事务所" name="accountingFirm">
              <a-input v-model:value="projectInfo.accountingFirm" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="交易结构：证券分层" />
        <a-row class="mt-5">
          <a-col :span="24">
            <a-form-item label="优先级/次级比例" name="trancheRatio">
              <a-textarea
                v-model:value="projectInfo.trancheRatio"
                :rows="4"
                placeholder="说明优先级证券和次级证券在发行规模中的占比，用于确定不同层级证券的风险和收益分配。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="各层级利率" name="trancheInterestRates">
              <a-textarea
                v-model:value="projectInfo.trancheInterestRates"
                :rows="4"
                placeholder="分别明确优先级、次级等不同层级证券的预期收益率。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="信用增级方式" name="creditEnhancementMethod">
              <a-textarea
                v-model:value="projectInfo.creditEnhancementMethod"
                :rows="4"
                placeholder="如内部信用增级（如超额抵押、储备金账户等）或外部信用增级（如担保机构提供担保等）的具体方式和相关信息。"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="交易结构：现金流分配机构" />
        <a-row class="mt-5">
          <a-col :span="24">
            <a-form-item label="分配顺序" name="paymentWaterfall">
              <a-textarea
                v-model:value="projectInfo.paymentWaterfall"
                :rows="4"
                placeholder="明确在收到基础资产现金流后，按照何种顺序进行资金分配，如先支付税费、再支付优先级证券本息、然后支付次级证券收益等。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="支付频率" name="paymentFrequency">
              <a-textarea
                v-model:value="projectInfo.paymentFrequency"
                :rows="4"
                placeholder="说明是按季度、半年还是年度等频率进行收益支付。"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="交易结构：交易流程" />
        <a-row class="mt-5">
          <a-col :span="24">
            <a-form-item label="基础资产转让方式" name="assetTransferMethod">
              <a-textarea
                v-model:value="projectInfo.assetTransferMethod"
                :rows="4"
                placeholder="是真实出售还是有其他特殊约定的转让方式，以及相关的法律手续和文件要求。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="证券发行方式" name="issuanceMethod">
              <a-textarea
                v-model:value="projectInfo.issuanceMethod"
                :rows="4"
                placeholder="是公开发行还是私募发行，发行的对象范围、发行时间安排等相关信息。"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="项目合规性审查" />
        <a-row class="mt-5">
          <a-col :span="24">
            <a-form-item label="法律法规依据" name="legalBasis">
              <a-textarea
                v-model:value="projectInfo.legalBasis"
                :rows="4"
                placeholder="说明项目所依据的相关法律法规，如《合同法》《证券法》以及商业保理相关的监管规定等。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="审批备案情况" name="approvalFilingSummary">
              <a-textarea
                v-model:value="projectInfo.approvalFilingSummary"
                :rows="4"
                placeholder="项目是否已获得相关监管部门的审批或备案，以及审批或备案的文号、时间等信息"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="项目风险评审" />
        <a-row class="mt-5">
          <a-col :span="24">
            <a-form-item label="信用风险评估" name="creditRiskSummary">
              <a-textarea
                v-model:value="projectInfo.creditRiskSummary"
                :rows="4"
                placeholder="对债务人的信用状况进行评估的结果，包括信用评级、违约概率等相关指标。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="市场风险评估" name="marketRiskSummary">
              <a-textarea
                v-model:value="projectInfo.marketRiskSummary"
                :rows="4"
                placeholder="分析市场因素（如利率波动、行业竞争等）对基础资产现金流和证券收益的影响程度。"
              />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="操作风险评估" name="operationalRiskSummary">
              <a-textarea
                v-model:value="projectInfo.operationalRiskSummary"
                :rows="4"
                placeholder="对项目在运营管理过程中可能面临的操作风险（如合同管理风险、资金划转风险等）进行评估的结果和应对措施。"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicPopup>
</template>
