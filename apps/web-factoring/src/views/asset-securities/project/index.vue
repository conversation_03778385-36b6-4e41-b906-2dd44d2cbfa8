<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AbsProjectInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delAbsProjectApi, getAbsProjectPageListApi } from '#/api';

import Form from './form.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: 'ABS项目名称',
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: 'ABS项目编号',
    },
    {
      component: 'Input',
      fieldName: 'originalBeneficiaryName',
      label: '原始受益人',
    },
    {
      component: 'Input',
      fieldName: 'planManager',
      label: '计划管理人',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewRemark',
      label: '立项结果',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
  ],
  fieldMappingTime: [['establishDate', ['establishDateStart', 'establishDateEnd'], 'x']],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'projectCode', title: 'ABS项目编号' },
    { field: 'projectName', title: 'ABS项目名称' },
    { field: 'originalBeneficiaryName', title: '原始受益人' },
    { field: 'planManager', title: '计划管理人' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: '',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: '',
        },
      },
    },
    {
      field: 'reviewRemark',
      title: '立项结果',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: '',
        },
      },
    },
    { field: 'establishDate', title: '立项日期', formatter: 'formatDate' },
  ],
  height: 'auto',
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async ({ page }, formValues) => {
        return await getAbsProjectPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: AccessInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: AbsProjectInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const del = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该ABS项目，是否继续？',
    async onOk() {
      await delAbsProjectApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
          <a-button type="primary" danger @click="del">
            {{ $t('base.del') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
