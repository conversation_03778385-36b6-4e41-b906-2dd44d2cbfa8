<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref } from 'vue';

import { Page } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Select, Space, TypographyLink } from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {} from '#/api';

// 定义模态框显示状态
const modalVisible = ref(false);

const labelCol = { style: { width: '150px' } };

// 定义验证规则
const financeCompareRules: Record<string, Rule[]> = {
  company: [{ required: true, message: '请选择企业', trigger: 'change' }],
  timeRange: [{ required: true, message: '请选择财报对比时间', trigger: 'change' }],
};
// 定义表单数据
const financeCompareForm = ref<{
  company: string;
  productName: string;
  receiptDate: string; // 添加结清日期字段
  reportFile?: File; // 可选的上传文件字段
  timeRange: [string, string];
}>({
  company: '',
  timeRange: ['', ''],
  productName: '',
  receiptDate: '', // 初始化结清日期
  reportFile: undefined, // 初始化上传文件
});

// 定义企业选项
const companyOptions = ref([
  { label: '企业A', value: 'companyA' },
  { label: '企业B', value: 'companyB' },
]);
// 修复表单字段与后端接口匹配的bug
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目后评价名称',
    },
    {
      component: 'RangePicker',
      fieldName: 'businessDate', // 修复字段名：productName -> businessDate
      label: '结清日期',
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

// 修复表格列显示和数据绑定的bug
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'projectCode', title: '项目后评价名称' },
    { field: 'businessStructure', title: '项目名称' },
    { field: 'projectModel', title: '结清日期' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getProjectPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 打开模态框方法
const add = () => {
  modalVisible.value = true;
};

// 定义正确的类型
interface ProjectItem {
  id: number;
  projectCode: string;
  projectName: string;
  businessStructure: string;
  projectModel: string;
  supplierCompanyName: string;
  purchaserCompanyName: string;
  executorCompanyName: string;
  status: string; // 修复字段名
  approvalStatus: string;
  businessManagerName: string;
  businessDate: string;
  createTime: string;
  createBy: string;
  createDeptName: string;
}

const edit = (_row: ProjectItem) => {};

const del = async (_row: ProjectItem) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      try {
        // await projectDeleteApi(row.id); // 恢复删除API调用
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch {
        // message.error('删除失败: ' + error.message);
      }
    },
  });
};
const detail = (_row: ProjectItem) => {};

const upLoadReport = () => {};

// 确认方法
const confirmFinanceCompare = async () => {
  modalVisible.value = false;
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)"> 重新上传 </TypographyLink>
          <TypographyLink type="danger" @click="del(row)"> 下载 </TypographyLink>
          <TypographyLink @click="detail(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <AntdModal
      title="上传项目后评价"
      :open="modalVisible"
      @cancel="() => (modalVisible = false)"
      @ok="confirmFinanceCompare"
    >
      <Form
        :label-col="labelCol"
        :wrapper-col="{ span: 30 }"
        :model="financeCompareForm"
        class="mt-5 px-5"
        :rules="financeCompareRules"
      >
        <Row class="mt-5">
          <Col :span="24">
            <FormItem label="上传项目后评价" name="company">
              <Button class="mr-2" type="primary" @click="upLoadReport"> 点击上传 </Button>
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="关联项目" name="company">
              <Select v-model:value="financeCompareForm.company" :options="companyOptions" />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="结清日期" name="company">
              <DatePicker v-model:value="financeCompareForm.receiptDate" value-format="YYYY-MM-DD" />
            </FormItem>
          </Col>
          <Col :span="24">
            <FormItem label="项目后评价名称" name="timeRange">
              <Input v-model:value="financeCompareForm.productName" placeholder="请输入项目后评价名称" />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </AntdModal>
  </Page>
</template>

<style></style>
