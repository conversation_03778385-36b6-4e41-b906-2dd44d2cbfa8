<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, onMounted, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, Modal, Row, Select, Textarea } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {} from '#/api';

import FinancialReportTable from './components/FinancialReportTable.vue';
import ScoreTable from './components/ScoreTable.vue';

const emit = defineEmits(['register', 'ok']);
const creditorTableData = ref([
  {
    projectCode: '001',
    projectName: '项目检查1',
    businessStructure: '已完成',
    projectModel: '一切正常，无异常情况',
  },
  {
    projectCode: '002',
    projectName: '项目检查2',
    businessStructure: '进行中',
    projectModel: '部分环节未完成，需跟进',
  },
  {
    projectCode: '003',
    projectName: '项目检查3',
    businessStructure: '未开始',
    projectModel: '准备工作尚未完成',
  },
]);
const { getDictList } = useDictStore();

// 根据接口定义初始化产品信息
// 移除找不到类型定义的类型注解
const defaultForm = {
  project: {
    projectName: '',
    businessStructure: undefined,
    projectModel: undefined,
    executorCompanyId: undefined,
    executorCompanyName: '',
    businessManagerId: undefined,
    businessManagerName: undefined,
    supplierCompanyName: undefined,
    purchaserCompanyName: undefined,
    creditCompanyName: undefined,
    isDeposit: undefined,
    isGoodsControlMode: undefined,
    serviceFeeRate: '',
    paymentTermDays: '',
    expectedProjectScale: '',
    businessDate: undefined,
    estimatedEndDate: undefined,
    purchaseMode: undefined,
    projectCode: '',
    remarks: '',
  },
  attachmentFiles: [],
};

// 移除找不到类型定义的类型注解
const detailForm = reactive(defaultsDeep(defaultForm));
const colSpan = { md: 12, sm: 24 };
// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  projectName: [{ required: true, message: '请输入项目名称', trigger: 'change' }],
  businessStructure: [{ required: true, message: '请选择业务结构', trigger: 'change' }],
  projectModel: [{ required: true, message: '请选择项目模式', trigger: 'change' }],
  executorCompanyName: [{ required: true, message: '请输入贸易执行企业', trigger: 'change' }],
  businessManagerName: [{ required: true, message: '请选择业务负责人', trigger: 'change' }],
  supplierCompanyName: [{ required: true, message: '请选择上游企业', trigger: 'change' }],
  purchaserCompanyName: [{ required: true, message: '请选择下游企业', trigger: 'change' }],
  creditCompanyName: [{ required: true, message: '请选择终端企业', trigger: 'change' }],
  projectAddress: [{ required: true, message: '请输入项目地点', trigger: 'change' }],
  isDeposit: [{ required: true, message: '请选择是否有保证金', trigger: 'change' }],
  purchaseMode: [{ required: true, message: '请选择采购模式', trigger: 'change' }],
  isGoodsControlMode: [{ required: true, message: '请选择是否是控货模式', trigger: 'change' }],
  serviceFeeRate: [{ required: true, message: '请输入服务费率', trigger: 'change' }],
  paymentTermDays: [{ required: true, message: '请输入账期', trigger: 'change' }],
};
const title = computed(() => {
  return detailForm.project.id ? '编辑项目' : '新增项目';
});

const formRef = ref();
const save = async () => {
  await formRef.value.validate();
  try {
    emit('ok', '');
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner();
const labelCol = { style: { width: '150px' } };

// 移除未使用的 gridOptions 变量
// const gridOptions: VxeTableGridOptions = {
//   ...
// };

// 动态表头数据
const dynamicHeaders = ref<string[]>([]);
// 表格数据
const tableData = ref<Record<string, any>[]>([]);

// 模拟 getFinancialReportDataApi 函数
const getFinancialReportDataApi = async () => {
  // 生成假表头数据
  const headers = Array.from({ length: 5 }, (_, index) => `2025年${index + 1}月`);

  // 生成假表格数据，处理未使用的 rowIndex 参数
  const data = Array.from({ length: 3 }, () => {
    const rowData: Record<string, any> = {
      result: Math.floor(Math.random() * 1000), // 第一列经营成果（万元）
    };
    headers.forEach((header) => {
      rowData[header.replaceAll(/[年月]/g, '')] = Math.floor(Math.random() * 500);
    });
    return rowData;
  });

  return {
    headers,
    data,
  };
};

// 获取数据
const fetchData = async () => {
  try {
    const res = await getFinancialReportDataApi();
    dynamicHeaders.value = res.headers; // 假设接口返回的表头数据在 headers 字段
    tableData.value = res.data; // 假设接口返回的表格数据在 data 字段
  } catch (error) {
    // 将 console.log 替换为 console.error
    console.error('获取财务报告数据失败', error);
  }
};

onMounted(() => {
  fetchData();
});
const gridPerformanceTable: VxeTableGridOptions = {
  pagerConfig: {
    enabled: false,
  },
  columns: [
    { field: 'projectCode', title: '还本付息日' },
    { field: 'projectName', title: '应还款金额（元）' },
    { field: 'businessStructure', title: '实际回款日' },
    { field: 'projectModel', title: '实际回款金额（元）' },
    { field: 'supplierCompanyName', title: '是否逾期' },
  ],

  // height: 'auto',
  proxyConfig: {
    // ajax: {
    //   query: async ({ page }, formValues) => {
    //     return await getProjectPageApi({
    //       current: page.currentPage,
    //       size: page.pageSize,
    //       ...formValues,
    //     });
    //   },
    // },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
// 移除未使用的 gridApiSupplier 变量
const [PerformanceTable] = useVbenVxeGrid({ gridOptions: gridPerformanceTable });

// const loading = ref<boolean>(false);
// 定义企业选项
const companyOptions = ref([
  { label: '企业A', value: 'companyA' },
  { label: '企业B', value: 'companyB' },
]);

// 定义模态框显示状态
const modalVisible = ref(false);

// 定义表单数据
const financeCompareForm = ref<{
  company: string;
  timeRange: [string, string];
}>({
  company: '',
  timeRange: ['', ''],
});

// 定义验证规则
const financeCompareRules: Record<string, Rule[]> = {
  company: [{ required: true, message: '请选择企业', trigger: 'change' }],
  timeRange: [{ required: true, message: '请选择财报对比时间', trigger: 'change' }],
};

// 确认方法
const confirmFinanceCompare = async () => {
  // 这里可以添加表单验证逻辑
  // 将 console.log 替换为 console.warn
  console.warn('确认选择', financeCompareForm.value);
  modalVisible.value = false;
};

// 打开模态框方法
const add = () => {
  modalVisible.value = true;
};
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <Form
      ref="formRef"
      :colon="false"
      :model="detailForm.project"
      :rules="rules"
      :label-col="labelCol"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <!-- 基本信息 -->
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="选择项目" name="businessStructure">
            <!-- 为 options 添加默认值 -->
            <Select v-model:value="detailForm.project.businessStructure" :options="[]" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="运营分析报告编号" name="projectName">
            <Input v-model:value="detailForm.project.projectName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="运营分析报告名称" name="projectName">
            <Input v-model:value="detailForm.project.projectName" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="分析季度" name="projectName">
            <Input v-model:value="detailForm.project.projectName" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="一、基本情况" />
      <Row class="mt-5">
        <Col v-bind="colSpan">
          <FormItem label="检查人员" name="isGoodsControlMode">
            <!-- 为 options 添加默认值 -->
            <Select v-model:value="detailForm.project.isGoodsControlMode" :options="[]" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="检查方式" name="isGoodsControlMode">
            <Select
              v-model:value="detailForm.project.isGoodsControlMode"
              :options="getDictList('isGoodsControlMode')"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="检查日期" name="receiptDate">
            <DatePicker v-model:value="detailForm.receiptDate" value-format="YYYY-MM-DD" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="项目类型" name="isGoodsControlMode">
            <Select
              v-model:value="detailForm.project.isGoodsControlMode"
              :options="getDictList('isGoodsControlMode')"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="增信措施" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入增信措施" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="投放日期" name="isGoodsControlMode">
            <DatePicker v-model:value="detailForm.receiptDate" value-format="YYYY-MM-DD" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="投放金额（元）" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入投放金额（元）" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="业务金额（元）" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入业务金额（元）" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="内含报酬率（XIRR）" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入内含报酬率（XIRR）" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="保理类型" name="isGoodsControlMode">
            <Select
              v-model:value="detailForm.project.isGoodsControlMode"
              :options="getDictList('isGoodsControlMode')"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="合同开始日期" name="isGoodsControlMode">
            <DatePicker v-model:value="detailForm.receiptDate" value-format="YYYY-MM-DD" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="合同结束日期" name="isGoodsControlMode">
            <DatePicker v-model:value="detailForm.receiptDate" value-format="YYYY-MM-DD" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="债权人" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入债权人" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="债务人" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入债务人" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="担保人" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入担保人" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="应收账款概况" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入应收账款概况" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="风险管理要求落实情况" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入风险管理要求落实情况" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="资金流向" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入资金流向" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="底层项目情况" name="isGoodsControlMode">
            <Input v-model:value="detailForm.productName" placeholder="请输入底层项目情况" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="二、本季度回款履约情况" />
      <PerformanceTable />
      <BasicCaption content="三、<债权人>投后管理情况" />
      <ScoreTable v-model="creditorTableData" />
      <div class="mt-5 flex justify-end">
        <Button class="mr-2" type="primary" @click="add">选择财务数据对比</Button>
      </div>
      <FinancialReportTable :dynamic-headers="dynamicHeaders" :table-data="tableData" />
      <Row class="mt-5">
        <Col :span="24">
          <FormItem label="财务分析" name="isGoodsControlMode">
            <Textarea v-model:value="detailForm.operationalRiskSummary" :rows="4" placeholder="请输入财务分析" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="四、<债务人>投后管理情况" />
      <ScoreTable v-model="creditorTableData" />
      <BasicCaption content="五、<担保人>投后管理情况" />
      <ScoreTable v-model="creditorTableData" />
      <div class="mt-5 flex justify-end">
        <Button class="mr-2" type="primary" @click="add">选择财务数据对比</Button>
      </div>
      <FinancialReportTable :dynamic-headers="dynamicHeaders" :table-data="tableData" />
      <Row class="mt-5">
        <Col :span="24">
          <FormItem label="财务分析" name="isGoodsControlMode">
            <Textarea v-model:value="detailForm.operationalRiskSummary" :rows="4" placeholder="请输入财务分析" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="六、抵质押物检查情况" />
      <Row class="mt-5">
        <Col :span="24">
          <FormItem label="存续情况" name="isGoodsControlMode">
            <Textarea v-model:value="detailForm.operationalRiskSummary" :rows="4" placeholder="请输入存续情况" />
          </FormItem>
        </Col>
      </Row>
      <BasicCaption content="七、检查结果" />
      <Row class="mt-5">
        <Col :span="24">
          <FormItem label="其他情况" name="isGoodsControlMode">
            <Textarea v-model:value="detailForm.operationalRiskSummary" :rows="4" placeholder="请输入其他情况" />
          </FormItem>
        </Col>
        <Col :span="24">
          <FormItem label="检查结论" name="isGoodsControlMode">
            <Textarea v-model:value="detailForm.operationalRiskSummary" :rows="4" placeholder="请输入检查结论" />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="资产分类" name="isGoodsControlMode">
            <Select
              v-model:value="detailForm.project.isGoodsControlMode"
              :options="getDictList('isGoodsControlMode')"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="运营人员" name="isGoodsControlMode">
            <Select
              v-model:value="detailForm.project.isGoodsControlMode"
              :options="getDictList('isGoodsControlMode')"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="复核人员" name="isGoodsControlMode">
            <Select
              v-model:value="detailForm.project.isGoodsControlMode"
              :options="getDictList('isGoodsControlMode')"
            />
          </FormItem>
        </Col>
        <Col v-bind="colSpan">
          <FormItem label="报告日期" name="isGoodsControlMode">
            <DatePicker v-model:value="detailForm.receiptDate" value-format="YYYY-MM-DD" />
          </FormItem>
        </Col>
      </Row>
    </Form>
    <Modal
      title="选择财务数据对比"
      :open="modalVisible"
      @cancel="() => (modalVisible = false)"
      @ok="confirmFinanceCompare"
    >
      <Form :label-col="labelCol" :wrapper-col="{ span: 30 }" :model="financeCompareForm" :rules="financeCompareRules">
        <Row class="mt-5">
          <Col :span="24">
            <FormItem label="选择企业" name="company">
              <Select v-model:value="financeCompareForm.company" :options="companyOptions" />
            </FormItem>
          </Col>
        </Row>
        <Row class="mt-5">
          <Col :span="24">
            <FormItem label="选择财报对比时间" name="timeRange">
              <DatePicker.RangePicker style="width: 100%" v-model:value="financeCompareForm.timeRange" picker="month" />
            </FormItem>
          </Col>
        </Row>
      </Form>
    </Modal>
  </BasicPopup>
</template>

<style scoped></style>
