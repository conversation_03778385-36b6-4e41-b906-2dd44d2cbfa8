<script setup lang="ts">
import type { VxeGridProps } from '#/adapter/vxe-table';

import { defineProps, ref, watch } from 'vue';

import { Input, Radio, RadioGroup } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

const props = defineProps<{
  modelValue: Record<string, any>[]; // 接收外部传入的表格数据
}>();

// const emit = defineEmits(['update:modelValue']);

const tableData = ref(props.modelValue);

// 监听 props.modelValue 变化，更新 tableData
watch(
  () => props.modelValue,
  (newValue) => {
    tableData.value = newValue;
  },
  { deep: true },
);

// 监听数据变化，更新父组件的数据
// const updateData = () => {
//   emit('update:modelValue', tableData.value);
// };

const gridOptions: VxeGridProps<any> = {
  pagerConfig: {
    enabled: false,
  },
  columns: [
    { field: 'projectCode', title: '' },
    { field: 'projectName', title: '检查内容' },
    {
      field: 'businessStructure',
      title: '状态',
      slots: { default: 'customStatusComponent' },
    },
    {
      field: 'projectModel',
      title: '情况描述',
      slots: { default: 'customDescriptionComponent' },
    },
  ],
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid] = useVbenVxeGrid({ gridOptions });

const init = () => {
  console.error('表格数据:', tableData.value); // 改为 console.error
  console.error('列配置:', gridOptions.columns); // 改为 console.error
};
init();
</script>

<template>
  <Grid>
    <template #customStatusComponent="{ row }">
      <RadioGroup>
        <Radio :value="row.businessStructure">无</Radio>
        <Radio :value="row.businessStructure">有</Radio>
      </RadioGroup>
    </template>
    <template #customDescriptionComponent="{ row }">
      <Input v-model:value="row.projectModel" />
    </template>
  </Grid>
</template>

<style scoped>
/* 确保表格有最小高度 */
.vxe-table {
  min-height: 200px;
}
</style>
