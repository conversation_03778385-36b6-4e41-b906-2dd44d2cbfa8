<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InitiationInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent, Page, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message, Select } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delInitiationApi, getCompanyListApi, getInitiationPageListApi } from '#/api';

import InitiationEdit from './initiation-edit.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'targetCompanyName',
      label: '投向企业',
    },
    {
      component: 'Select',
      fieldName: 'projectType',
      label: '项目类型',
      componentProps: {
        options: dictStore.getDictList('FCT_PROJECT_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'factoringType',
      label: '保理类型',
      componentProps: {
        options: dictStore.getDictList('FCT_FACTORING_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'targetIndustry',
      label: '投放行业',
      componentProps: {
        options: dictStore.getDictList('FCT_TARGET_INDUSTRY'),
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '操作状态',
      componentProps: {
        options: dictStore.getDictList(''),
      },
    },
    {
      component: 'Select',
      fieldName: 'reviewStatus',
      label: '审批状态',
      componentProps: {
        options: dictStore.getDictList('REVIEW_STATUS'),
      },
    },
    {
      component: 'Input',
      fieldName: 'businessUserNames',
      label: '业务经理',
    },
    {
      component: 'Input',
      fieldName: 'operationsUserNames',
      label: '运营经理',
    },
    {
      component: 'Input',
      fieldName: 'riskUserNames',
      label: '风控经理',
    },
    {
      component: 'Input',
      fieldName: 'financeUserNames',
      label: '财务经理',
    },
    {
      component: 'Input',
      fieldName: 'cityName',
      label: '地市',
    },
    {
      component: 'Input',
      fieldName: 'districtName',
      label: '区县',
    },
  ],
  commonConfig: {
    labelCol: { span: 8 },
    wrapperCol: { span: 16 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px' },
    { field: 'projectCode', title: '项目编号', minWidth: 120 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    {
      field: 'factoringType',
      title: '保理类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_FACTORING_TYPE',
        },
      },
    },
    { field: 'cityName', title: '地市' },
    { field: 'districtName', title: '区县' },
    {
      field: 'targetIndustry',
      title: '投放行业',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_TARGET_INDUSTRY',
        },
      },
    },
    { field: 'targetCompanyCode', title: '合作企业' },
    { field: 'businessUserNames', title: '业务经理' },
    { field: 'operationsUserNames', title: '运营经理' },
    { field: 'riskUserNames', title: '风控经理' },
    { field: 'financeUserNames', title: '财务经理' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: '',
        },
      },
    },
    {
      field: 'reviewStatus',
      title: '审批状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'REVIEW_STATUS',
        },
      },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getInitiationPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  checkboxConfig: {
    showHeader: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: InitiationInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
const edit = (row: InitiationInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const ProjectFormRef = ref();
const add = () => {
  modalApi.open();
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await ProjectFormRef.value.validate();
    await modalApi.close();
    openFormPopup(true, { ...projectForm.value });
  },
  onClosed: () => {
    ProjectFormRef.value.resetFields();
    projectForm.value = {};
  },
});
const projectForm = ref<InitiationInfo>({});
const rules = {
  targetCompanyCode: [{ required: true, message: '请选择合作企业' }],
  projectType: [{ required: true, message: '请选择项目类型' }],
};
const del = () => {
  const res = gridApi.grid.getCheckboxRecords(true);
  if (res.length === 0) {
    message.error('请选择数据');
    return false;
  }
  const id = res[0].id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该立项，是否继续？',
    async onOk() {
      await delInitiationApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
          <a-button type="primary" danger @click="del">
            {{ $t('base.del') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <InitiationEdit @register="registerForm" @ok="editSuccess" />
    <Modal title="选择项目类型">
      <a-form ref="ProjectFormRef" :model="projectForm" :rules="rules">
        <a-form-item label="项目类型" name="projectType">
          <a-select v-model:value="projectForm.projectType" :options="dictStore.getDictList('FCT_PROJECT_TYPE')" />
        </a-form-item>
        <a-form-item label="合作企业" name="targetCompanyCode">
          <ApiComponent
            v-model="projectForm.targetCompanyCode as unknown as string"
            :component="Select"
            :api="getCompanyListApi"
            label-field="companyName"
            value-field="companyCode"
            model-prop-name="value"
          />
        </a-form-item>
      </a-form>
    </Modal>
  </Page>
</template>

<style></style>
