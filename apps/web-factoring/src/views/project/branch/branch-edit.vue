<script setup lang="ts">
import type { BranchInfo, ReviewInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message, Select } from 'ant-design-vue';

import { addBranchApi, editBranchApi, getBranchInfoApi, getCompanyListApi } from '#/api';

const emit = defineEmits(['ok', 'register']);
const init = async (data: BranchInfo) => {
  branchForm.value = data.id
    ? await getBranchInfoApi(data.id as number)
    : {
        ...data,
      };
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const FormRef = ref();
const branchForm = ref<BranchInfo>({});
const save = async () => {
  await FormRef.value.validate();
  changeOkLoading(true);
  let api = addBranchApi;
  if (branchForm.value.id) {
    api = editBranchApi;
  }
  try {
    const res = await api(branchForm.value as BranchInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const selectReview = (_value: number, data: ReviewInfo) => {
  branchForm.value.reviewNodeName = `关于【${data.projectName}】的请示`;
  branchForm.value.projectName = data.projectName;
  branchForm.value.projectId = data.projectId;
  branchForm.value.projectBackground = data.projectBackground;
};
const rules = {
  reviewId: [{ required: true, message: '请选择关联项目评审会议', trigger: 'change' }],
  reviewNodeName: [{ required: true, message: '请输入党支部会议名称', trigger: 'blur' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="党支部会议申请" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="branchForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联项目评审会议" name="reviewId">
              <ApiComponent
                v-model="branchForm.reviewId as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="selectReview"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目名称" name="projectName">
              <a-input v-model:value="branchForm.projectName" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="党支部会议名称" name="reviewNodeName">
              <a-input v-model:value="branchForm.reviewNodeName" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目背景" name="projectBackground" v-bind="fullProp">
              <a-textarea v-model:value="branchForm.projectBackground" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目风险点及建议" name="riskMitigationDesc" v-bind="fullProp">
              <a-textarea v-model:value="branchForm.riskMitigationDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目结论" name="resultDesc" v-bind="fullProp">
              <a-textarea v-model:value="branchForm.resultDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
