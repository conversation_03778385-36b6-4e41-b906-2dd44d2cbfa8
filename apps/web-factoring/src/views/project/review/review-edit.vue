<script setup lang="ts">
import type { InitiationInfo, ReviewInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { message, Select } from 'ant-design-vue';

import { addReviewApi, editReviewApi, getCompanyListApi, getReviewInfoApi } from '#/api';

const emit = defineEmits(['ok', 'register']);
const init = async (data: ReviewInfo) => {
  reviewForm.value = data.id
    ? await getReviewInfoApi(data.id as number)
    : {
        ...data,
      };
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const FormRef = ref();
const reviewForm = ref<ReviewInfo>({});
const save = async () => {
  await FormRef.value.validate();
  changeOkLoading(true);
  let api = addReviewApi;
  if (reviewForm.value.id) {
    api = editReviewApi;
  }
  try {
    const res = await api(reviewForm.value as ReviewInfo);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const selectInitiation = (_value: number, data: InitiationInfo) => {
  reviewForm.value.reviewNodeName = `关于【${data.projectName}】的评审`;
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  reviewNodeName: [{ required: true, message: '请输入项目评审会议名称', trigger: 'blur' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="项目评审信息" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="reviewForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联项目" name="projectId">
              <ApiComponent
                v-model="reviewForm.projectId as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目评审会议名称" name="reviewNodeName">
              <a-input v-model:value="reviewForm.reviewNodeName" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目背景" name="projectBackground" v-bind="fullProp">
              <a-textarea v-model:value="reviewForm.projectBackground" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目风险点及建议" name="riskMitigationDesc" v-bind="fullProp">
              <a-textarea v-model:value="reviewForm.riskMitigationDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="项目结论" name="resultDesc" v-bind="fullProp">
              <a-textarea v-model:value="reviewForm.resultDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
