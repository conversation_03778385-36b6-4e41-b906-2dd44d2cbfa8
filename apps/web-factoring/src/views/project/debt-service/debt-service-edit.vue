<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { DebtServiceInfo, InitiationInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { useDictStore } from '@vben/stores';
import { getCombinedErrorMessagesString } from '@vben/utils';

import { message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { addDebtServiceApi, editDebtServiceApi, getCompanyListApi, getDebtServiceInfoApi } from '#/api';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();

const init = async (data: DebtServiceInfo) => {
  debtServiceForm.value = data.id
    ? await getDebtServiceInfoApi(data.id as number)
    : {
        ...data,
      };
  // 确保所有字段都正确初始化
  if (debtServiceForm.value.penaltyType === 'ladder') {
    debtServiceForm.value.penaltySteps = debtServiceForm.value.penaltySteps ?? [];
    await LadderGridApi.grid.reloadData(debtServiceForm.value.penaltySteps ?? []);
  }
  debtServiceForm.value.detailList = debtServiceForm.value.detailList ?? [];
  await CalculationGridApi.grid.reloadData(debtServiceForm.value.detailList ?? []);
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const FormRef = ref();
const debtServiceForm = ref<DebtServiceInfo>({});
const save = async () => {
  await FormRef.value.validate();
  const errMap = await CalculationGridApi.grid.validate(true);
  if (errMap) {
    const errMessage = getCombinedErrorMessagesString(errMap);
    if (errMessage) {
      message.error(errMessage);
      return;
    }
  }
  changeOkLoading(true);
  let api = addDebtServiceApi;
  if (debtServiceForm.value.id) {
    api = editDebtServiceApi;
  }
  const formData = cloneDeep(debtServiceForm.value);
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const selectInitiation = (_value: number, data: InitiationInfo) => {
  debtServiceForm.value.pricingName = `${data.projectName}-定价`;
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  expectedLaunchDate: [{ required: true, message: '请选择预计业务投放日', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择预估最后还款日', trigger: 'change' }],
  financingAmount: [{ required: true, message: '请输入计划融资金额' }],
  gracePeriodDays: [{ required: true, message: '请输入宽限期天数' }],
  gracePeriodRate: [{ required: true, message: '请输入宽限期费率' }],
  penaltyInterestRate: [{ required: true, message: '请输入固定罚息利率' }],
  principalRepaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  interestRepaymentMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
  principalPeriod: [{ required: true, message: '请选择分期还本频次', trigger: 'change' }],
  interestPeriod: [{ required: true, message: '请选择分期还息频次', trigger: 'change' }],
  repayPrincipalDay: [{ required: true, message: '请选择默认当期还本日', trigger: 'change' }],
  repayInterestDay: [{ required: true, message: '请选择默认当期还息日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
const baseGridOptions = {
  showOverflow: true,
  keepSource: true,
  editConfig: {
    trigger: 'click',
    mode: 'row',
    autoClear: false,
  },
  rowConfig: {
    drag: true,
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbarTools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
const ladderGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'daysInfo',
      title: '逾期天数（天）',
      slots: { default: 'edit_days_info' },
    },
    {
      field: 'rate',
      title: '年化罚息利率（%）',
      slots: { default: 'edit_rate' },
    },
  ],
  editRules: {
    rate: [{ required: true, content: '请输入年化罚息利率' }],
    daysInfo: [
      {
        validator({ row }) {
          if (!row.endDays) {
            return new Error('请输入逾期天数');
          }
          return true;
        },
      },
    ],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [LadderGrid, LadderGridApi] = useVbenVxeGrid({
  gridOptions: ladderGridOptions,
});
const addLadder = () => {
  LadderGridApi.grid.insertAt({});
};
const removeLadder = () => {
  const records = LadderGridApi.grid.getCheckboxRecords();
  LadderGridApi.grid.remove(records);
};
const exportData = () => {
  // 导出逻辑实现
};
const calculation = () => {
  // 计算逻辑实现
  CalculationGridApi.grid.insertAt({});
};
const calculationGridOptions = {
  columns: [
    {
      field: 'id',
      title: '还款期数ID',
      minWidth: '100px',
    },
    {
      field: 'repayPeriods',
      title: '还款期数',
      minWidth: '100px',
    },
    {
      field: 'businessType',
      title: '业务类型',
      minWidth: '120px',
    },
    {
      field: 'currentDate',
      title: '当期还本/付息日',
      slots: { default: 'edit_current_date' },
      minWidth: '180px',
    },
    {
      field: 'totalAmount',
      title: '当期净现金流(元)',
      slots: { default: 'edit_total_amount' },
      minWidth: '180px',
    },
    {
      field: 'principalAmount',
      title: '应还本金(元)',
      slots: { default: 'edit_principal_amount' },
      minWidth: '180px',
    },
    {
      field: 'interestAmount',
      title: '应还利息(元)',
      slots: { default: 'edit_interest_amount' },
      minWidth: '180px',
    },
    {
      field: 'serviceAmount',
      title: '应收服务费(元)',
      minWidth: '180px',
    },
    {
      field: 'graceInterestAmount',
      title: '应还宽限期利息(元)',
      minWidth: '180px',
    },
    {
      field: 'overdueInterestAmount',
      title: '应还逾期罚息(元)',
      minWidth: '180px',
    },
  ],
  editRules: {
    currentDate: [{ required: true, content: '请选择当期还本/付息日', trigger: 'change' }],
    totalAmount: [{ required: true, content: '请输入当期净现金流' }],
    principalAmount: [{ required: true, content: '请输入应还本金' }],
    interestAmount: [{ required: true, content: '请输入应还利息' }],
  },
  ...baseGridOptions,
} as VxeTableGridOptions;
const [CalculationGrid, CalculationGridApi] = useVbenVxeGrid({
  gridOptions: calculationGridOptions,
});
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="还本付息计划" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="debtServiceForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联单一项目" name="projectId">
              <ApiComponent
                v-model="debtServiceForm.projectId as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还本付息试算名称" name="calculationName">
              <a-input v-model:value="debtServiceForm.calculationName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="应收账款金额（元）" name="receivableAmount">
              <a-input v-model:value="debtServiceForm.receivableAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="基准定价（%/年）" name="pricingBasicRatio">
              <a-input v-model:value="debtServiceForm.pricingBasicRatio" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="浮动定价（%/年）" name="pricingFloatingRatio">
              <a-input v-model:value="debtServiceForm.pricingFloatingRatio" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目定价综合收益率（%）" name="comprehensiveRate">
              <a-input v-model:value="debtServiceForm.comprehensiveRate" disabled />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="备注" name="remarks" v-bind="fullProp">
              <a-textarea v-model:value="debtServiceForm.remarks" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>

        <BasicCaption content="还本付息方案" />

        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="计划融资金额（元）" name="financingAmount">
              <a-input v-model:value="debtServiceForm.financingAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="融资比例（%）" name="financingRatio">
              <a-input v-model:value="debtServiceForm.financingRatio" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期天数（天）" name="gracePeriodDays">
              <a-input v-model:value="debtServiceForm.gracePeriodDays" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期费率（%/年）" name="gracePeriodRate">
              <a-input v-model:value="debtServiceForm.gracePeriodRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="服务费（元）" name="serviceFeeAmount">
              <a-input v-model:value="debtServiceForm.serviceFeeAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="罚息类型" name="penaltyType">
              <a-radio-group
                v-model:value="debtServiceForm.penaltyType"
                :options="dictStore.getDictList('FCT_PENALTY_TYPE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="debtServiceForm.penaltyType === 'fixed'">
            <a-form-item label="固定罚息利率（%）" name="penaltyInterestRate">
              <a-input v-model:value="debtServiceForm.penaltyInterestRate" />
            </a-form-item>
          </a-col>
        </a-row>
        <LadderGrid v-if="debtServiceForm.penaltyType === 'ladder'">
          <template #toolbarTools>
            <a-space>
              <a-button type="primary" @click="addLadder">新增</a-button>
              <a-button type="primary" danger @click="removeLadder">删除</a-button>
            </a-space>
          </template>
          <template #edit_days_info="{ row }">
            <a-space>
              <a-input v-model:value="row.startDays" disabled />
              <a-input v-model:value="row.endDays" />
            </a-space>
          </template>
          <template #edit_rate="{ row }">
            <a-input v-model:value="row.rate" />
          </template>
        </LadderGrid>
        <BasicCaption content="还本付息试算" />

        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="还本付息计划规划方式" name="planningMethod">
              <a-radio-group
                v-model:value="debtServiceForm.planningMethod"
                :options="dictStore.getDictList('FCT_PLANNING_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="测算综合收益率（%/年）" name="compositeYieldRate">
              <a-input v-model:value="debtServiceForm.compositeYieldRate" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="预计业务投放日" name="expectedLaunchDate">
              <a-date-picker v-model:value="debtServiceForm.expectedLaunchDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="预估最后还款日" name="expectedDueDate">
              <a-date-picker v-model:value="debtServiceForm.expectedDueDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还本方式" name="principalRepaymentMethod">
              <a-select
                v-model:value="debtServiceForm.principalRepaymentMethod"
                :options="dictStore.getDictList('FCT_REPAY_PRINCIPAL_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="还息方式" name="interestRepaymentMethod">
              <a-select
                v-model:value="debtServiceForm.interestRepaymentMethod"
                :options="dictStore.getDictList('FCT_REPAY_INTEREST_METHOD')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="分期还本频次" name="principalPeriod">
              <a-select
                v-model:value="debtServiceForm.principalPeriod"
                :options="dictStore.getDictList('FCT_FREQUENCY')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="分期还息频次" name="interestPeriod">
              <a-select
                v-model:value="debtServiceForm.interestPeriod"
                :options="dictStore.getDictList('FCT_FREQUENCY')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="默认当期还本日" name="repayPrincipalDay">
              <a-select
                v-model:value="debtServiceForm.repayPrincipalDay"
                :options="dictStore.getDictList('FCT_CURRENT_REPAY_DATE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="默认当期还息日" name="repayInterestDay">
              <a-select
                v-model:value="debtServiceForm.repayInterestDay"
                :options="dictStore.getDictList('FCT_CURRENT_REPAY_DATE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="预估计息天数（天）" name="expectedInterestDays">
              <a-input v-model:value="debtServiceForm.expectedInterestDays" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="预估还款期数" name="expectedRepayPeriods">
              <a-input v-model:value="debtServiceForm.expectedRepayPeriods" disabled />
            </a-form-item>
          </a-col>
        </a-row>
        <CalculationGrid>
          <template #toolbarTools>
            <a-space>
              <a-button type="primary" @click="calculation">开始试算</a-button>
              <a-button type="primary" @click="exportData">导出</a-button>
            </a-space>
          </template>
          <template #edit_current_date="{ row }">
            <a-date-picker v-model:value="row.currentDate" value-format="x" class="w-full" />
          </template>
          <template #edit_total_amount="{ row }">
            <a-input v-model:value="row.totalAmount" />
          </template>
          <template #edit_principal_amount="{ row }">
            <a-input v-model:value="row.principalAmount" />
          </template>
          <template #edit_interest_amount="{ row }">
            <a-input v-model:value="row.interestAmount" />
          </template>
        </CalculationGrid>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
