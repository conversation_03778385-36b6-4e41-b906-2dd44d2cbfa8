<script setup lang="ts">
import type { InitiationInfo, PricingInfo } from '#/api';

import { ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import {
  addComprehensivePricingApi,
  addSinglePricingApi,
  editComprehensivePricingApi,
  editSinglePricingApi,
  getCompanyListApi,
  getPricingInfoApi,
} from '#/api';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();

const init = async (data: PricingInfo) => {
  pricingForm.value = data.id
    ? await getPricingInfoApi(data.id as number)
    : {
        ...data,
      };
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
const FormRef = ref();
const pricingForm = ref<PricingInfo>({});
const save = async (type) => {
  await FormRef.value.validate();
  changeOkLoading(true);
  let api = pricingForm.value.projectType === 'comprehensive' ? addComprehensivePricingApi : addSinglePricingApi;
  if (pricingForm.value.id) {
    api = pricingForm.value.projectType === 'comprehensive' ? editComprehensivePricingApi : editSinglePricingApi;
  }
  const formData = cloneDeep(pricingForm.value);
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    changeOkLoading(false);
  }
};
const selectInitiation = (_value: number, data: InitiationInfo) => {
  pricingForm.value.pricingName = `${data.projectName}-定价`;
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  pricingName: [{ required: true, message: '请输入项目定价名称', trigger: 'blur' }],
  customerCategory: [{ required: true, message: '请选择客户类别', trigger: 'change' }],
  paymentCollectionMethod: [{ required: true, message: '请选择客户类别', trigger: 'change' }],
  pricingBasicRatio: [{ required: true, message: '请输入基准定价', trigger: 'blur' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 6 }, wrapperCol: { span: 18 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 3 }, wrapperCol: { span: 21 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn title="项目定价信息" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="pricingForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联项目" name="projectId">
              <ApiComponent
                v-model="pricingForm.projectId as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目类型" name="projectType">
              <a-select
                v-model:value="pricingForm.projectType"
                :options="dictStore.getDictList('FCT_PROJECT_TYPE')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目定价名称" name="pricingName">
              <a-input v-model:value="pricingForm.pricingName" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="客户类别" name="customerCategory">
              <a-select
                v-model:value="pricingForm.customerCategory"
                :options="dictStore.getDictList('FCT_CUSTOMER_CATEGORY')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="客户分级" name="customerLevel">
              <a-select
                v-model:value="pricingForm.customerLevel"
                :options="dictStore.getDictList('FCT_CUSTOMER_LEVEL')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="行业属性" name="industryAttributes">
              <a-input v-model:value="pricingForm.industryAttributes" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信额度测算结果" name="creditCalculateAmount">
              <a-input v-model:value="pricingForm.creditCalculateAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="区域特性" name="areaCharacteristics">
              <a-input v-model:value="pricingForm.areaCharacteristics" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="底层项目" name="bottomProject">
              <a-input v-model:value="pricingForm.bottomProject" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="担保方式" name="guaranteeMethodDesc">
              <a-input v-model:value="pricingForm.guaranteeMethodDesc" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="其他情况" name="pricingOtherDesc" v-bind="fullProp">
              <a-textarea v-model:value="pricingForm.pricingOtherDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption
          v-if="pricingForm.projectType"
          :content="`一般定价方案（${dictStore.formatter(pricingForm.projectType, 'FCT_PROJECT_TYPE')}）`"
        />
        <a-row class="mt-5">
          <template v-if="pricingForm.projectType === 'comprehensive'">
            <a-col v-bind="colSpan">
              <a-form-item label="授信额度（元）" name="pricingCreditAmount">
                <a-input v-model:value="pricingForm.pricingCreditAmount" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信期限（个月）" name="pricingCreditTerm">
                <a-input v-model:value="pricingForm.pricingCreditTerm" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信费率（%）" name="pricingCreditRate">
                <a-input v-model:value="pricingForm.pricingCreditRate" disabled />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="授信额度类型">
                {{ dictStore.formatter(pricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
              </a-form-item>
            </a-col>
          </template>
          <template v-if="pricingForm.projectType">
            <a-col v-bind="colSpan">
              <a-form-item label="基准定价（%/年）" name="pricingBasicRatio">
                <a-input v-model:value="pricingForm.pricingBasicRatio" />
              </a-form-item>
            </a-col>
            <a-col v-bind="colSpan">
              <a-form-item label="回款方式" name="paymentCollectionMethod">
                <a-select
                  v-model:value="pricingForm.paymentCollectionMethod"
                  :options="dictStore.getDictList('FCT_PAYMENT_COLLECTION_METHOD')"
                  disabled
                />
              </a-form-item>
            </a-col>
          </template>
        </a-row>
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
