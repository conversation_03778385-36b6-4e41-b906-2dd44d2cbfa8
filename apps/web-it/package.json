{"name": "@vben/web-it", "version": "0.0.1", "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:analyze": "pnpm vite build --mode analyze", "dev": "pnpm vite --mode development", "preview": "vite preview", "typecheck": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vben-core/shadcn-ui": "workspace:*", "@vben/fe-ui": "workspace:*", "@vben/base-ui": "workspace:*", "@ant-design/colors": "catalog:", "@ant-design/icons-vue": "catalog:", "@vueuse/core": "catalog:", "@vueuse/integrations": "catalog:", "@purge-icons/generated": "catalog:", "@vue/runtime-core": "catalog:", "@vue/shared": "catalog:", "@form-create/ant-design-vue": "catalog:", "@form-create/antd-designer": "catalog:", "ant-design-vue": "catalog:", "resize-observer-polyfill": "catalog:", "dayjs": "catalog:", "lodash-es": "catalog:", "json-bigint": "catalog:", "pinia": "catalog:", "nzh": "catalog:", "sortablejs": "catalog:", "spark-md5": "catalog:", "sm-crypto": "catalog:", "vue-simple-uploader": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "vue-types": "catalog:", "vue3-signature": "catalog:", "lunar-typescript": "catalog:"}, "devDependencies": {"@types/lodash-es": "catalog:", "@types/spark-md5": "catalog:", "@types/sm-crypto": "catalog:", "vue-router": "catalog:", "@types/json-bigint": "catalog:"}}