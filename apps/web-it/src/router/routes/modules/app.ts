import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'lucide:layout-dashboard',
      title: $t('page.app.manage'),
    },
    name: 'App',
    path: '/app',
    children: [
      {
        name: 'Man<PERSON>',
        path: 'manage',
        component: () => import('#/views/app/manage/index.vue'),
        meta: {
          icon: 'tdesign:app',
          title: $t('page.app.manage'),
        },
      },
    ],
  },
];
export default routes;
