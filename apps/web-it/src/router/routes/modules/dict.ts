import type { RouteRecordRaw } from 'vue-router';

import { $t } from '@vben/locales';

import { BasicLayout } from '#/layouts';

const routes: RouteRecordRaw[] = [
  {
    component: BasicLayout,
    meta: {
      icon: 'qlementine-icons:dictionary-16',
      title: $t('page.dict.title'),
    },
    name: 'Dict',
    path: '/dict',
    children: [
      {
        name: 'DictManage',
        path: '/dict/manage',
        component: () => import('#/views/dict/manage/index.vue'),
        meta: {
          icon: 'qlementine-icons:dictionary-16',
          title: $t('page.dict.manage'),
        },
      },
      {
        name: 'DictBusiness',
        path: '/dict/business',
        component: () => import('#/views/dict/business/index.vue'),
        meta: {
          icon: 'fluent-mdl2:dictionary',
          title: $t('page.dict.business'),
        },
      },
      {
        name: 'DictRegion',
        path: '/dict/region',
        component: () => import('#/views/dict/region/index.vue'),
        meta: {
          icon: 'fluent-mdl2:dictionary',
          title: $t('page.dict.region'),
        },
      },
    ],
  },
];
export default routes;
