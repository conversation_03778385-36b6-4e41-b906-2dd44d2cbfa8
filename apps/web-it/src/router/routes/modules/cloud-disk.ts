import type { RouteRecordRaw } from 'vue-router';
import {$t} from "@vben/locales";

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: 'ri:file-cloud-line',
      title: $t('page.cloudDisk.group'),
    },
    name: 'CloudDisk',
    path: '/cloud-disk',
    children: [
      {
        name: 'CloudDiskManage',
        path: '/cloud-disk/manage',
        component: () => import('#/views/cloud-disk/manage/index.vue'),
        meta: {
          icon: 'akar-icons:file',
          title: $t('page.cloudDisk.my'),
        },
      },
    ],
  }
];
export default routes;
