<script setup lang="ts">
import type { CalendarConfigInfo } from '#/api';

import { ref, unref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Col, message, Row, TypographyLink } from 'ant-design-vue';
import dayjs from 'dayjs';

import {
  getCustomHolidaySettingsApi,
  getHolidaySettingsApi,
  saveHolidaySettingsApi,
} from '#/api';
import WorkdaySettings from '#/views/system/config/components/workday-settings.vue';

const infoForm = ref<Partial<CalendarConfigInfo>>({
  type: 'STATUTORY',
});
const configType = [
  {
    icon: 'fluent:calendar-20-regular',
    type: 'STATUTORY',
    title: '中国大陆法定工作日',
    desc: '数据源:中国国务院办公厅法定工作日',
  },
  {
    icon: 'fluent:calendar-edit-20-regular',
    type: 'CUSTOM',
    title: '自定义工作日',
  },
];
const workdaySettingsRef = ref();
const loading = ref({
  save: false,
});
const [Modal, modalApi] = useVbenModal({
  fullscreen: true,
  isOpen: false,
  onConfirm: async () => {
    loading.value.save = true;
    try {
      await saveHolidaySettingsApi(unref(infoForm) as CalendarConfigInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
    } finally {
      loading.value.save = false;
    }
  },
});
const toEdit = () => {
  modalApi.open();
};
const getHolidaySettings = async () => {
  const res = await getHolidaySettingsApi();
  infoForm.value = {
    type: res.type,
  };
  if (res.type === 'CUSTOM') {
    infoForm.value = await getCustomHolidaySettingsApi({
      year: dayjs().year(),
    });
  }
};
getHolidaySettings();
const changeType = async (type: string) => {
  if (type !== infoForm.value.type) {
    infoForm.value =
      type === 'CUSTOM'
        ? await getCustomHolidaySettingsApi({
            year: dayjs().year(),
          })
        : {
            type,
          };
    await saveHolidaySettingsApi(unref(infoForm) as CalendarConfigInfo);
    message.success($t('base.resSuccess'));
  }
};
</script>

<template>
  <Row>
    <Col :span="8">
      <div
        v-for="item in configType"
        :key="item.type"
        class="relative m-4 flex min-h-20 cursor-pointer items-center justify-between rounded-md border px-6 py-4"
        :class="{
          'border-blue-500': infoForm.type === item.type,
          'border-2': infoForm.type === item.type,
        }"
        @click="changeType(item.type)"
      >
        <div class="flex items-center">
          <VbenIcon :icon="item.icon" class="mr-6 h-8 w-8 text-blue-500" />
          <div>
            <p class="text-sm">{{ item.title }}</p>
            <p v-if="item.desc" class="mt-1 text-sm text-gray-400">
              {{ item.desc }}
            </p>
          </div>
        </div>
        <TypographyLink v-if="item.type === 'CUSTOM'" @click="toEdit">
          去设置
        </TypographyLink>
        <VbenIcon
          v-if="infoForm.type === item.type"
          icon="bi:check-square-fill"
          class="absolute right-0 top-0 h-4 w-4 text-blue-500"
        />
      </div>
      <Modal
        class="w-[800px]"
        title="自定义工作日"
        :confirm-loading="loading.save"
      >
        <WorkdaySettings ref="workdaySettingsRef" v-model="infoForm" />
      </Modal>
    </Col>
  </Row>
</template>

<style></style>
