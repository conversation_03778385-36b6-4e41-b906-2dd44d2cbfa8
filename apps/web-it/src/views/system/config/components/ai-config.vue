<script setup lang="ts">
import type { Ref } from 'vue';

import type { AiModelInfo, ConfigInfo } from '#/api';

import { inject, ref } from 'vue';

import { Button, Col, Form, FormItem, Row, Select, SelectOption } from 'ant-design-vue';

import { getAiModelListApi } from '#/api';
import {
  configInfoInjectionKey,
  configLoadingInjectionKey,
  configSaveInjectionKey,
} from '#/views/system/config/config-injection-key';

const configForm = inject(configInfoInjectionKey) as Ref<Partial<ConfigInfo>>;
const saveConfig = inject(configSaveInjectionKey) as Function;
const loading = inject(configLoadingInjectionKey) as Ref<{ save: boolean }>;
const modelList = ref<AiModelInfo[]>([]);
const getAiModelList = async () => {
  modelList.value = await getAiModelListApi({ type: 'llm' });
};
getAiModelList();
</script>

<template>
  <Row>
    <Col :span="12">
      <Form :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }">
        <FormItem label="默认模型">
          <Select v-model:value="configForm.defaultModel">
            <SelectOption v-for="item in modelList" :key="item.id" :value="item.id">
              {{ item.name }}
            </SelectOption>
          </Select>
        </FormItem>
        <FormItem :wrapper-col="{ span: 24 }">
          <div class="text-right">
            <Button :loading="loading.save" type="primary" @click="saveConfig(configForm)">保存</Button>
          </div>
        </FormItem>
      </Form>
    </Col>
  </Row>
</template>

<style></style>
