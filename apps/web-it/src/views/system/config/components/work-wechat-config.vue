<script setup lang="ts">
import type { Ref } from 'vue';

import type { ConfigInfo } from '#/api';

import { computed, inject, ref } from 'vue';

import {
  Button,
  Col,
  Form,
  FormItem,
  Input,
  InputGroup,
  Row,
  Table,
  TypographyLink,
} from 'ant-design-vue';

import {
  configInfoInjectionKey,
  configLoadingInjectionKey,
  configSaveInjectionKey,
} from '#/views/system/config/config-injection-key';

const configForm = inject(configInfoInjectionKey) as Ref<Partial<ConfigInfo>>;
const saveConfig = inject(configSaveInjectionKey) as Function;
const loading = inject(configLoadingInjectionKey) as Ref<{ save: boolean }>;
const eventData = computed(() => {
  return [
    {
      key: 'qyhSynIsSynOrg',
      name: '启用同步组织',
      desc: '新增、删除、修改组织信息触发同步组织事件',
    },
    {
      key: 'qyhSynIsSynUser',
      name: '启用同步用户',
      desc: '新增、删除、修改用户信息触发同步用户事件',
    },
  ];
});
const selectedRowKeys = computed({
  get() {
    const keys = [];
    if (configForm.value.qyhSynIsSynOrg) {
      keys.push('qyhSynIsSynOrg');
    }
    if (configForm.value.qyhSynIsSynUser) {
      keys.push('qyhSynIsSynUser');
    }
    return keys;
  },
  set(val) {
    configForm.value.qyhSynIsSynOrg = val.includes('qyhSynIsSynOrg') ? 1 : 0;
    configForm.value.qyhSynIsSynUser = val.includes('qyhSynIsSynUser') ? 1 : 0;
  },
});
const selectChange = (val: (number | string)[]) => {
  selectedRowKeys.value = val as string[];
};
const resData = ref([]);
const eventColumns = [
  {
    title: '触发事件',
    dataIndex: 'name',
  },
  {
    title: '描述',
    dataIndex: 'desc',
  },
];
const resColumns = [
  {
    title: '同步类型',
    dataIndex: 'type',
  },
  {
    title: '总数',
    dataIndex: 'total',
  },
  {
    title: '同步成功数',
    dataIndex: 'successCount',
  },
  {
    title: '同步失败数',
    dataIndex: 'failureCount',
  },
  {
    title: '未同步数',
    dataIndex: 'unsyncCount',
  },
  {
    title: '同步时间',
    dataIndex: 'syncTime',
  },
  {
    title: '操作',
    dataIndex: 'operation',
  },
];
const formRef = ref();
const save = () => {
  formRef.value.validate().then(() => {
    saveConfig(configForm.value);
  });
};
</script>

<template>
  <Row>
    <Col :span="12">
      <Form ref="formRef" :model="configForm" :colon="false">
        <FormItem label="企业号ID">
          <Input
            v-model:value="configForm.qyhCorpId"
            placeholder="请输入CorpId"
          />
        </FormItem>
        <FormItem label="应用凭证">
          <Input
            v-model:value="configForm.qyhAgentId"
            placeholder="请输入AgentId"
          />
        </FormItem>
        <FormItem label="凭证密钥">
          <InputGroup compact>
            <Input
              v-model:value="configForm.qyhAppSecret"
              placeholder="请输入AppSecret"
              style="width: calc(100% - 88px)"
            />
            <Button>连接测试</Button>
          </InputGroup>
        </FormItem>
        <FormItem label="同步密钥">
          <InputGroup compact>
            <Input
              v-model:value="configForm.qyhCorpSecret"
              placeholder="请输入CorpSecret"
              style="width: calc(100% - 88px)"
            />
            <Button>同步测试</Button>
          </InputGroup>
        </FormItem>
        <FormItem label="触发事件">
          <Table
            :columns="eventColumns"
            :data-source="eventData"
            :row-selection="{ selectedRowKeys, onChange: selectChange }"
            :pagination="false"
            row-key="key"
            size="small"
            bordered
          />
        </FormItem>
        <FormItem label="同步反馈">
          <Table
            :columns="resColumns"
            :data-source="resData"
            :pagination="false"
            row-key="name"
            size="small"
            bordered
          >
            <template #bodyCell="{ column }">
              <template v-if="column.dataIndex === 'operation'">
                <TypographyLink>同步</TypographyLink>
              </template>
            </template>
          </Table>
        </FormItem>
        <FormItem>
          <div class="text-right">
            <Button :loading="loading.save" type="primary" @click="save">
              保存
            </Button>
          </div>
        </FormItem>
      </Form>
    </Col>
  </Row>
</template>

<style></style>
