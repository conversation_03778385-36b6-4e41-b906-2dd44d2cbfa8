<script setup lang="ts">
import { defineFormOptions } from '@vben/utils';

import { Col, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';

const [SmsForm, smsFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Select',
        fieldName: 'platform',
        label: '短信平台',
        componentProps: {
          options: [
            { label: '汇云', value: 'huiyun' },
            { label: '阿里云', value: 'aliyun' },
            { label: '腾讯云', value: 'tencent' },
            { label: '华为云', value: 'huawei' },
            { label: '七牛云', value: 'qiniu' },
            { label: '网易云', value: 'netease' },
            { label: '又拍云', value: 'upyun' },
            { label: '中国移动', value: 'chinamobile' },
            { label: '云片', value: 'yunpian' },
            { label: '京东云', value: 'jdcloud' },
          ],
        },
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'pid',
        label: '通道ID',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'endpoint',
        label: 'Endpoint',
        dependencies: {
          if(values) {
            return [
              'aliyun',
              'baidu',
              'chinamobile',
              'huawei',
              'huiyun',
              'jdcloud',
            ].includes(values.platform);
          },
          trigger(values) {
            const platformConfig = {
              huiyun: { label: 'Endpoint' },
              aliyun: { label: 'Endpoint' },
              huawei: { label: 'Url' },
              chinamobile: { label: 'Url' },
              jdcloud: { label: '地域' },
            };
            const config = platformConfig[
              values.platform as keyof typeof platformConfig
            ] || {
              label: 'Endpoint',
            };
            smsFormApi.updateSchema([
              { fieldName: 'endpoint', label: config.label },
            ]);
          },
          triggerFields: ['platform'],
        },
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'accessKey',
        label: 'AccessKey',
        dependencies: {
          if(values) {
            return [
              'aliyun',
              'baidu',
              'chinamobile',
              'huawei',
              'huiyun',
              'jdcloud',
              'netease',
              'qiniu',
              'tencent',
              'upyun',
            ].includes(values.platform);
          },
          trigger(values) {
            const platformConfig = {
              huiyun: { label: 'Account' },
              tencent: { label: 'AppId' },
              huawei: { label: 'AppKey' },
              netease: { label: 'AppKey' },
              upyun: { label: 'Account' },
              chinamobile: { label: 'AppId' },
            };
            const config = platformConfig[
              values.platform as keyof typeof platformConfig
            ] || {
              label: 'AccessKey',
            };
            smsFormApi.updateSchema([
              { fieldName: 'accessKey', label: config.label },
            ]);
          },
          triggerFields: ['platform'],
        },
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'secretKey',
        label: 'SecretKey',
        dependencies: {
          if(values) {
            return [
              'aliyun',
              'baidu',
              'chinamobile',
              'huawei',
              'huiyun',
              'jdcloud',
              'netease',
              'qiniu',
              'tencent',
              'upyun',
              'yunpian',
            ].includes(values.platform);
          },
          trigger(values) {
            const platformConfig = {
              huiyun: { label: 'Token' },
              tencent: { label: 'AppKey' },
              huawei: { label: 'AppSecret' },
              netease: { label: 'AppSecret' },
              upyun: { label: 'Token' },
              yunpian: { label: 'ApiKey' },
            };
            const config = platformConfig[
              values.platform as keyof typeof platformConfig
            ] || {
              label: 'SecretKey',
            };
            smsFormApi.updateSchema([
              { fieldName: 'secretKey', label: config.label },
            ]);
          },
          triggerFields: ['platform'],
        },
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'sender',
        label: '签名通道',
        dependencies: {
          if(values) {
            return ['chinamobile', 'huawei'].includes(values.platform);
          },
          trigger(values) {
            const platformConfig = {
              huawei: { label: '签名通道' },
              chinamobile: { label: '企业名称' },
            };
            const config = platformConfig[
              values.platform as keyof typeof platformConfig
            ] || {
              label: '签名通道',
            };
            smsFormApi.updateSchema([
              { fieldName: 'sender', label: config.label },
            ]);
          },
          triggerFields: ['platform'],
        },
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'signName',
        label: '短信签名',
        rules: 'required',
      },
    ],
    wrapperClass: 'grid-cols-1',
    resetButtonOptions: {
      show: false,
    },
    submitButtonOptions: {
      content: '保存',
    },
    // handleSubmit(values) {
    //   console.log(values);
    // },
  }),
);
</script>

<template>
  <Row>
    <Col :span="12">
      <SmsForm />
    </Col>
  </Row>
</template>

<style></style>
