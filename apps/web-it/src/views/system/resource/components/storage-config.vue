<script setup lang="ts">
import { defineFormOptions } from '@vben/utils';

import { Col, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';

const [StorageForm, storageFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Select',
        fieldName: 'platform',
        label: '存储平台',
        componentProps: {
          options: [
            { label: 'MinIO', value: 'minio' },
            { label: '阿里云', value: 'aliyun' },
          ],
        },
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'pid',
        label: '对象ID',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'bucket',
        label: 'Bucket',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'endpoint',
        label: 'Endpoint',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'domain',
        label: '访问域名',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'accessKey',
        label: 'AccessKey',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'secretKey',
        label: 'SecretKey',
        rules: 'required',
      },
      {
        component: 'Switch',
        fieldName: 'pathStyleAccess',
        label: 'pathStyleAccess',
        componentProps: {
          class: '',
        },
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'region',
        label: '区域',
      },
    ],
    wrapperClass: 'grid-cols-1',
    resetButtonOptions: {
      show: false,
    },
    submitButtonOptions: {
      content: '保存',
    },
    handleSubmit(values) {
      console.log(values);
    },
  }),
);
</script>

<template>
  <Row>
    <Col :span="12">
      <StorageForm />
    </Col>
  </Row>
</template>

<style scoped></style>
