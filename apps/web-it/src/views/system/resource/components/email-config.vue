<script setup lang="ts">
import type { ConfigInfo } from '#/api';

import { ref, watch } from 'vue';

import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Button, Col, message, Row } from 'ant-design-vue';

import { useVbenForm } from '#/adapter/form';
import { getConfigInfoApi, saveConfigInfoApi } from '#/api';

const loading = ref({
  detail: false,
  save: false,
});
const configForm = ref<Partial<ConfigInfo>>({});
const getConfigInfo = async () => {
  try {
    loading.value.detail = true;
    configForm.value = await getConfigInfoApi();
  } finally {
    loading.value.detail = false;
  }
};
const saveConfig = async (values: Partial<ConfigInfo>) => {
  try {
    loading.value.save = true;
    await saveConfigInfoApi(values);
    message.success($t('base.resSuccess'));
    await getConfigInfo();
  } finally {
    loading.value.save = false;
  }
};
getConfigInfo();
const [EmailForm, emailFormApi] = useVbenForm(
  defineFormOptions({
    schema: [
      {
        component: 'Input',
        fieldName: 'emailSenderName',
        label: '发件人昵称',
      },
      {
        component: 'Input',
        fieldName: 'emailSmtpHost',
        label: 'SMTP服务器',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'emailSmtpPort',
        label: 'SMTP端口',
        rules: 'required',
      },
      {
        label: 'SSL安全连接',
        fieldName: 'emailSsl',
        component: 'Switch',
        componentProps: {
          class: '',
        },
      },
      {
        component: 'Input',
        fieldName: 'emailAccount',
        label: 'SMTP用户名',
        rules: 'required',
      },
      {
        component: 'Input',
        fieldName: 'emailPassword',
        label: 'SMTP密码',
        componentProps: {
          type: 'password',
        },
        rules: 'required',
      },
    ],
    wrapperClass: 'grid-cols-1',
    resetButtonOptions: {
      show: false,
    },
    submitButtonOptions: {
      content: '保存',
    },
    handleSubmit(values) {
      saveConfig(values);
    },
  }),
);
watch(
  configForm,
  (val) => {
    emailFormApi.setValues(val, false);
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <Row>
    <Col :span="12">
      <div class="m-4">
        <EmailForm :submit-button-options="{ loading: loading.save }">
          <template #reset-before>
            <Button>测试</Button>
          </template>
        </EmailForm>
      </div>
    </Col>
  </Row>
</template>

<style></style>
