<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { DictInfo, DictTypeInfo } from '#/api';

import { h, ref } from 'vue';

import { ColorPicker, IconPicker, Page, useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { cloneDeep, defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import {
  Modal as AntdModal,
  Button,
  Descriptions,
  DescriptionsItem,
  Form,
  FormItem,
  Input,
  InputNumber,
  message,
  Space,
  Switch,
  Tag,
  Textarea,
  TypographyLink,
} from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addDictItemBizApi,
  clearDictCacheApi,
  delDictItemBizApi,
  editDictItemBizApi,
  getDictItemBizListApi,
  getDictTypeBizPageListApi,
} from '#/api';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: '字典编码',
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '字典名称',
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'code', title: '字典编码' },
    { field: 'name', title: '字典名称' },
    { field: 'sortCode', title: '排序' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 180,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getDictTypeBizPageListApi({
          page: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const loading = ref({
  dict: false,
  save: false,
});
const dictTypeData = ref<Partial<DictTypeInfo>>({});
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const dictGridOptions = {
  border: 'none',
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'dictName', title: '字典项名称', slots: { default: 'dictName' } },
    { field: 'dictValue', title: '字典项键值' },
    { field: 'sortCode', title: '排序' },
    {
      field: 'enabled',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: $t('base.action'),
      slots: { default: 'action' },
    },
  ],
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    refresh: true,
    custom: false,
    zoom: false,
  },
};
const [DictGrid, dictGridApi] = useVbenVxeGrid({
  gridOptions: dictGridOptions,
});
const [Modal, modalApi] = useVbenModal({
  onOpened() {
    if (dictTypeData.value.type === 'BIZ') {
      dictGridApi.grid.showColumn('permission');
    } else {
      dictGridApi.grid.hideColumn('permission');
    }
  },
});
const dictItemModalTitle = ref('');
const [DictItemModal, dictItemModalApi] = useVbenModal({
  onConfirm: async () => {
    await dictItemFormRef.value.validate();
    let api = addDictItemBizApi;
    if (dictItemForm.value.id) {
      api = editDictItemBizApi;
    }
    try {
      loading.value.save = true;
      await api(dictItemForm.value as DictInfo);
      message.success($t('base.resSuccess'));
      await dictItemModalApi.close();
      const dictItemList = await getDictItemBizListApi({
        code: dictTypeData.value.code as string,
      });
      await dictGridApi.grid.loadData(dictItemList);
    } finally {
      loading.value.save = false;
    }
  },
  onClosed: () => {
    dictItemForm.value = cloneDeep(defaultDictItem);
    dictItemFormRef.value.resetFields();
  },
});
const set = async (row: DictTypeInfo) => {
  dictTypeData.value = cloneDeep(row);
  modalApi.open();
  const dictItemList = await getDictItemBizListApi({ code: row.code });
  await dictGridApi.grid.loadData(dictItemList);
};
const addDictItem = async () => {
  dictItemModalTitle.value = '新增字典项';
  dictItemModalApi.open();
  dictItemForm.value.code = dictTypeData.value.code;
  const dictItemList = await getDictItemBizListApi({
    code: dictTypeData.value.code as string,
  });
  await dictGridApi.grid.loadData(dictItemList);
};
const editDictItem = async (row: DictInfo) => {
  dictItemModalTitle.value = '编辑字典项';
  dictItemModalApi.open();
  dictItemForm.value = defaultsDeep(row, defaultDictItem);
};
const delDictItem = async (row: DictInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delDictItemBizApi(row.id);
      message.success($t('base.resSuccess'));
      const dictItemList = await getDictItemBizListApi({
        code: dictTypeData.value.code as string,
      });
      await dictGridApi.grid.loadData(dictItemList);
    },
  });
};
const revertDictItem = async (row: DictInfo) => {
  AntdModal.confirm({
    title: '确认还原',
    content: '确认还原此字典项？',
    async onOk() {
      await delDictItemBizApi(row.id);
      message.success($t('base.resSuccess'));
      const dictItemList = await getDictItemBizListApi({
        code: dictTypeData.value.code as string,
      });
      await dictGridApi.grid.loadData(dictItemList);
    },
  });
};
const dictItemFormRef = ref();
const defaultDictItem = {
  sortCode: 0,
  enabled: 1,
  dictColor: 'default',
};
const dictItemForm = ref<Partial<DictInfo>>(cloneDeep(defaultDictItem));
const dictItemRules: Record<string, Rule[]> = {
  dictName: [{ required: true, message: '请输入字典项名称', trigger: 'blur' }],
  dictValue: [{ required: true, message: '请输入字典项键值', trigger: 'blur' }],
  permission: [{ required: true, message: '请选择权限', trigger: 'change' }],
  dictColor: [{ required: true, message: '请选择字典颜色', trigger: 'change' }],
};
const clearCache = async () => {
  AntdModal.confirm({
    title: '清除缓存',
    content: '确认清除缓存？',
    async onOk() {
      await clearDictCacheApi();
      message.success($t('base.resSuccess'));
    },
  });
};
const svgVisible = ref(false);
const activeDictItem = ref<DictInfo>({} as DictInfo);
const editSvg = (row: DictInfo) => {
  svgVisible.value = true;
  activeDictItem.value = row;
};
const confirmSetSvg = () => {
  svgVisible.value = false;
  dictItemForm.value.dictIcon = activeDictItem.value.dictIcon;
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button danger @click="clearCache">
          <VbenIcon icon="icon-park-outline:clear" class="mr-1 text-base" />
          清除缓存
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="set(row)">字典配置</TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal
      :confirm-loading="loading.dict"
      :show-confirm-button="false"
      cancel-text="关闭"
      class="w-[800px]"
      title="字典配置"
    >
      <Descriptions v-bind="DESCRIPTIONS_PROP">
        <DescriptionsItem label="字典编码">
          {{ dictTypeData.code }}
        </DescriptionsItem>
        <DescriptionsItem label="字典名称">
          {{ dictTypeData.name }}
        </DescriptionsItem>
      </Descriptions>
      <DictGrid>
        <template #toolbar-actions>
          <Button class="mr-2" type="primary" @click="addDictItem">
            {{ $t('base.add') }}
          </Button>
        </template>
        <template #dictName="{ row }">
          <Tag v-if="dictTypeData.isTag" :color="row.dictColor">{{ row.dictName }}</Tag>
          <span v-else>{{ row.dictName }}</span>
        </template>
        <template #action="{ row }">
          <Space>
            <TypographyLink @click="editDictItem(row)">
              {{ $t('base.edit') }}
            </TypographyLink>
            <TypographyLink v-if="row.type === 'BIZ' && !row.permission" type="danger" @click="delDictItem(row)">
              {{ $t('base.del') }}
            </TypographyLink>
            <TypographyLink v-else-if="row.type === 'BIZ' && row.permission" type="danger" @click="revertDictItem(row)">
              还原
            </TypographyLink>
          </Space>
        </template>
      </DictGrid>
    </Modal>
    <DictItemModal :title="dictItemModalTitle" :submitting="loading.save">
      <!--<DictItemForm />-->
      <Form
        ref="dictItemFormRef"
        :colon="false"
        :model="dictItemForm"
        :rules="dictItemRules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <FormItem name="dictName" label="字典项名称">
          <Input
            v-model:value="dictItemForm.dictName"
            :disabled="['NONE', 'STATUS'].includes(dictItemForm.permission as string)"
          />
        </FormItem>
        <FormItem name="dictValue" label="字典项键值">
          <Input v-model:value="dictItemForm.dictValue" :disabled="dictItemForm.type === 'SYS'" />
        </FormItem>
        <FormItem v-if="dictTypeData.iconType" name="dictIcon" label="字典项图标">
          <IconPicker v-if="dictTypeData.iconType === 1" v-model="dictItemForm.dictIcon" />
          <div v-else class="flex items-center">
            <VbenIcon
              v-if="dictItemForm.dictIcon"
              :icon="dictItemForm.dictIcon"
              class="h-8 w-8 cursor-pointer rounded-lg border"
              @click="editSvg(dictItemForm as DictInfo)"
            />
            <Button v-else :icon="h(PlusOutlined)" class="mr-2" @click="editSvg(dictItemForm as DictInfo)" />
          </div>
        </FormItem>
        <FormItem v-if="dictTypeData.isTag === 1" name="dictColor" label="字典项颜色">
          <Input v-model:value="dictItemForm.dictColor">
            <template #addonBefore>
              <ColorPicker v-model="dictItemForm.dictColor as string" />
            </template>
          </Input>
        </FormItem>
        <FormItem name="sortCode" label="排序">
          <InputNumber v-model:value="dictItemForm.sortCode" :min="0" :step="1" :precision="0" class="w-full" />
        </FormItem>
        <FormItem name="enabled" label="状态">
          <Switch
            v-model:checked="dictItemForm.enabled"
            :checked-value="1"
            :un-checked-value="0"
            :disabled="['NONE', 'NAME'].includes(dictItemForm.permission as string)"
          />
        </FormItem>
      </Form>
    </DictItemModal>
    <AntdModal v-model:open="svgVisible" title="请输入SVG代码" @ok="confirmSetSvg">
      <Textarea v-model:value="activeDictItem.dictIcon" :autosize="{ minRows: 5, maxRows: 8 }" />
    </AntdModal>
  </Page>
</template>

<style></style>
