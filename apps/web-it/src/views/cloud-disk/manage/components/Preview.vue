<script lang="ts" setup>
import { reactive, toRefs } from 'vue';

// import { previewFile } from '@/api/basic/common';
import ModalClose from '@vben/fe-ui/components/Modal/src/components/ModalClose.vue';
import { useMessage } from '@vben/fe-ui/hooks/web/useMessage';
import { $t as t } from '@vben/locales';

// import { getToken } from '@/utils/auth';
import { Modal as AModal, Button, Space } from 'ant-design-vue';

interface State {
  visible: boolean;
  loading: boolean;
  title: string;
  url: string;
  file: any;
}

const { createMessage } = useMessage();
const state = reactive<State>({
  visible: false,
  loading: false,
  title: '',
  url: '',
  file: {},
});
const { visible, loading, title, url } = toRefs(state);

defineExpose({ init });

function init(file) {
  state.title = `文档预览 - ${file.name}`;
  state.url = '';
  state.file = file;
  state.visible = true;
  state.loading = true;
  const query = {
    fileName: file.fileId,
    fileVersionId: file.fileVersionId,
    fileDownloadUrl: file.url,
  };
  // previewFile(query)
  //   .then((res) => {
  //     state.loading = false;
  //     if (res.data) {
  //       state.url = `${res.data}&token=}`;
  //     } else {
  //       createMessage.warning('文件不存在');
  //       handleCancel();
  //     }
  //   })
  //   .catch(() => {
  //     state.loading = false;
  //     handleCancel();
  //   });
}
function handleCancel() {
  state.visible = false;
}
</script>
<template>
  <AModal
    v-model:open="visible"
    :footer="null"
    :closable="false"
    :keyboard="false"
    :mask-closable="false"
    class="common-container-modal fe-full-modal full-modal file-preview-modal"
    wrap-class-name="fullscreen-modal"
  >
    <template #closeIcon>
      <ModalClose :can-fullscreen="false" @cancel="handleCancel" />
    </template>
    <template #title>
      <div class="fe-full-modal-header">
        <div class="header-title">
          <p class="header-txt">{{ title }}</p>
        </div>
        <Space class="options" :size="10">
          <Button @click="handleCancel()">{{ t('common.cancelText') }}</Button>
        </Space>
      </div>
    </template>
    <div class="basic-content bg-white" v-loading="loading">
      <iframe width="100%" height="100%" :src="url" frameborder="0"></iframe>
    </div>
  </AModal>
</template>
<style lang="less">
.file-preview-modal {
  .ant-modal-body {
    padding: 10px !important;
  }
  .header-txt {
    max-width: 80vw !important;
  }
}
</style>
