<script lang="ts" setup>
import type { FormSchema } from '@vben/fe-ui';

import { computed, reactive } from 'vue';

import { BasicForm, BasicModal, useForm, useModalInner } from '@vben/fe-ui';
import { useMessage } from '@vben/fe-ui/hooks/web/useMessage';

// import { create, getInfo, update } from '#/api/workFlow/document';
import { createFolderApi, fileInfoApi, updateFolderApi } from '#/api';

interface State {
  dataForm: any;
  id: string;
  parentId: string;
  type: string;
}

const emit = defineEmits(['register', 'reload']);
const state = reactive<State>({
  dataForm: {},
  id: '',
  parentId: '',
  type: '0',
});
const schemas: FormSchema[] = [
  {
    field: 'fullName',
    label: '',
    component: 'Input',
    componentProps: { placeholder: '请输入' },
    rules: [{ required: true, trigger: 'blur', message: '必填' }],
  },
];
const getTitle = computed(() => (state.id ? '重命名文件' : '新建文件夹'));
const { createMessage } = useMessage();
const [registerForm, { setFieldsValue, validate, resetFields }] = useForm({ labelWidth: 80, schemas });
const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);

function init(data) {
  resetFields();
  state.id = data.id;
  state.parentId = data.parentId;
  state.type = '0';
  if (state.id) {
    changeLoading(true);
    fileInfoApi({ id: state.id }).then((res) => {
      state.dataForm = res;
      state.type = state.dataForm.type || '0';
      setFieldsValue({ fullName: state.dataForm.fullName });
      changeLoading(false);
    });
  }
}
async function handleSubmit() {
  const values = await validate();
  if (!values) return;
  changeOkLoading(true);
  const query = {
    ...values,
    id: state.id,
    parentId: state.parentId,
    type: state.type,
  };
  const formMethod = state.id ? updateFolderApi : createFolderApi;
  formMethod(query)
    .then((res) => {
      createMessage.success(res.msg);
      changeOkLoading(false);
      closeModal();
      emit('reload');
    })
    .catch(() => {
      changeOkLoading(false);
    });
}
</script>
<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    show-ok-btn
    @ok="handleSubmit"
    destroy-on-close
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
