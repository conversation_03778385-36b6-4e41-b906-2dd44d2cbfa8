<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';
import type { MenuClickEventHandler } from 'ant-design-vue/es/menu/src/interface';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { ButtonPermissionInfo, MenuInfo } from '#/api';

import { h, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { DownOutlined, PlusOutlined } from '@ant-design/icons-vue';
import {
  Modal as AntdModal,
  Button,
  Dropdown,
  Form,
  Input,
  InputNumber,
  Menu,
  MenuItem,
  message,
  Space,
  Switch,
  Textarea,
  TypographyLink,
} from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addButtonPermissionApi,
  delButtonPermissionApi,
  editButtonPermissionApi,
  getButtonPermissionListApi,
} from '#/api';

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'name', title: '按钮名称' },
    { field: 'code', title: '按钮编码' },
    { field: 'sortCode', title: '排序' },
    {
      field: 'enabled',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 120,
      slots: { default: 'action' },
    },
  ],
  border: 'none',
  pagerConfig: {
    enabled: false,
  },
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async (_, formValues) => {
        return await getButtonPermissionListApi({
          ...formValues,
          moduleId: moduleId.value,
        });
      },
    },
  },
  toolbarConfig: {
    custom: false,
    refresh: true,
    resizable: false,
    zoom: false,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const edit = (row: ButtonPermissionInfo) => {
  title.value = '编辑按钮权限';
  btnForm.value = defaultsDeep(row, { enabled: 1, sortCode: 0, moduleId: moduleId.value });
  modalApi.open();
};
const del = (row: ButtonPermissionInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delButtonPermissionApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const moduleId = ref('');
const menuCode = ref('');
const init = (info: MenuInfo) => {
  moduleId.value = info.id;
  menuCode.value = info.code as string;
  gridApi.reload();
};
const loading = ref({
  submit: false,
});
const btnFormRef = ref();
const title = ref('新增按钮权限');
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await btnFormRef.value.validate();
    loading.value.submit = true;
    let api = addButtonPermissionApi;
    if (btnForm.value.id) {
      api = editButtonPermissionApi;
    }

    try {
      await api(btnForm.value as ButtonPermissionInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.submit = false;
    }
  },
  onBeforeClose: () => {
    btnForm.value = defaultsDeep({ enabled: 1, sortCode: 0, moduleId: moduleId.value });
    btnFormRef.value.resetFields();
    return true;
  },
});
const btnForm = ref<Partial<ButtonPermissionInfo>>({});
const rules: Record<string, RuleObject | RuleObject[]> = {
  name: [{ required: true, message: '请输入按钮名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入按钮编码', trigger: 'blur' }],
  enabled: [{ required: true, message: '请选择状态', trigger: 'change' }],
};
const addBtnPermission = () => {
  title.value = '新增按钮权限';
  btnForm.value = defaultsDeep({}, { enabled: 1, sortCode: 0, moduleId: moduleId.value });
  modalApi.open();
};
const commonBtnPermissionList = [
  { name: '新增', code: 'add' },
  { name: '编辑', code: 'edit' },
  { name: '删除', code: 'delete' },
  { name: '详情', code: 'detail' },
  { name: '复制', code: 'copy' },
  { name: '导入', code: 'import' },
  { name: '导出', code: 'export' },
];
const addCommonBtnPermission: MenuClickEventHandler = async (e) => {
  const btnInfo = commonBtnPermissionList.find((item) => item.code === e.key);
  if (btnInfo) {
    await addButtonPermissionApi({
      name: btnInfo.name,
      code: `${menuCode.value}:${btnInfo.code}`,
      moduleId: moduleId.value,
      enabled: 1,
      sortCode: 0,
    } as ButtonPermissionInfo);
    message.success($t('base.resSuccess'));
    await gridApi.reload();
  }
};
defineExpose({ init });
</script>

<template>
  <div>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="addBtnPermission">新建</Button>
        <Dropdown>
          <Button :icon="h(PlusOutlined)" class="mr-2" type="link">
            常用按钮权限 <DownOutlined :style="{ fontSize: '12px' }" />
          </Button>
          <template #overlay>
            <Menu @click="addCommonBtnPermission">
              <MenuItem v-for="item in commonBtnPermissionList" :key="item.code">
                {{ item.name }}
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Modal :title="title" :submitting="loading.submit">
      <Form
        ref="btnFormRef"
        :model="btnForm"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :colon="false"
      >
        <Form.Item label="按钮名称" name="name">
          <Input v-model:value="btnForm.name" />
        </Form.Item>
        <Form.Item label="按钮编码" name="code">
          <Input v-model:value="btnForm.code" />
        </Form.Item>
        <Form.Item label="排序" name="sortCode">
          <InputNumber v-model:value="btnForm.sortCode" :min="0" class="w-full" />
        </Form.Item>
        <Form.Item label="状态" name="enabled" initial-value="1">
          <Switch v-model:checked="btnForm.enabled" :checked-value="1" :un-checked-value="0" />
        </Form.Item>
        <Form.Item label="说明" name="description">
          <Textarea v-model:value="btnForm.description" :rows="3" />
        </Form.Item>
      </Form>
    </Modal>
  </div>
</template>

<style></style>
