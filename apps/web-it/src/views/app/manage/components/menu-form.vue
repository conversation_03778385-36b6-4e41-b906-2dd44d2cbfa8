<script setup lang="ts">
import type { RuleObject } from 'ant-design-vue/es/form';

import type { MenuInfo } from '#/api';

import { ref } from 'vue';

import { IconPicker } from '@vben/common-ui';
import { useDictStore } from '@vben/stores';

import { Form, Input, InputNumber, Select, Switch, Textarea, TreeSelect } from 'ant-design-vue';
import { defaultsDeep } from 'lodash-es';

import { getMenuTreeApi } from '#/api';

interface DefaultOptionType {
  label?: string;
  title?: string;
  value?: number | string;
  key?: string;
  children?: DefaultOptionType[];
  disabled?: boolean;
  [key: string]: any;
}

const props = defineProps({
  appCode: { type: String, required: true },
});
const treeData = ref<DefaultOptionType[] | (DefaultOptionType[] & {})>([]);
const getMenuData = async () => {
  const res = await getMenuTreeApi({ appCode: props.appCode, type: '1' });
  treeData.value = [
    {
      name: '根节点',
      id: '-1',
      children: res,
    },
  ];
};
getMenuData();

const { getDictList } = useDictStore();
const menuForm = ref<Partial<MenuInfo>>({});
const rules: Record<string, RuleObject | RuleObject[]> = {
  name: [{ required: true, message: '请输入菜单名称', trigger: 'blur' }],
  code: [{ required: true, message: '请输入菜单编码', trigger: 'blur' }],
  type: [{ required: true, message: '请选择菜单类型', trigger: 'change' }],
  parentId: [{ required: true, message: '请选择上级菜单', trigger: 'change' }],
  urlAddress: [{ required: true, message: '请输入路由地址', trigger: 'blur' }],
  component: [{ required: true, message: '请输入组件名称', trigger: 'blur' }],
  enabled: [{ required: true, message: '请选择状态', trigger: 'change' }],
};
const init = (data: Partial<MenuInfo>) => {
  menuForm.value = defaultsDeep(data, { sortCode: 0, enabled: 1, parentId: '-1', appCode: props.appCode });
};
const changeMenuType = () => {
  menuForm.value.urlAddress = '';
  menuForm.value.component = '';
  menuForm.value.linkTarget = '_self';
};
const menuFormRef = ref();
const onSubmit = async () => {
  await menuFormRef.value.validate();
  return menuForm.value;
};
defineExpose({ init, onSubmit });
</script>

<template>
  <Form
    ref="menuFormRef"
    :model="menuForm"
    :rules="rules"
    :label-col="{ span: 6 }"
    :wrapper-col="{ span: 18 }"
    :colon="false"
  >
    <Form.Item label="上级菜单" name="parentId">
      <TreeSelect
        v-model:value="menuForm.parentId"
        :tree-data="treeData"
        :field-names="{
          children: 'children',
          label: 'name',
          value: 'id',
        }"
        :tree-default-expanded-keys="['-1']"
      />
    </Form.Item>
    <Form.Item label="菜单名称" name="name">
      <Input v-model:value="menuForm.name" />
    </Form.Item>
    <Form.Item label="菜单编码" name="code">
      <Input v-model:value="menuForm.code" />
    </Form.Item>
    <Form.Item label="图标" name="icon">
      <IconPicker v-model="menuForm.icon" />
    </Form.Item>
    <Form.Item label="菜单类型" name="type">
      <Select v-model:value="menuForm.type" :options="getDictList('MODULE_TYPE')" @change="changeMenuType" />
    </Form.Item>
    <Form.Item
      v-if="['2', '3'].includes(menuForm.type ?? '')"
      :label="menuForm.type === '2' ? '路由地址' : '外链地址'"
      name="urlAddress"
    >
      <Input v-model:value="menuForm.urlAddress">
        <template #addonAfter>
          <Select v-model:value="menuForm.linkTarget" class="w-[100px]">
            <Select.Option value="_self">当前窗口</Select.Option>
            <Select.Option value="_blank">新窗口</Select.Option>
          </Select>
        </template>
      </Input>
    </Form.Item>
    <Form.Item v-if="menuForm.type === '2'" label="组件名称" name="component">
      <Input v-model:value="menuForm.component" />
    </Form.Item>
    <Form.Item label="排序" name="sortCode">
      <InputNumber v-model:value="menuForm.sortCode" :min="0" class="w-full" />
    </Form.Item>
    <Form.Item label="状态" name="enabled" initial-value="1">
      <Switch v-model:checked="menuForm.enabled" :checked-value="1" :un-checked-value="0" />
    </Form.Item>
    <Form.Item label="说明" name="description">
      <Textarea v-model:value="menuForm.description" :rows="3" />
    </Form.Item>
  </Form>
</template>

<style scoped></style>
