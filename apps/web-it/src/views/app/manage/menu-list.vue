<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { AppInfo, MenuInfo } from '#/api';

import { computed, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { DownOutlined, UpOutlined } from '@ant-design/icons-vue';
import { Modal as AntdModal, Button, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addMenu<PERSON><PERSON>, delMenuA<PERSON>, editMenu<PERSON><PERSON>, getMenuTreeApi } from '#/api';
import ButtonPermissionList from '#/views/app/manage/components/button-permission-list.vue';
import MenuForm from '#/views/app/manage/components/menu-form.vue';

const props = defineProps({
  height: { type: Number, default: 600 },
});
// 头部 60 主体部分 额外减 60
const bodyHeight = computed(() => props.height - 60 - 60);
const { getDictList } = useDictStore();
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'code',
      label: '菜单编码',
    },
    {
      component: 'Input',
      fieldName: 'name',
      label: '菜单名称',
    },
    {
      component: 'Select',
      fieldName: 'type',
      label: '菜单类型',
      componentProps: {
        options: getDictList('MODULE_TYPE'),
      },
    },
    {
      component: 'Select',
      fieldName: 'enabled',
      label: '状态',
      componentProps: {
        options: getDictList('baseEnableType'),
      },
    },
  ],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'name', title: '菜单名称', treeNode: true, minWidth: 300 },
    { field: 'code', title: '菜单编码' },
    { field: 'urlAddress', title: '路由地址', minWidth: 180 },
    { field: 'icon', title: '图标', slots: { default: 'icon' } },
    {
      field: 'type',
      title: '类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'MODULE_TYPE',
        },
      },
      width: 120,
    },
    { field: 'sortCode', title: '排序' },
    {
      field: 'enabled',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 180,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  pagerConfig: {
    enabled: false,
  },
  treeConfig: {
    rowField: 'id',
    childrenField: 'children',
    expandAll: true,
  },
  proxyConfig: {
    ajax: {
      query: async (_, formValues) => {
        return await getMenuTreeApi({
          ...formValues,
          appCode: appInfo.value.code,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
    slots: {
      tools: 'toolbarTools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const appInfo = ref<Partial<AppInfo>>({});
const init = (data: AppInfo) => {
  appInfo.value = data;
};
const [registerPopup] = usePopupInner(init);
const title = computed(() => {
  return `${appInfo.value.name}的菜单管理`;
});
const menuFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onOpened() {
    const value = modalApi.getData<Record<string, any>>();
    menuFormRef.value.init(value);
  },
  onConfirm: async () => {
    const formInfo = await menuFormRef.value.onSubmit();
    let api = addMenuApi;
    if (formInfo.id) {
      api = editMenuApi;
    }
    try {
      loading.value.submit = true;
      await api(formInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.submit = false;
    }
  },
});
const buttonPermissionListRef = ref();
const [BtnPermissionModal, btnPermissionModelApi] = useVbenModal({
  onOpened() {
    const value = btnPermissionModelApi.getData<Record<string, any>>();
    buttonPermissionListRef.value.init(value);
  },
});
const modalTitle = ref('新建菜单');
const loading = ref({
  submit: false,
});
const addMenu = () => {
  modalTitle.value = '新建菜单';
  modalApi.setData(null).open();
};
const edit = (row: MenuInfo) => {
  modalTitle.value = '编辑菜单';
  modalApi.setData(row).open();
};
const del = async (row: MenuInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delMenuApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
const treeExpand = ref(true);
const expandAll = () => {
  treeExpand.value = !treeExpand.value;
  gridApi.grid.setAllTreeExpand(treeExpand.value);
};
const addSubNode = (row: MenuInfo) => {
  modalApi.setData({ parentId: row.id }).open();
};
const setButtonPermission = (row: MenuInfo) => {
  btnPermissionModelApi.setData(row).open();
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <div :style="{ height: `${bodyHeight}px` }">
      <Grid>
        <template #toolbar-actions>
          <Button class="mr-2" type="primary" @click="addMenu">新建</Button>
        </template>
        <template #toolbarTools>
          <div
            title="展开/收起"
            class="flex h-[30px] w-[30px] cursor-pointer items-center justify-center rounded-full border hover:text-[color:var(--vxe-ui-font-primary-lighten-color)]"
            @click="expandAll"
          >
            <UpOutlined v-if="treeExpand" />
            <DownOutlined v-else />
          </div>
        </template>
        <template #icon="{ row }">
          <VbenIcon :icon="row.icon" class="text-lg" />
        </template>
        <template #action="{ row }">
          <Space>
            <TypographyLink @click="edit(row)">
              {{ $t('base.edit') }}
              {{ $t('common.cancelText') }}
            </TypographyLink>
            <TypographyLink type="danger" @click="del(row)">
              {{ $t('base.del') }}
            </TypographyLink>
            <Dropdown v-if="['1', '2'].includes(row.type)">
              <TypographyLink>
                <Space :size="0">
                  {{ $t('base.more') }}
                  <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
                </Space>
              </TypographyLink>
              <template #overlay>
                <Menu>
                  <MenuItem v-if="row.type === '1'" key="menu" @click="addSubNode(row)"> 新增子级 </MenuItem>
                  <MenuItem v-if="row.type === '2'" key="menu" @click="setButtonPermission(row)"> 按钮权限 </MenuItem>
                </Menu>
              </template>
            </Dropdown>
          </Space>
        </template>
      </Grid>
    </div>
    <Modal :title="modalTitle" :submitting="loading.submit">
      <MenuForm ref="menuFormRef" :app-code="appInfo.code as string" />
    </Modal>
    <BtnPermissionModal title="按钮权限" class="w-[800px]">
      <ButtonPermissionList ref="buttonPermissionListRef" />
    </BtnPermissionModal>
  </BasicPopup>
</template>

<style scoped></style>
