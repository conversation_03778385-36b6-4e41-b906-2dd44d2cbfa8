<script setup lang="ts">
import type { MenuClickEventHandler } from 'ant-design-vue/es/menu/src/interface';

import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { OrgInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { useModal } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, Dropdown, Menu, MenuItem, message, Space, TypographyLink } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addOrgApi, deleteOrgApi, editOrg<PERSON>pi, getOrgList<PERSON><PERSON>, getOrgPageList<PERSON>pi } from '#/api';
import CompanyForm from '#/views/org/manage/components/company-form.vue';
import OrgForm from '#/views/org/manage/components/org-form.vue';

import Member from './components/Member.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '组织名称',
    },
    {
      component: 'Input',
      fieldName: 'code',
      label: '组织编码',
    },
    {
      component: 'Select',
      fieldName: 'type',
      label: '组织类型',
      componentProps: {
        options: dictStore.getDictList('orgType'),
      },
    },
  ],
  fieldMappingTime: [['createTime', ['createTimeStart', 'createTimeEnd'], 'x']],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
  handleSubmit: async (values) => {
    gridApi.setLoading(true);
    const isEmpty = Object.values(values).every((value) => value === undefined || value === '');
    if (isEmpty) {
      const res = await getOrgListApi({ ...values, parentId: '-1' });
      res.forEach((item: OrgInfo & { hasChild: boolean }) => {
        item.hasChild = true;
      });
      await gridApi.grid.reloadData([]);
      gridApi.setGridOptions(gridOptions);
      await gridApi.grid.reloadData(res);
    } else {
      await gridApi.grid.reloadData([]);
      gridApi.setGridOptions(pageGridOptions);
      setTimeout(async () => {
        await gridApi.grid.commitProxy('query', values);
      });
    }
    gridApi.setLoading(false);
  },
  handleReset: async () => {
    await gridApi.formApi.resetForm();
    gridApi.setLoading(true);
    const res = await getOrgListApi({ parentId: '-1' });
    res.forEach((item: OrgInfo & { hasChild: boolean }) => {
      item.hasChild = true;
    });
    await gridApi.grid.reloadData([]);
    gridApi.setGridOptions(gridOptions);
    await gridApi.grid.reloadData(res);
    gridApi.setLoading(false);
  },
});
const init = async () => {
  const res = await getOrgListApi({ parentId: '-1' });
  res.forEach((item: OrgInfo & { hasChild: boolean }) => {
    item.hasChild = true;
  });
  await gridApi.grid.reloadData([]);
  await gridApi.grid.reloadData(res);
};
init();
const gridOptions: VxeTableGridOptions<OrgInfo> = {
  columns: [
    {
      field: 'name',
      title: '组织名称',
      treeNode: true,
      minWidth: 200,
      slots: { default: 'name' },
    },
    { field: 'code', title: '组织编码' },
    {
      field: 'type',
      title: '组织类型',
      formatter: ['formatStatus', 'orgType'],
    },
    { field: 'createTime', title: '创建时间', formatter: 'formatDateTime' },
    { field: 'sortCode', title: '排序' },
    {
      field: 'action',
      title: $t('base.action'),
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  rowConfig: {
    keyField: 'id',
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    lazy: true,
    async loadMethod({ row }: { row: OrgInfo }) {
      // 异步加载子节点
      const res = await getOrgListApi({ parentId: row.id });
      res.forEach((item: OrgInfo & { hasChild: boolean }) => {
        item.hasChild = true;
      });
      return res;
    },
  },
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const pageGridOptions: VxeTableGridOptions<OrgInfo> = {
  columns: [
    { type: 'seq', width: 70 },
    {
      field: 'name',
      title: '组织名称',
      minWidth: 200,
      slots: { default: 'name' },
    },
    { field: 'code', title: '组织编码' },
    {
      field: 'type',
      title: '组织类型',
      formatter: ['formatStatus', 'orgType'],
    },
    { field: 'createTime', title: '创建时间' },
    { field: 'sortCode', title: '排序' },
    {
      field: 'action',
      title: $t('base.action'),
      fixed: 'right',
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  rowConfig: {
    keyField: 'id',
  },
  pagerConfig: {
    enabled: true,
  },
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        try {
          return await getOrgPageListApi({
            current: page.currentPage,
            size: page.pageSize,
            ...formValues,
          });
        } catch (error) {
          console.error(error);
        }
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const orgFormRef = ref();
const modalTitle = ref($t('base.add'));
const orgForm = ref<Partial<OrgInfo>>({});
const [Modal, modalApi] = useVbenModal({
  onOpened: () => {
    orgFormRef.value.init(orgForm.value);
  },
  onConfirm: async () => {
    orgForm.value = orgFormRef.value.submit();
    let api = addOrgApi;
    if (orgForm.value.id) {
      api = editOrgApi;
    }
    try {
      loading.value.save = true;
      await api(orgForm.value as OrgInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.formApi.submitForm();
    } finally {
      loading.value.save = false;
    }
  },
  onClosed: () => {
    orgForm.value = {};
  },
});
const loading = ref({
  save: false,
});
const edit = (row: OrgInfo) => {
  orgForm.value = row;
  modalTitle.value = $t('base.edit');
  modalApi.open();
};
const addOrg = (info: { key: string }) => {
  orgForm.value = { type: info.key as OrgInfo['type'] };
  modalTitle.value = $t('base.add');
  modalApi.open();
};
const del = async (row: OrgInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await deleteOrgApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
const [registerMember, { openModal: openMemberModal }] = useModal();
const viewMember = (row: OrgInfo) => {
  openMemberModal(true, { id: row.id, name: row.name, objectType: 'ORGANIZE' });
};
</script>

<template>
  <Page auto-content-height>
    <!--<div class="bg-white px-2 pt-6">-->
    <!--  <SearchForm />-->
    <!--</div>-->
    <Grid>
      <template #toolbar-actions>
        <Dropdown>
          <Button class="mr-2" type="primary">
            <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
            {{ $t('base.add') }}
            <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
          </Button>
          <template #overlay>
            <Menu @click="addOrg as MenuClickEventHandler">
              <MenuItem v-for="item in dictStore.getDictList('orgType')" :key="item.dictValue">
                新建{{ item.dictName }}
              </MenuItem>
            </Menu>
          </template>
        </Dropdown>
      </template>
      <template #name="{ row }">
        <div class="flex items-center">
          <VbenIcon v-if="row.type === 'DEPARTMENT'" icon="clarity:group-line" class="text-base" />
          <VbenIcon v-else-if="row.type === 'COMPANY'" icon="clarity:organization-line" class="text-base" />
          <p class="ml-1">{{ row.name }}</p>
        </div>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="user" @click="viewMember(row)"> 组织成员 </MenuItem>
                <MenuItem key="view"> 查看权限 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </template>
    </Grid>
    <Modal :title="modalTitle" :confirm-loading="loading.save">
      <CompanyForm v-if="orgForm.type === 'COMPANY'" ref="orgFormRef" />
      <OrgForm v-else ref="orgFormRef" />
    </Modal>
    <Member @register="registerMember" />
  </Page>
</template>

<style></style>
