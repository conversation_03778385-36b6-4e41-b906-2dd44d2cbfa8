<script setup lang="ts">
import type { TreeProps } from 'ant-design-vue';
import type { Key } from 'ant-design-vue/es/_util/type';
import type { Rule } from 'ant-design-vue/es/form';
import type { DataNode, EventDataNode } from 'ant-design-vue/es/vc-tree/interface';

import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import {type OrgInfo, type UserInfo, validatePasswordApi} from '#/api';

import { computed, h, ref, unref } from 'vue';

import { ColPage, useVbenModal } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { PlusOutlined } from '@ant-design/icons-vue';
import {
  Form as AntdForm,
  Modal as AntdModal,
  Button,
  Card,
  Dropdown,
  FormItem,
  InputPassword,
  InputSearch,
  Menu,
  MenuItem,
  message,
  RadioButton,
  RadioGroup,
  Space,
  Tag,
  Tree,
  TypographyLink,
  TypographyText,
} from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delUserApi, getOrgListApi, getUserPageListApi, lockUserApi, setUserPasswordApi, unlockUserApi } from '#/api';

import Form from './form.vue';

const loading = ref({
  search: false,
  save: false,
});
const pickSearchValue = ref('');
const treeData = ref<DataNode[]>([]);
const expandedKeys = ref<string[]>([]);
const getOrgList = async (name?: string) => {
  treeData.value = [];
  loading.value.search = true;
  try {
    const res = await getOrgListApi({ name, parentId: name ? undefined : '-1' });
    if (name) {
      res.forEach((item: OrgInfo & { isLeaf: boolean }) => {
        item.isLeaf = true;
      });
    } else {
      res.forEach((item: OrgInfo & { isLeaf: boolean }) => {
        item.isLeaf = Boolean(item.isLeaf);
      });
    }
    treeData.value = res;
    expandedKeys.value = name ? [] : treeData.value.map((item) => item.id);
    if (treeData.value.length > 0) {
      selectKeys.value = [treeData.value[0]?.id as string];
      await gridApi.formApi.submitForm();
    }
  } finally {
    loading.value.search = false;
  }
};
getOrgList();
const onLoadData: TreeProps['loadData'] = async (treeNode) => {
  if (!treeNode.dataRef) {
    return;
  }
  if (treeNode.dataRef.children && treeNode.dataRef.children.length > 0) {
    return;
  }
  try {
    const res = await getOrgListApi({
      parentId: treeNode.dataRef.id,
    });
    res.forEach((item: OrgInfo & { isLeaf: boolean }) => {
      item.isLeaf = Boolean(item.isLeaf);
    });
    treeNode.dataRef.children = res;
  } catch (error) {
    console.error('加载子节点数据失败:', error);
  }
};
const selectKeys = ref<string[]>([]);
const selectOrganizeId = computed(() => {
  return selectKeys.value.length > 0 ? selectKeys.value[0] : '';
});
type InfoType = {
  event: 'select';
  nativeEvent: MouseEvent;
  node: EventDataNode;
  selected: boolean;
  selectedNodes: DataNode[];
};

const treeSelect = (_: Key[], e: Pick<InfoType, 'node'>) => {
  selectKeys.value = [e.node.id];
  gridApi.formApi.submitForm();
};
const fieldNames = {
  key: 'id',
  title: 'name',
  children: 'children',
};

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'userName',
      label: '账号',
    },
    {
      component: 'Input',
      fieldName: 'realName',
      label: '姓名',
    },
    {
      component: 'Input',
      fieldName: 'mobile',
      label: '电话',
    },
  ],
  fieldMappingTime: [['createTime', ['createTimeStart', 'createTimeEnd'], 'x']],
  // 按下回车时是否提交表单
  submitOnEnter: true,
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'code', title: '工号' },
    { field: 'userName', title: '账号' },
    { field: 'realName', title: '姓名', slots: { default: 'realName' } },
    { field: 'mobile', title: '手机号' },
    { field: 'organFullNames', title: '所属组织', minWidth: 200 },
    { field: 'lastLogTime', title: '最后登录时间', formatter: ['formatDateTime', '从不'] },
    { field: 'sortCode', title: '排序' },
    {
      field: 'status',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'userStatus',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async ({ page }, formValues) => {
        return await getUserPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
          organId: unref(selectOrganizeId),
          includeOrgSub: unref(includeOrgSub),
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
    slots: {
      tools: 'toolbarTools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const addUser = () => {
  openFormPopup(true, { organIds: selectKeys.value });
};
const edit = (row: UserInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.formApi.submitForm();
};
const includeOrgSub = ref('1');
const change = () => {
  gridApi.formApi.submitForm();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const del = async (row: UserInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await delUserApi(row.id as number);
      message.success($t('base.resSuccess'));
      await gridApi.formApi.submitForm();
    },
  });
};
const restPasswordRef = ref();
const [Modal, modalApi] = useVbenModal({
  onClosed() {
    restPasswordForm.value = cloneDeep(defaultRestPasswordInfo);
    restPasswordRef.value.resetFields();
  },
  async onConfirm() {
    await restPasswordRef.value.validate();
    try {
      loading.value.save = true;
      await setUserPasswordApi(restPasswordForm.value);
      message.success($t('base.resSuccess'));
      await modalApi.close();
    } finally {
      loading.value.save = false;
    }
  },
});
const defaultRestPasswordInfo = {
  id: 0,
  userName: '',
  newPassword: '',
  confirmPassword: '',
};
interface RestPasswordInfo {
  id: number;
  userName: string;
  newPassword: string;
  confirmPassword: string;
}
const restPasswordForm = ref<RestPasswordInfo>(cloneDeep(defaultRestPasswordInfo));
const resetPassword = (row: UserInfo) => {
  restPasswordForm.value = {
    id: row.id as number,
    userName: row.userName,
    newPassword: '',
    confirmPassword: '',
  };
  modalApi.open();
};
const validateConfirmPassword = async (_rule: Rule, value: string) => {
  if (value === '') {
    throw new Error('请输入确认密码');
  } else if (value !== restPasswordForm.value.newPassword) {
    throw new Error('两次输入的密码不一致');
  }
};
const validateNewPassword = async (_rule: Rule, value: string) => {
  if (!value) {
    throw new Error('请输入新密码');
  }
  const res = await validatePasswordApi({ password: value, userId: restPasswordForm.value.id });
  if (res.code !== 200) {
    throw new Error(res.msg);
  }
};
const restPasswordRules: Record<string, Rule[]> = {
  newPassword: [{ required: true, validator: validateNewPassword, trigger: 'blur' }],
  confirmPassword: [{ required: true, validator: validateConfirmPassword, trigger: 'blur' }],
};
const lockAccount = async (row: UserInfo) => {
  AntdModal.confirm({
    title: '锁定账号',
    content: '该操作将锁定该账号，锁定后账号将无法登录，是否继续？',
    async onOk() {
      try {
        loading.value.save = true;
        await lockUserApi([row.id as number]);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } finally {
        loading.value.save = false;
      }
    },
  });
};
const unlockAccount = async (row: UserInfo) => {
  AntdModal.confirm({
    title: '解除锁定',
    content: '该操作将锁定该账号，锁定后账号将无法登录，是否继续？',
    async onOk() {
      try {
        loading.value.save = true;
        await unlockUserApi([row.id as number]);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } finally {
        loading.value.save = false;
      }
    },
  });
};
</script>

<template>
  <ColPage :resizable="false" auto-content-height :left-width="20" :right-width="80">
    <template #left>
      <Card class="mr-4 h-full">
        <InputSearch v-model:value="pickSearchValue" :loading="loading.search" class="mb-2" @search="getOrgList" />
        <Tree
          v-model:expanded-keys="expandedKeys"
          v-model:selected-keys="selectKeys"
          :tree-data="treeData"
          :load-data="onLoadData"
          :field-names="fieldNames"
          @select="treeSelect"
        >
          <template #title="props">
            <div class="flex items-center">
              <VbenIcon v-if="props.type === 'DEPARTMENT'" icon="clarity:group-line" class="text-base" />
              <VbenIcon v-else-if="props.type === 'COMPANY'" icon="clarity:organization-line" class="text-base" />
              <p class="ml-1">{{ props.name }}</p>
            </div>
          </template>
        </Tree>
      </Card>
    </template>
    <Grid>
      <template #toolbar-actions>
        <Button type="primary" :icon="h(PlusOutlined)" @click="addUser">
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #toolbarTools>
        <RadioGroup v-model:value="includeOrgSub" @change="change">
          <RadioButton value="1">展示全部成员</RadioButton>
          <RadioButton value="0">展示直属成员</RadioButton>
        </RadioGroup>
      </template>
      <template #realName="{ row }">
        <Space>
          <span>{{ row.realName }}</span>
          <Tag v-if="row.isAdmin" color="processing">管理员</Tag>
        </Space>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="resetPassword" @click="resetPassword(row)"> 重置密码 </MenuItem>
                <MenuItem v-if="row.status === 1" key="lockAccount" @click="lockAccount(row)"> 锁定账号 </MenuItem>
                <MenuItem v-if="row.status === 0" key="unlockAccount" @click="unlockAccount(row)"> 解除锁定 </MenuItem>
                <MenuItem key="view"> 查看权限 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </template>
    </Grid>
    <Form @register="registerForm" @ok="editSuccess" />
    <Modal title="重置密码" :submitting="loading.save">
      <AntdForm
        ref="restPasswordRef"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
        :model="restPasswordForm as RestPasswordInfo"
        :rules="restPasswordRules"
      >
        <FormItem label="账号">
          <TypographyText>{{ restPasswordForm.userName }}</TypographyText>
        </FormItem>
        <FormItem label="密码" name="newPassword">
          <InputPassword v-model:value="restPasswordForm.newPassword" />
        </FormItem>
        <FormItem label="确认密码" name="confirmPassword">
          <InputPassword v-model:value="restPasswordForm.confirmPassword" />
        </FormItem>
      </AntdForm>
    </Modal>
  </ColPage>
</template>

<style></style>
