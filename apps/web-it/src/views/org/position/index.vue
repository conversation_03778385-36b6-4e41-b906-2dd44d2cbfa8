<script setup lang="ts">
import type { TreeProps } from 'ant-design-vue';
import type { Key } from 'ant-design-vue/es/_util/type';
import type { DataNode, EventDataNode } from 'ant-design-vue/es/vc-tree/interface';

import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { OrgInfo, PositionInfo } from '#/api';

import { computed, ref } from 'vue';

import { ColPage, useVbenModal } from '@vben/common-ui';
import { useModal } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import {
  Button,
  Card,
  Dropdown,
  InputSearch,
  Menu,
  MenuItem,
  message,
  Space,
  Tree,
  TypographyLink,
} from 'ant-design-vue';
import { Modal as AntdModal } from 'ant-design-vue/es/components';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { addPositionApi, deletePositionApi, editPositionApi, getOrgListApi, getPositionPageListApi } from '#/api';
import Member from '#/components/Member.vue';
import PositionForm from '#/views/org/position/components/position-form.vue';

const pickSearchValue = ref('');
const loading = ref({
  search: false,
  save: false,
});
const treeData = ref<DataNode[]>([]);
const expandedKeys = ref<string[]>([]);
const loadedKeys = ref<string[]>([]);
const getOrgList = async (name?: string) => {
  treeData.value = [];
  loadedKeys.value = [];
  loading.value.search = true;
  try {
    const res = await getOrgListApi({ name, parentId: name ? undefined : '-1' });
    if (name) {
      res.forEach((item: OrgInfo & { isLeaf: boolean }) => {
        item.isLeaf = true;
      });
    } else {
      res.forEach((item: OrgInfo & { isLeaf: boolean }) => {
        item.isLeaf = Boolean(item.isLeaf);
      });
    }
    treeData.value = res;
    expandedKeys.value = name ? [] : treeData.value.map((item) => item.id);
    if (treeData.value.length > 0) {
      selectKeys.value = [treeData.value[0]?.id as string];
      await gridApi.formApi.submitForm();
    }
  } finally {
    loading.value.search = false;
  }
};
getOrgList();
const onLoadData: TreeProps['loadData'] = async (treeNode) => {
  if (!treeNode.dataRef) {
    return;
  }
  if (treeNode.dataRef.children && treeNode.dataRef.children.length > 0) {
    return;
  }
  try {
    const res = await getOrgListApi({
      parentId: treeNode.dataRef.id,
    });
    if (res.length > 0) {
      res.forEach((item: OrgInfo & { isLeaf: boolean }) => {
        item.isLeaf = Boolean(item.isLeaf);
      });
    }
    treeNode.dataRef.children = res;
    loadedKeys.value.push(treeNode.dataRef.id as string);
  } catch (error) {
    console.error('加载子节点数据失败:', error);
  }
};
const selectKeys = ref<string[]>([]);
const selectOrganizeId = computed(() => {
  return selectKeys.value.length > 0 ? selectKeys.value[0] : '';
});
type InfoType = {
  event: 'select';
  nativeEvent: MouseEvent;
  node: EventDataNode;
  selected: boolean;
  selectedNodes: DataNode[];
};

const treeSelect = (_: Key[], e: Pick<InfoType, 'node'>) => {
  selectKeys.value = [e.node.id];
  gridApi.formApi.submitForm();
};
const fieldNames = {
  key: 'id',
  title: 'name',
  children: 'children',
};
const isSearch = ref(false);
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '岗位名称',
    },
    {
      component: 'Select',
      fieldName: 'enabled',
      label: '状态',
      componentProps: {
        options: [
          { label: '启用', value: 1 },
          { label: '禁用', value: 0 },
        ],
      },
    },
  ],
  fieldMappingTime: [['createTime', ['createTimeStart', 'createTimeEnd'], 'x']],
  // 按下回车时是否提交表单
  submitOnEnter: true,
  commonConfig: {
    colon: false,
  },
  handleSubmit: async (values = {}) => {
    const isEmpty = Object.values(values).every((value) => value === undefined || value === '');
    isSearch.value = !isEmpty;
    await gridApi.query(values);
  },
  handleReset: async () => {
    await gridApi.formApi.resetForm();
    isSearch.value = false;
    await gridApi.reload();
  },
});
const gridOptions: VxeTableGridOptions<AppInfo> = {
  columns: [
    { field: 'seq', type: 'seq', width: 80 },
    { field: 'name', title: '岗位名称' },
    { field: 'code', title: '岗位编码' },
    { field: 'organFullName', title: '所属组织' },
    { field: 'createTime', title: '创建时间', formatter: 'formatDateTime' },
    { field: 'sortCode', title: '排序' },
    {
      field: 'enabled',
      title: '状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'baseEnableType',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 220,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    autoLoad: false,
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPositionPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
          organId: isSearch.value ? undefined : selectOrganizeId.value,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const positionFormRef = ref();
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    const formInfo = await positionFormRef.value.onSubmit();
    let api = addPositionApi;
    if (formInfo.id) {
      api = editPositionApi;
    }
    try {
      loading.value.save = true;
      await api(formInfo);
      message.success($t('base.resSuccess'));
      await modalApi.close();
      await gridApi.reload();
    } finally {
      loading.value.save = false;
    }
  },
  onOpened() {
    const value = modalApi.getData<Record<string, any>>();
    positionFormRef.value.init(value);
  },
});
const modalTitle = ref($t('base.add'));
const addPosition = () => {
  modalTitle.value = $t('base.add');
  modalApi.setData({ organId: selectOrganizeId.value }).open();
};
const edit = (row: PositionInfo) => {
  modalTitle.value = $t('base.edit');
  modalApi.setData(row).open();
};
const del = async (row: PositionInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmDelTitle'),
    content: $t('base.confirmDelContent'),
    async onOk() {
      await deletePositionApi(row.id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
function handleMember(row: PositionInfo) {
  openMemberModal(true, { id: row.id, name: row.name, objectType: 'POST' });
}
const [registerMember, { openModal: openMemberModal }] = useModal();
</script>

<template>
  <ColPage :resizable="false" auto-content-height :left-width="20" :right-width="80">
    <template #left>
      <Card class="mr-4 h-full">
        <InputSearch v-model:value="pickSearchValue" :loading="loading.search" class="mb-2" @search="getOrgList" />
        <Tree
          v-if="!isSearch"
          v-model:expanded-keys="expandedKeys"
          v-model:selected-keys="selectKeys"
          :loaded-keys="loadedKeys"
          :tree-data="treeData"
          :load-data="onLoadData"
          :field-names="fieldNames"
          @select="treeSelect"
        >
          <template #title="props">
            <div class="flex items-center">
              <VbenIcon v-if="props.type === 'DEPARTMENT'" icon="clarity:group-line" class="text-base" />
              <VbenIcon v-else-if="props.type === 'COMPANY'" icon="clarity:organization-line" class="text-base" />
              <p class="ml-1">{{ props.name }}</p>
            </div>
          </template>
        </Tree>
        <Tree
          v-else
          :tree-data="[{ id: 'result', key: 'result', name: '搜索结果', isLeaf: false }]"
          :field-names="fieldNames"
          :selected-keys="['result']"
        >
          <template #title="props">
            <div class="flex items-center">
              <VbenIcon icon="clarity:organization-line" class="text-base" />
              <p class="ml-1">{{ props.name }}</p>
            </div>
          </template>
        </Tree>
      </Card>
    </template>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="addPosition">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="edit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <Dropdown>
            <TypographyLink>
              <Space :size="0">
                {{ $t('base.more') }}
                <VbenIcon class="ml-1" icon="ant-design:down-outlined" />
              </Space>
            </TypographyLink>
            <template #overlay>
              <Menu>
                <MenuItem key="user" @click="handleMember(row)"> 岗位成员 </MenuItem>
                <MenuItem key="view"> 查看权限 </MenuItem>
              </Menu>
            </template>
          </Dropdown>
        </Space>
      </template>
    </Grid>
    <Modal :title="modalTitle" :confirm-loading="loading.save">
      <PositionForm ref="positionFormRef" />
    </Modal>
    <Member @register="registerMember" />
  </ColPage>
</template>

<style scoped></style>
