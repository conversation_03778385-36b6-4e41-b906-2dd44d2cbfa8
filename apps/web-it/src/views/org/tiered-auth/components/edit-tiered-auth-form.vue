<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { GroupInfo, OrgInfo } from '#/api';
import type { OrganizeAdminInfo, OrganizeAdminListItem } from '#/api/org/tiered-auth';

import { ref } from 'vue';

import { FeCheckboxSingle } from '@vben/fe-ui';
import FeUserSelect from '@vben/fe-ui/components/Fe/Organize/src/UserSelect.vue';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Form, FormItem } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getOrgListApi, getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi } from '#/api';

const authForm = ref<OrganizeAdminInfo>({
  id: '',
  organAdminList: [],
});
const rules: Record<string, Rule[]> = {
  id: [{ required: true, message: '请选择管理员', trigger: 'change' }],
};
const gridOptions: VxeTableGridOptions<OrgInfo> = {
  columns: [
    {
      field: 'name',
      title: '组织架构',
      treeNode: true,
      minWidth: 200,
      slots: { default: 'name' },
    },
    { field: 'currentNode', title: '组织操作权限(本层级)', minWidth: 300, slots: { default: 'currentNode' } },
    {
      field: 'subNode',
      title: '子组织操作权限(子层级)',
      minWidth: 300,
      slots: { default: 'subNode' },
    },
  ],
  rowConfig: {
    keyField: 'id',
  },
  treeConfig: {
    rowField: 'id',
    parentField: 'parentId',
    lazy: true,
    async loadMethod({ row }: { row: OrgInfo }) {
      // 异步加载子节点
      const res = await getOrgListApi({ parentId: row.id });
      res.forEach((item: OrgInfo & { hasChild: boolean }) => {
        item.hasChild = true;
      });
      return res;
    },
  },
  proxyConfig: {
    ajax: {
      query: async () => {
        try {
          const res = await getOrgListApi({ parentId: -1 });
          res.forEach((item: OrgInfo & { hasChild: boolean }) => {
            item.hasChild = true;
          });
          return res;
        } catch (error) {
          console.error(error);
        }
      },
    },
  },
  border: 'none',
  pagerConfig: {
    enabled: false,
  },
  toolbarConfig: {
    custom: false,
    refresh: true,
    resizable: false,
    zoom: false,
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
const authFormRef = ref();
/**
 * 将树形结构转换为 OrganizeAdminListItem[] 格式
 * @param treeData 树形数据
 * @returns OrganizeAdminListItem[]
 */
const convertTreeToOrganizeAdminList = (treeData: any[]): OrganizeAdminListItem[] => {
  const result: OrganizeAdminListItem[] = [];

  const processNode = (node: any) => {
    // 创建符合 OrganizeAdminListItem 接口的对象
    const item: OrganizeAdminListItem = {
      // 将 id 转换为 organId
      organId: Number(node.id),
      // 直接使用 type 作为 organType
      organType: node.type,
      thisLayerAdd: node.thisLayerAdd || 0,
      thisLayerEdit: node.thisLayerEdit || 0,
      thisLayerDelete: node.thisLayerDelete || 0,
      thisLayerSelect: node.thisLayerSelect || 0,
      subLayerAdd: node.subLayerAdd || 0,
      subLayerEdit: node.subLayerEdit || 0,
      subLayerDelete: node.subLayerDelete || 0,
      subLayerSelect: node.subLayerSelect || 0,
    };

    // 只有当至少有一个权限被设置时才添加到结果中
    if (
      item.thisLayerAdd ||
      item.thisLayerEdit ||
      item.thisLayerDelete ||
      item.thisLayerSelect ||
      item.subLayerAdd ||
      item.subLayerEdit ||
      item.subLayerDelete ||
      item.subLayerSelect
    ) {
      result.push(item);
    }

    // 递归处理子节点
    if (node.children && node.children.length > 0) {
      node.children.forEach((child: any) => {
        processNode(child);
      });
    }
  };

  // 遍历所有根节点
  treeData.forEach((node) => {
    processNode(node);
  });

  return result;
};
const changeThisViewSelect = (value: 0 | 1, row: OrganizeAdminListItem) => {
  if (value === 0) {
    row.thisLayerAdd = 0;
    row.thisLayerEdit = 0;
    row.thisLayerDelete = 0;
  }
};
const changeSubViewSelect = (value: 0 | 1, row: OrganizeAdminListItem) => {
  if (value === 0) {
    row.subLayerAdd = 0;
    row.subLayerEdit = 0;
    row.subLayerDelete = 0;
  }
};
const onSubmit = async () => {
  await authFormRef.value.validate();
  const treeData = gridApi.grid.getFullData();

  // 将树形数据转换为 OrganizeAdminListItem[] 格式
  const organAdminList = convertTreeToOrganizeAdminList(treeData);

  // 构建最终的提交数据
  const submitData: OrganizeAdminInfo = {
    id: authForm.value.id,
    organAdminList,
  };
  return submitData;
};
const init = (_info: GroupInfo) => {};
defineExpose({ onSubmit, init });
</script>

<template>
  <div>
    <Form ref="authFormRef" :model="authForm" :rules="rules">
      <FormItem label="管理员" name="id">
        <FeUserSelect
          v-model:value="authForm.id"
          :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi }"
        />
      </FormItem>
    </Form>
    <Grid>
      <template #name="{ row }">
        <div class="flex items-center">
          <VbenIcon v-if="row.type === 'DEPARTMENT'" icon="clarity:group-line" class="text-base" />
          <VbenIcon v-else-if="row.type === 'COMPANY'" icon="clarity:organization-line" class="text-base" />
          <p class="ml-1">{{ row.name }}</p>
        </div>
      </template>
      <template #currentNode="{ row }">
        <FeCheckboxSingle
          v-model:value="row.thisLayerSelect"
          label="查看"
          @change="(value: 0 | 1) => changeThisViewSelect(value, row)"
        />
        <FeCheckboxSingle
          v-model:value="row.thisLayerAdd"
          label="新增"
          :disabled="!row.thisLayerSelect || row.thisLayerSelect === 0"
        />
        <FeCheckboxSingle
          v-model:value="row.thisLayerEdit"
          label="编辑"
          :disabled="!row.thisLayerSelect || row.thisLayerSelect === 0"
        />
        <FeCheckboxSingle
          v-model:value="row.thisLayerDelete"
          label="删除"
          :disabled="!row.thisLayerSelect || row.thisLayerSelect === 0"
        />
      </template>
      <template #subNode="{ row }">
        <FeCheckboxSingle
          v-model:value="row.subLayerSelect"
          label="查看"
          @change="(value: 0 | 1) => changeSubViewSelect(value, row)"
        />
        <FeCheckboxSingle
          v-model:value="row.subLayerAdd"
          label="新增"
          :disabled="!row.subLayerSelect || row.subLayerSelect === 0"
        />
        <FeCheckboxSingle
          v-model:value="row.subLayerEdit"
          label="编辑"
          :disabled="!row.subLayerSelect || row.subLayerSelect === 0"
        />
        <FeCheckboxSingle
          v-model:value="row.subLayerDelete"
          label="删除"
          :disabled="!row.subLayerSelect || row.subLayerSelect === 0"
        />
      </template>
    </Grid>
  </div>
</template>

<style></style>
