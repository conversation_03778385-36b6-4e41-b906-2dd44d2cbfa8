<script setup lang="ts">
import type { AppInfo, UserInfo } from '#/api';

import { computed, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Button, Checkbox, CheckboxGroup, Space, Steps, Tree, TypographyLink } from 'ant-design-vue';

import { getAppListApi, getMenuTreeApi } from '#/api';

const userInfo = ref<Partial<UserInfo>>({});
const title = computed(() => {
  return `${userInfo.value.realName}的分级授权`;
});
const init = (data: UserInfo) => {
  userInfo.value = data;
};
const [registerPopup] = usePopupInner(init);
const current = ref(0);
const stepItems = [{ title: '应用权限' }, { title: '菜单权限' }];
const appList = ref<AppInfo[]>([]);
const authForm = ref<{ appIds: string[] }>({
  appIds: [],
});
const getAppList = async () => {
  appList.value = await getAppListApi();
};
getAppList();
const prev = () => {
  current.value = 0;
};
const next = () => {
  current.value = 1;
  getMenuList();
};
/**
 * 递归提取所有节点的 id
 * @param treeData 树形数据
 * @returns 所有节点的 id 数组
 */
const getAllNodeIds = (treeData: any[]): string[] => {
  const ids: string[] = [];

  const extractIds = (nodes: any[]) => {
    if (!nodes || nodes.length === 0) return;

    for (const node of nodes) {
      // 添加当前节点的 id
      if (node.id) {
        ids.push(node.id);
      }

      // 递归处理子节点
      if (node.children && node.children.length > 0) {
        extractIds(node.children);
      }
    }
  };

  extractIds(treeData);
  return ids;
};

const checkAll = () => {
  if (current.value === 0) {
    authForm.value.appIds = appList.value.map((item) => item.id);
  } else if (current.value === 1) {
    // 获取所有节点的 id 并设置到 checkedKeys
    checkedKeys.value = getAllNodeIds(menuTreeList.value);
  }
};
const uncheckAll = () => {
  if (current.value === 0) {
    authForm.value.appIds = [];
  } else if (current.value === 1) {
    // 清空所有选中的节点
    checkedKeys.value = [];
  }
};
const expandAll = () => {
  if (current.value === 1) {
    expandedKeys.value = getAllNodeIds(menuTreeList.value);
  }
};
const foldAll = () => {
  if (current.value === 1) {
    expandedKeys.value = [];
  }
};
const changeSteps = (current: number) => {
  if (current === 1) {
    getMenuList();
  }
};
const menuTreeList = ref([]);
const expandedKeys = ref<string[]>([]);
const checkedKeys = ref<string[]>([]);
const getMenuList = async () => {
  menuTreeList.value = await getMenuTreeApi({ appCode: 'testCode' });
};
const close = () => {
  userInfo.value = {};
  current.value = 0;
  authForm.value = {
    appIds: [],
  };
  checkedKeys.value = [];
  expandedKeys.value = [];
  menuTreeList.value = [];
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup" @close="close">
    <div class="px-8">
      <BasicCaption content="应用权限">
        <template #action>
          <Space class="mr-4">
            <TypographyLink @click="expandAll">展开全部</TypographyLink>
            <TypographyLink @click="foldAll">折叠全部</TypographyLink>
            <TypographyLink @click="checkAll">勾选全部</TypographyLink>
            <TypographyLink @click="uncheckAll">取消全部</TypographyLink>
          </Space>
          <Space>
            <Button :disabled="current === 0" @click="prev">上一步</Button>
            <Button :disabled="current === 1" @click="next">下一步</Button>
            <Button type="primary">保存</Button>
          </Space>
        </template>
      </BasicCaption>
      <div class="p-4">
        <Steps v-model:current="current" :items="stepItems" @change="changeSteps" />
      </div>
      <div v-if="current === 0" class="p-4">
        <CheckboxGroup v-model:value="authForm.appIds">
          <div class="flex flex-col">
            <Checkbox v-for="item in appList" :key="item.id" :value="item.id" class="items-center">
              <div class="flex items-center">
                <VbenIcon :icon="item.icon" class="mr-1 text-lg" />
                <span>{{ item.name }}</span>
              </div>
            </Checkbox>
          </div>
        </CheckboxGroup>
      </div>
      <div v-else-if="current === 1" class="p-4">
        <Tree
          v-model:expanded-keys="expandedKeys"
          v-model:checked-keys="checkedKeys"
          :tree-data="menuTreeList"
          checkable
          :field-names="{ key: 'id', title: 'name' }"
        >
          <template #title="props">
            <div class="flex items-center">
              <VbenIcon :icon="props.icon" class="mr-1 text-lg" />
              <span>{{ props.name }}</span>
            </div>
          </template>
        </Tree>
      </div>
    </div>
  </BasicPopup>
</template>

<style></style>
