import { requestClient } from '#/api/request';

export const getCloudDiskPageListApi = (params: { page: string }) => {
  return requestClient.get('/cloud-disk/page-list', { params });
};
export const createFolderApi = (data: { fullName: string }) => {
  return requestClient.post('/cloud-disk/create-document', data);
};
export const updateFolderApi = (data: { fullName: string }) => {
  return requestClient.post('/cloud-disk/create-document', data);
};
export const shareFileApi = (data: { ids: string[]; userIds: string[] }) => {
  return requestClient.post('/cloud-disk/create-document', data);
};
export const downloadFileApi = (data: { ids: string[] }) => {
  return requestClient.post('/cloud-disk/pack-download', data);
};
export const delFileApi = (data: { ids: string[] }) => {
  return requestClient.post('/cloud-disk/create-document', data);
};
export const fileInfoApi = (params: { id: string }) => {
  return requestClient.get('/cloud-disk/file-info', { params });
};
export const getFolderTreeApi = () => {
  return requestClient.get('/cloud-disk/folder-tree');
};
export const moveToApi = (data: { ids: string; toId: string }) => {
  return requestClient.post('/cloud-disk/create-document', data);
};
export const shareFileCancelApi = (ids: string[]) => {
  return requestClient.post('/cloud-disk/folder-tree', ids);
};
export const restoreApi = (ids: string[]) => {
  return requestClient.post('/cloud-disk/folder-tree', ids);
};
export const permanentDelApi = (ids: string[]) => {
  return requestClient.post('/cloud-disk/folder-tree', ids);
};
