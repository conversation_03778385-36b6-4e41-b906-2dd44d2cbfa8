import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface LogSystemInfo {
  pid: string;
  type: string;
  title: string;
  platform: string;
  time: number;
  serviceId: string;
  serverIp: string;
  serverHost: string;
  status: string;
  env: string;
  remoteIp: string;
  remoteAddress: string;
  userAgent: string;
  client: string;
  os: string;
  device: string;
  path: string;
  requestUrl: string;
  method: string;
  response: string;
  request: string;
  userId: string;
  userName: string;
  account: string;
  operateTime: number;
  tenantId: string;
}

export async function getLogPageListApi(params: PageListParams) {
  return requestClient.get('/log/system/page-list', { params });
}
export async function getLogDetailApi(params: { pid: string }) {
  return requestClient.get('/log/system/detail', { params });
}
