import type { BaseDataParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface MenuInfo extends BaseDataParams {
  /** 上级id */
  parentId?: number;
  /** 类型 (1-目录, 2-页面, 3-外链) */
  type?: '1' | '2' | '3';
  /** 名称 */
  name?: string;
  /** 编码 */
  code?: string;
  /** 分类 */
  category?: string;
  /** 路由地址 */
  urlAddress?: string;
  /** 组件 */
  component?: string;
  /** 菜单图标 */
  icon?: string;
  /** 链接目标 */
  linkTarget?: '_blank' | '_self';
  /** 关联功能id */
  moduleId?: string;
  /** 系统id */
  appId?: string;
  /** 扩展属性 */
  propertyJson?: string;
  /** 描述 */
  description?: string;
  /** 有效标志 (0-禁用，1-启用) */
  enabled?: number;
  /** 排序 */
  sortCode?: number;
}

export async function getMenuTreeApi(params: {
  appCode: string;
  code?: string;
  enabled?: string;
  name?: string;
  type?: '1' | '2' | '3';
}) {
  return requestClient.get('/upms/module/tree', { params });
}
export async function addMenuApi(data: MenuInfo) {
  return requestClient.post('/upms/module/add', data);
}
export async function editMenuApi(data: MenuInfo) {
  return requestClient.post('/upms/module/edit', data);
}
export async function delMenuApi(id: string) {
  return requestClient.post('/upms/module/delete', {}, { params: { id } });
}
