import type { BaseDataParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface AppInfo extends BaseDataParams {
  code: string;
  name: string;
  type: string;
  sortCode: number;
  url: string;
  enabled: number;
  description: string;
  icon: string;
}

export async function getAppPageListApi(params: { page: string }) {
  return requestClient.get('/upms/app/page', { params });
}
export async function getAppListApi() {
  return requestClient.get<AppInfo[]>('/upms/app/list');
}
export async function addAppApi(data: AppInfo) {
  return requestClient.post('/upms/app/add', data);
}
export async function editAppApi(data: AppInfo) {
  return requestClient.post('/upms/app/edit', data);
}
export async function deleteAppApi(id: string) {
  return requestClient.post('/upms/app/delete', {}, { params: { id } });
}
