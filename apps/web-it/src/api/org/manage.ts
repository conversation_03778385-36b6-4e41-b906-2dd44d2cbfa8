import type { BaseDataParams, PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface OrgInfo extends BaseDataParams {
  // 编码
  code?: string;
  // 名称
  name?: string;
  // 完整名称
  fullName?: string;
  // 类型（枚举值: COMPANY, DEPARTMENT）
  type?: 'COMPANY' | 'DEPARTMENT';
  // 描述
  description?: string;
  // 排序
  sortCode?: number;
  // 主管Id
  managerIds?: string[];
  // 上级主键
  parentId?: string;
  // 上级名称
  parentName?: string;
  // 上级完整名称
  parentFullName?: string;
  // 上级路径
  parentPath?: string;
  // 层级
  level?: number;
  // 扩展属性
  propertyJson?: string;
}
export async function getOrgListApi(
  params: Partial<{
    code: string;
    name: string;
    parentId: number;
    type: string;
  }>,
) {
  return requestClient.get('/upms/organ/async/list', { params });
}
export async function getOrgPageListApi(
  params: Partial<
    PageListParams & {
      code: string;
      name: string;
      parentId: string;
      type: string;
    }
  >,
) {
  return requestClient.get('/upms/organ/page', { params });
}

export async function addOrgApi(data: OrgInfo) {
  return requestClient.post('/upms/organ/add', data);
}
export async function editOrgApi(data: OrgInfo) {
  return requestClient.post('/upms/organ/edit', data);
}
export async function deleteOrgApi(id: string) {
  return requestClient.post('/upms/organ/delete', {}, { params: { id } });
}
export async function getOrgListPyIdsApi(ids: string[]) {
  return requestClient.post('/upms/organ/list_by_ids', ids);
}
