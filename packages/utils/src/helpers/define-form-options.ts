import type { VbenFormProps } from '@vben-core/form-ui';

import defaultsDeep from 'lodash.defaultsdeep';

function defineFormOptions(options: VbenFormProps) {
  options = defaultsDeep(options, {
    schema: [],
    collapsed: true,
    submitOnEnter: true,
    wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
    commonConfig: {
      colon: false,
      componentProps: {
        class: 'w-full',
      },
    },
  });
  return options;
}
export { defineFormOptions };
