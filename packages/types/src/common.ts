interface PageListParams {
  current: number;
  size: number;
}
interface BaseDataParams {
  // 主键
  id?: number;
  // 创建人
  createBy?: string;
  // 创建时间
  createTime?: string;
  // 更新人
  updateBy?: string;
  // 更新时间
  updateTime?: string;
  // 逻辑删除
  deleteFlag?: number;
  // 租户主键
  tenantId?: string;
}
interface Pagination<T> {
  current: number;
  records: T[];
  size: number;
  total: number;
}
interface ConfigInfo {
  // 企业号Id
  qyhCorpId: string;
  // 启用同步用户
  qyhSynIsSynUser: number;
  // 凭证密钥
  qyhAppSecret: string;
  // 同步密钥
  qyhCorpSecret: string;
  // 应用凭证
  qyhAgentId: string;
  // 启用同步组织
  qyhSynIsSynOrg: number;
  // 企业号Id
  dingAgentId: string;
  // 凭证密钥
  dingSynAppSecret: string;
  // 启用同步用户
  dingSynIsSynUser: number;
  // 启用同步组织
  dingSynIsSynOrg: number;
  // 应用凭证
  dingSynAppKey: string;
  // 登录方式
  loginType: string;
  // 新用户默认密码
  newUserDefaultPassword: string;
  // 包含字符串
  containsCharacters: number;
  // 密码强度限制
  passwordStrengthLimit: number;
  // 包含小写字母
  includeLowercaseLetters: number;
  // 密码最小长度
  passwordLengthMin: number;
  // 密码最小长度值
  passwordLengthMinNumber: number;
  // 包含大写字母
  includeUppercaseLetters: number;
  // 修改初始密码提醒
  mandatoryModificationOfInitialPassword: number;
  // 包含数字
  containsNumbers: number;
  // 密码错误次数
  passwordErrorsNumber: number;
  // 密码定期更新
  passwordIsUpdatedRegularly: number;
  // 禁用旧密码
  disableOldPassword: number;
  // 禁用旧密码数量
  disableNumberOfOldPasswords: number;
  // 密码更新周期（天）
  passwordUpdatedCycle: number;
  // 提前提醒天数（天）
  reminderDaysInAdvance: number;
  // 登录验证码
  verificationCodeSwitch: number;
  // 超时登录
  tokenTimeout: number;
  // 白名单验证
  whitelistSwitch: number;
  // 验证码类型
  verificationCodeType: string;
  // 错误锁定时间
  lockTime: number;
  // 白名单设置
  whitelistIp: string;
  // SSL安全连接
  emailSsl: number;
  // SMTP密码
  emailPassword: string;
  // SMTP端口
  emailSmtpPort: number;
  // 发件人昵称
  emailSenderName: string;
  // SMTP用户名
  emailAccount: string;
  // SMTP服务器
  emailSmtpHost: string;
  // Logo图标
  imgLogo: string;
  // Favicon图标
  imgFavicon: string;
  // 登录背景图
  imgLoginBackground: string;
  // 标题
  title: string;
  // 页脚信息
  copyright: string;
  // 备案号
  icpLicense: string;
  // 登录页显示
  isLoginShow: number;
  // 导航栏显示
  isNavbarShow: number;
  // 默认模型
  defaultModel: string;
  // 同IP错误次数
  ipErrorsNumber: number;
}
export type { BaseDataParams, ConfigInfo, PageListParams, Pagination };
