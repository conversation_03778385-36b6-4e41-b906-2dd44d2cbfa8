{"name": "@vben/stores", "version": "5.5.7", "homepage": "https://github.com/vbenjs/vue-vben-admin", "bugs": "https://github.com/vbenjs/vue-vben-admin/issues", "repository": {"type": "git", "url": "git+https://github.com/vbenjs/vue-vben-admin.git", "directory": "packages/stores"}, "license": "MIT", "type": "module", "sideEffects": ["**/*.css"], "exports": {".": {"types": "./src/index.ts", "default": "./src/index.ts"}}, "dependencies": {"@vben-core/preferences": "workspace:*", "@vben-core/shared": "workspace:*", "@vben-core/typings": "workspace:*", "pinia": "catalog:", "pinia-plugin-persistedstate": "catalog:", "@vueuse/integrations": "catalog:", "universal-cookie": "catalog:", "secure-ls": "catalog:", "tldjs": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@types/tldjs": "catalog:"}}