import { ref } from 'vue';

import { defineStore } from 'pinia';

export interface BasicConfigInfo {
  // 包含字符串
  containsCharacters: number;
  // 包含数字
  containsNumbers: number;
  // 页脚信息
  copyright: string;
  // 默认模型
  defaultModel: string;
  // 企业号Id
  dingAgentId: string;
  // 应用凭证
  dingSynAppKey: string;
  // 凭证密钥
  dingSynAppSecret: string;
  // 启用同步组织
  dingSynIsSynOrg: number;
  // 启用同步用户
  dingSynIsSynUser: number;
  // 禁用旧密码数量
  disableNumberOfOldPasswords: number;
  // 禁用旧密码
  disableOldPassword: number;
  // SMTP用户名
  emailAccount: string;
  // SMTP密码
  emailPassword: string;
  // 发件人昵称
  emailSenderName: string;
  // SMTP服务器
  emailSmtpHost: string;
  // SMTP端口
  emailSmtpPort: number;
  // SSL安全连接
  emailSsl: number;
  // 备案号
  icpLicense: string;
  // Favicon图标
  imgFavicon: string;
  // 登录背景图
  imgLoginBackground: string;
  // Logo图标
  imgLogo: string;
  // 包含小写字母
  includeLowercaseLetters: number;
  // 包含大写字母
  includeUppercaseLetters: number;
  // 登录页显示
  isLoginShow: number;
  // 导航栏显示
  isNavbarShow: number;
  // 错误锁定时间
  lockTime: number;
  // 登录方式
  loginType: string;
  // 修改初始密码提醒
  mandatoryModificationOfInitialPassword: number;
  // 新用户默认密码
  newUserDefaultPassword: string;
  // 密码错误次数
  passwordErrorsNumber: number;
  // 密码定期更新
  passwordIsUpdatedRegularly: number;
  // 密码最小长度
  passwordLengthMin: number;
  // 密码最小长度值
  passwordLengthMinNumber: number;
  // 密码强度限制
  passwordStrengthLimit: number;
  // 密码更新周期（天）
  passwordUpdatedCycle: number;
  // 应用凭证
  qyhAgentId: string;
  // 凭证密钥
  qyhAppSecret: string;
  // 企业号Id
  qyhCorpId: string;
  // 同步密钥
  qyhCorpSecret: string;
  // 启用同步组织
  qyhSynIsSynOrg: number;
  // 启用同步用户
  qyhSynIsSynUser: number;
  // 提前提醒天数（天）
  reminderDaysInAdvance: number;
  // 标题
  title: string;
  // 超时登录
  tokenTimeout: number;
  // 登录验证码
  verificationCodeSwitch: number;
  // 验证码类型
  verificationCodeType: string;
  // 白名单设置
  whitelistIp: string;
  // 白名单验证
  whitelistSwitch: number;
}

export const useSystemConfigStore = defineStore(
  'systemConfig',
  () => {
    const systemConfig = ref<Partial<BasicConfigInfo>>({});
    function setSystemConfig(basicConfigInfo: BasicConfigInfo) {
      systemConfig.value = basicConfigInfo;
    }
    function $reset() {
      // 配置信息需要持久化，不默认清空
    }
    return { $reset, setSystemConfig, systemConfig };
  },
  {
    persist: {
      // 持久化
      pick: ['systemConfig'],
    },
  },
);
