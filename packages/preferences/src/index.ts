import type { Preferences } from '@vben-core/preferences';
import type { DeepPartial } from '@vben-core/typings';

import { merge } from '@vben-core/shared/utils';

/**
 * 如果你想所有的app都使用相同的默认偏好设置，你可以在这里定义
 * 而不是去修改 @vben-core/preferences 中的默认偏好设置
 * @param preferences
 * @returns
 */

function defineOverridesPreferences(preferences: DeepPartial<Preferences>) {
  const defaultPreferences: DeepPartial<Preferences> = {
    app: {
      enableRefreshToken: true,
    },
    breadcrumb: {
      enable: false,
    },
    header: {
      height: 60,
    },
    sidebar: {
      collapsedButton: false,
      fixedButton: false,
    },
    tabbar: {
      styleType: 'brisk',
    },
    theme: {
      mode: 'light',
      radius: '0.25',
    },
    widget: {
      languageToggle: false,
      refresh: false,
    },
  };
  return merge(preferences, defaultPreferences);
}

export { defineOverridesPreferences };

export * from '@vben-core/preferences';
