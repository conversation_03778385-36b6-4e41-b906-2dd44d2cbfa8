import type { ComputedRef, Ref } from 'vue';

interface BasicOption {
  label: string;
  value: string;
}

type SelectOption = BasicOption;

type TabOption = BasicOption;

interface BasicUserInfo {
  /**
   * 头像
   */
  avatar: string;
  /**
   * 用户昵称
   */
  realName: string;
  /**
   * 用户角色
   */
  roles?: string[];
  /**
   * 用户id
   */
  userId: string;
  /**
   * 用户名
   */
  username: string;
}

type ClassType = Array<object | string> | object | string;

declare type ElRef<T extends HTMLElement = HTMLDivElement> = Nullable<T>;

declare type Fn = (...args: any[]) => any;

declare type RefType<T> = null | T;

declare type ComponentRef<T extends HTMLElement = HTMLDivElement> = ComponentElRef<T> | null;

type DynamicProps<T> = {
  [P in keyof T]: ComputedRef<T[P]> | Ref<T[P]> | T[P];
};
declare type LabelValueOptions = {
  [key: string]: boolean | number | string;
  label: string;
  value: any;
}[];

declare type EmitType = (event: string, ...args: any[]) => void;

declare type TargetContext = '_blank' | '_self';

export type {
  BasicOption,
  BasicUserInfo,
  ClassType,
  ComponentRef,
  DynamicProps,
  ElRef,
  EmitType,
  Fn,
  LabelValueOptions,
  RefType,
  SelectOption,
  TabOption,
  TargetContext,
};
