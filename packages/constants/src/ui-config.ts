// 表单栅格布局
export const COL_SPAN_PROP = { md: 12, sm: 24 };
// 表单默认配置
export const FORM_PROP = {
  colon: false,
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
// 独占一行的表单项配置
export const FULL_FORM_ITEM_PROP = {
  labelCol: { span: 2 },
  wrapperCol: { span: 22 },
};
// 详情描述默认配置
export const DESCRIPTIONS_PROP = {
  // bordered: true,
  column: 2,
  contentStyle: {},
  labelStyle: {
    width: '160px',
    justifyContent: 'flex-end',
  },
};
type Border = '' | 'default' | 'full' | 'inner' | 'none' | 'outer' | boolean;
// 详情页表单默认配置
export const DETAIL_GRID_OPTIONS = {
  pagerConfig: {
    enabled: false,
  },
  border: 'inner' as Border,
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
    custom: false,
    refresh: false,
    resizable: false,
    zoom: false,
  },
};
// 编辑详情页默认class
export const BASE_PAGE_CLASS_NAME = 'px-10';
