<script lang="ts">
import type { Ref } from 'vue';

import type { Nullable, Recordable } from '@vben/types';

import type { FormActionType, FormProps, FormSchema } from './types/form';
import type { AdvanceState } from './types/hooks';

import { computed, defineComponent, nextTick, onMounted, reactive, ref, unref, watch } from 'vue';

import { useDebounceFn } from '@vueuse/core';
import { Form, Row } from 'ant-design-vue';

import { useModalContext } from '#/components/Modal';
import { useDesign } from '#/hooks/web/useDesign';
// import { cloneDeep } from 'lodash-es';
import { deepMerge } from '#/utils';
import { isArray, isFunction } from '#/utils/is';
import { buildUUID } from '#/utils/uuid';

import FormAction from './components/FormAction.vue';
import FormItem from './components/FormItem.vue';
import useAdvanced from './hooks/useAdvanced';
import { useAutoFocus } from './hooks/useAutoFocus';
import { createFormContext } from './hooks/useFormContext';
import { useFormEvents } from './hooks/useFormEvents';
import { useFormValues } from './hooks/useFormValues';
import { basicProps } from './props';

export default defineComponent({
  name: 'BasicForm',
  components: { FormItem, Form, Row, FormAction },
  props: basicProps,
  emits: ['advanced-change', 'reset', 'submit', 'register', 'field-value-change'],
  setup(props, { emit, attrs }) {
    const formModel = reactive<Recordable<any>>({});
    const modalFn = useModalContext();

    const advanceState = reactive<AdvanceState>({
      // 默认是收起状态
      isAdvanced: false,
      hideAdvanceBtn: false,
      isLoad: false,
      actionSpan: 6,
    });

    const defaultValueRef = ref<Recordable<any>>({});
    const fullValueRef = ref<Recordable<any>>({});
    const isInitedDefaultRef = ref(false);
    const propsRef = ref<Partial<FormProps>>({});
    const schemaRef = ref<Nullable<FormSchema[]>>(null);
    const formElRef = ref<Nullable<FormActionType>>(null);

    const { prefixCls } = useDesign('basic-form');

    // 每个表单生成不同name保证id不重复
    const getFormName = computed((): string => {
      return `form-${buildUUID()}`;
    });
    // Get the basic configuration of the form
    const getProps = computed((): FormProps => {
      const newProps: any = unref(propsRef);
      return { ...props, ...newProps } as FormProps;
    });

    const getFormClass = computed(() => {
      return [
        prefixCls,
        {
          [`${prefixCls}--compact`]: unref(getProps).compact,
        },
      ];
    });

    // Get uniform row style and Row configuration for the entire form
    const getRow = computed((): Recordable<any> => {
      const { baseRowStyle = {}, rowProps } = unref(getProps);
      return {
        style: baseRowStyle,
        gutter: 16,
        ...rowProps,
      };
    });

    const getBindValue = computed(() => ({ ...attrs, ...props, ...unref(getProps) }) as Recordable);

    const getSchema = computed((): FormSchema[] => {
      const schemas: FormSchema[] = unref(schemaRef) || (unref(getProps).schemas as any);
      return unref(getProps).showAdvancedButton
        ? (schemas.filter((schema) => schema.component !== 'Divider') as FormSchema[])
        : (schemas as FormSchema[]);
    });

    const { handleToggleAdvanced, fieldsIsAdvancedMap } = useAdvanced({
      advanceState,
      emit,
      getProps,
      getSchema,
      formModel,
      defaultValueRef,
    });

    const { handleFormValues, initDefault } = useFormValues({
      getProps,
      defaultValueRef,
      getSchema,
      formModel,
    });

    useAutoFocus({
      getSchema,
      getProps,
      isInitedDefault: isInitedDefaultRef,
      formElRef: formElRef as Ref<FormActionType>,
    });

    const {
      handleSubmit,
      setFieldsValue,
      clearValidate,
      validate,
      validateFields,
      getFieldsValue,
      updateSchema,
      resetSchema,
      appendSchemaByField,
      removeSchemaByField,
      resetFields,
      scrollToField,
    } = useFormEvents({
      emit,
      getProps,
      formModel,
      getSchema,
      defaultValueRef,
      fullValueRef,
      formElRef: formElRef as Ref<FormActionType>,
      schemaRef: schemaRef as Ref<FormSchema[]>,
      handleFormValues,
      isInitedDefaultRef,
    });

    createFormContext({
      resetAction: resetFields,
      submitAction: handleSubmit,
    });

    watch(
      () => unref(getProps).model,
      () => {
        const { model } = unref(getProps);
        if (!model) return;
        setFieldsValue(model);
      },
      {
        immediate: true,
      },
    );

    watch(
      () => unref(getProps).schemas,
      (schemas) => {
        resetSchema(schemas ?? []);
      },
    );

    watch(
      () => getSchema.value,
      (schema) => {
        nextTick(() => {
          //  Solve the problem of modal adaptive height calculation when the form is placed in the modal
          modalFn?.redoModalHeight?.();
        });
        if (unref(isInitedDefaultRef)) {
          return;
        }
        if (schema?.length) {
          initDefault();
          isInitedDefaultRef.value = true;
        }
      },
    );

    watch(
      () => formModel,
      useDebounceFn(() => {
        unref(getProps).submitOnChange && handleSubmit();
      }, 300),
      { deep: true },
    );

    async function setProps(formProps: Partial<FormProps>): Promise<void> {
      propsRef.value = deepMerge(unref(propsRef) || {}, formProps);
    }

    function setFormModel(key: string, value: any, schema: FormSchema) {
      formModel[key] = value;
      const { validateTrigger } = unref(getBindValue);
      if (isFunction(schema.dynamicRules) || isArray(schema.rules)) {
        return;
      }
      if (!validateTrigger || validateTrigger === 'change') {
        validateFields([key]).catch((_) => {});
      }
      emit('field-value-change', key, value);
    }

    function handleEnterPress(e: KeyboardEvent) {
      const { autoSubmitOnEnter } = unref(getProps);
      if (!autoSubmitOnEnter) return;
      if (e.key === 'Enter' && e.target && e.target instanceof HTMLElement) {
        const target: HTMLElement = e.target as HTMLElement;
        if (target && target.tagName && target.tagName.toUpperCase() == 'INPUT') {
          handleSubmit();
        }
      }
    }

    const formActionType: Partial<FormActionType> = {
      getFieldsValue,
      setFieldsValue,
      resetFields,
      updateSchema,
      resetSchema,
      setProps,
      removeSchemaByField,
      appendSchemaByField,
      clearValidate,
      validateFields,
      validate,
      submit: handleSubmit,
      scrollToField,
    };

    onMounted(() => {
      initDefault();
      emit('register', formActionType);
    });

    return {
      getBindValue,
      handleToggleAdvanced,
      handleEnterPress,
      formModel,
      defaultValueRef,
      advanceState,
      getRow,
      getProps,
      formElRef,
      getSchema,
      formActionType: formActionType as any,
      setFormModel,
      getFormClass,
      getFormActionBindProps: computed((): Recordable<any> => ({ ...getProps.value, ...advanceState })),
      fieldsIsAdvancedMap,
      ...formActionType,
      getFormName,
    };
  },
});
</script>
<template>
  <Form
    v-bind="getBindValue"
    :class="getFormClass"
    ref="formElRef"
    :model="formModel"
    @keypress.enter="handleEnterPress"
    :name="getFormName"
  >
    <Row v-bind="getRow">
      <slot name="formHeader"></slot>
      <template v-for="schema in getSchema" :key="schema.field">
        <FormItem
          :is-advanced="fieldsIsAdvancedMap[schema.field]"
          :table-action="tableAction"
          :form-action-type="formActionType"
          :schema="schema"
          :form-props="getProps"
          :all-default-values="defaultValueRef"
          :form-model="formModel"
          :set-form-model="setFormModel"
        >
          <template #[item]="data" v-for="item in Object.keys($slots)">
            <slot :name="item" v-bind="data || {}"></slot>
          </template>
        </FormItem>
      </template>

      <FormAction v-bind="getFormActionBindProps" @toggle-advanced="handleToggleAdvanced">
        <template #[item]="data" v-for="item in ['resetBefore', 'submitBefore', 'advanceBefore', 'advanceAfter']">
          <slot :name="item" v-bind="data || {}"></slot>
        </template>
      </FormAction>
      <slot name="formFooter"></slot>
    </Row>
  </Form>
</template>
<style lang="less">
@import '#/style/index.less';
@prefix-cls: ~'@{namespace}-basic-form';

.@{prefix-cls} {
  overflow-x: hidden;
  .ant-form-item {
    // &-label label::after {
    //   margin: 0 6px 0 2px;
    // }

    &-with-help {
      margin-bottom: 0;
      .ant-form-item-explain {
        font-size: 14px;
        line-height: 20px;
        min-height: 20px !important;
      }
    }

    &:not(.ant-form-item-with-help) {
      margin-bottom: 20px;
    }

    &.suffix-item {
      .ant-form-item-children {
        display: flex;
      }

      .ant-form-item-control {
        margin-top: 4px;
      }

      .suffix {
        display: inline-flex;
        padding-left: 6px;
        margin-top: 1px;
        line-height: 1;
        align-items: center;
      }
    }
  }

  .ant-form-item-explain {
    height: 0;
  }
  .ant-form-item-extra {
    font-size: 14px;
    line-height: 20px;
    min-height: 20px !important;
  }

  &--compact {
    .ant-form-item {
      margin-bottom: 10px !important;
    }
  }
  &.search-form {
    .ant-form-item {
      display: flex;
      .ant-form-item-row {
        display: flex;
        flex: 1;
      }
      .ant-form-item-label {
        width: auto !important;
      }
    }
  }
}
</style>
