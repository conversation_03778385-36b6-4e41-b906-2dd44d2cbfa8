<script lang="ts" setup>
import type { PropType } from 'vue';

import type { Recordable } from '@vben/types';

import type { DropMenu } from './typing';

import { computed } from 'vue';

import { $t as t } from '@vben/locales';

import { Dropdown, Menu, Popconfirm } from 'ant-design-vue';
import { omit } from 'lodash-es';

import { useMessage } from '#/hooks/web/useMessage';
import { isFunction } from '#/utils/is';

const props = defineProps({
  popconfirm: Boolean,
  /**
   * the trigger mode which executes the drop-down action
   * @default ['hover']
   * @type string[]
   */
  trigger: {
    type: Array as PropType<('click' | 'contextmenu' | 'hover')[]>,
    default: () => {
      return ['contextmenu'];
    },
  },
  dropMenuList: {
    type: Array as PropType<(DropMenu & Recordable<any>)[]>,
    default: () => [],
  },
  selectedKeys: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});
const emit = defineEmits(['menuEvent']);
const ADropdown = Dropdown;
const AMenu = Menu;
const AMenuItem = Menu.Item;
const AMenuDivider = Menu.Divider;
const APopconfirm = Popconfirm;
const { createConfirm } = useMessage();

function handleClickMenu(item) {
  const { event } = item;
  const menu = props.dropMenuList.find((item) => `${item.event}` === `${event}`);
  emit('menuEvent', menu);
  if (item.modelConfirm) {
    createConfirm({
      iconType: item.modelConfirm?.iconType || 'warning',
      title: item.modelConfirm?.title || t('common.tipTitle'),
      content: item.modelConfirm?.content || t('common.delTip'),
      onOk: item.modelConfirm?.onOk,
    });
  } else {
    !props.popconfirm && item.onClick?.();
  }
}

const getPopConfirmAttrs = computed(() => {
  return (attrs) => {
    const originAttrs = omit(attrs, ['confirm', 'cancel', 'icon']);
    if (!attrs.onConfirm && attrs.confirm && isFunction(attrs.confirm)) originAttrs.onConfirm = attrs.confirm;
    if (!attrs.onCancel && attrs.cancel && isFunction(attrs.cancel)) originAttrs.onCancel = attrs.cancel;
    return originAttrs;
  };
});

const getAttr = (key: number | string) => ({ key });
</script>

<template>
  <ADropdown :trigger="trigger" v-bind="$attrs">
    <span>
      <slot></slot>
    </span>
    <template #overlay>
      <AMenu :selected-keys="selectedKeys">
        <template v-for="item in dropMenuList" :key="`${item.event}`">
          <AMenuItem v-bind="getAttr(item.event)" @click="handleClickMenu(item)" :disabled="item.disabled">
            <APopconfirm v-if="popconfirm && item.popConfirm" v-bind="getPopConfirmAttrs(item.popConfirm)">
              <template #icon v-if="item.popConfirm.icon">
                <i :class="item.popConfirm.icon"></i>
              </template>
              <div>
                <i :class="item.icon" v-if="item.icon"></i>
                <span class="ml-1">{{ item.text }}</span>
              </div>
            </APopconfirm>
            <template v-else>
              <i :class="item.icon" v-if="item.icon"></i>
              <span class="ml-1">{{ item.text }}</span>
            </template>
          </AMenuItem>
          <AMenuDivider v-if="item.divider" :key="`d-${item.event}`" />
        </template>
      </AMenu>
    </template>
  </ADropdown>
</template>
