<script lang="ts">
import type { PropType } from 'vue';

import type { BasicColumn } from '../types/table';

import { computed, defineComponent } from 'vue';

import BasicHelp from '#/components/Basic/src/BasicHelp.vue';
import { useDesign } from '#/hooks/web/useDesign';

import EditTableHeaderCell from './EditTableHeaderIcon.vue';

export default defineComponent({
  name: 'TableHeaderCell',
  components: {
    EditTableHeaderCell,
    BasicHelp,
  },
  props: {
    column: {
      type: Object as PropType<BasicColumn>,
      default: () => ({}),
    },
  },
  setup(props) {
    const { prefixCls } = useDesign('basic-table-header-cell');

    const getIsEdit = computed(() => !!props.column?.edit);
    const getTitle = computed(() => {
      const title = props.column?.customTitle || props.column?.title;
      if (typeof title !== 'string') return '';
      return title;
    });
    const getHelpMessage = computed(() => props.column?.helpMessage);

    return { prefixCls, getIsEdit, getTitle, getHelpMessage };
  },
});
</script>
<template>
  <EditTableHeaderCell v-if="getIsEdit">
    {{ getTitle }}
  </EditTableHeaderCell>
  <span v-else>{{ getTitle }}</span>
  <BasicHelp v-if="getHelpMessage" :text="getHelpMessage" :class="`${prefixCls}__help`" />
</template>
<style lang="less">
@import '#/style/index.less';
@prefix-cls: ~'@{namespace}-basic-table-header-cell';

.@{prefix-cls} {
  &__help {
    margin-left: 8px;
    color: rgb(0 0 0 / 65%) !important;
  }
}
</style>
