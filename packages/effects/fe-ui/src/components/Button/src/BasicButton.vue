<script lang="ts" setup>
import { computed, unref } from 'vue';

import { Button } from 'ant-design-vue';

import { useAttrs } from '#/hooks/core/useAttrs';

import { buttonProps } from './props';

defineOptions({
  name: 'AButton',
  extends: Button,
  inheritAttrs: false,
});

const props = defineProps(buttonProps);

// get component class
const attrs = useAttrs({ excludeDefaultKeys: false });
const getButtonClass = computed(() => {
  const { color, disabled, type } = props;
  return [
    {
      [`ant-btn-${color}`]: !!color,
      [`ant-btn-${type}`]: type && ['error', 'warning'].includes(type),
      [`is-disabled`]: disabled,
    },
  ];
});

// get inherit binding value
const getBindValue = computed(() => ({
  ...unref(attrs),
  ...props,
  type: !props.type || ['error', 'warning'].includes(props.type) ? 'default' : props.type,
}));
</script>
<template>
  <Button v-bind="getBindValue" :class="getButtonClass" @click="onClick">
    <template #icon v-if="$slots.icon || preIcon">
      <slot name="icon">
        <i class="button-preIcon" :class="[preIcon]"></i>
      </slot>
    </template>
    <template #default="data">
      <slot v-bind="data || {}"></slot>
      <i class="button-postIcon" :class="[postIcon]" v-if="postIcon"></i>
    </template>
  </Button>
</template>
<style lang="less" scoped>
.ant-btn {
  .button-preIcon,
  .button-postIcon,
  i {
    font-size: 14px;
  }
  :deep(.button-preIcon + span),
  :deep(i + span) {
    margin-left: 5px;
  }
  .button-postIcon {
    margin-left: 5px;
  }
}
</style>
