<script lang="tsx">
import type { CSSProperties, PropType } from 'vue';

import { computed, defineComponent, unref } from 'vue';

import { getPopupContainer, isString } from '@vben/utils';

import { QuestionCircleFilled } from '@ant-design/icons-vue';
import { Tooltip } from 'ant-design-vue';

import { useDesign } from '#/hooks/web/useDesign';
import { getSlot } from '#/utils/tsxHelper';

const props = {
  /**
   * Help text max-width
   * @default: 600px
   */
  maxWidth: { type: String, default: '600px' },
  /**
   * Whether to display the serial number
   * @default: false
   */
  showIndex: { type: Boolean },
  /**
   * Help text font color
   * @default: #ffffff
   */
  color: { type: String, default: '#ffffff' },
  /**
   * Help text font size
   * @default: 14px
   */
  fontSize: { type: String, default: '14px' },
  /**
   * Help text list
   */
  placement: { type: String, default: 'top' },
  /**
   * Help text list
   */
  text: { type: [Array, String] as PropType<string | string[]> },
};

export default defineComponent({
  name: 'BasicHelp',
  components: { Tooltip },
  props,
  setup(props, { slots }) {
    const { prefixCls } = useDesign('basic-help');
    const getTooltipStyle = computed((): CSSProperties => ({ color: props.color, fontSize: props.fontSize }));

    const getOverlayStyle = computed((): CSSProperties => ({ maxWidth: props.maxWidth }));

    function renderTitle() {
      const textList = props.text;

      if (isString(textList)) {
        return <p>{textList}</p>;
      }

      if (Array.isArray(textList)) {
        return textList.map((text, index) => {
          return (
            <p key={text}>
              <>
                {props.showIndex ? `${index + 1}. ` : ''}
                {text}
              </>
            </p>
          );
        });
      }
      return null;
    }

    return () => {
      return (
        <Tooltip
          autoAdjustOverflow={true}
          getPopupContainer={() => getPopupContainer()}
          overlayClassName={`${prefixCls}__wrap`}
          overlayStyle={unref(getOverlayStyle)}
          placement={props.placement as 'right'}
          title={<div style={unref(getTooltipStyle)}>{renderTitle()}</div>}
        >
          <span class={prefixCls}>{getSlot(slots) || <QuestionCircleFilled />}</span>
        </Tooltip>
      );
    };
  },
});
</script>
<style lang="less">
@import '#/style/index.less';
@prefix-cls: ~'@{namespace}-basic-help';

.@{prefix-cls} {
  display: inline-block;
  margin-left: 4px;
  font-size: 14px;
  color: @text-color-secondary;
  cursor: pointer;

  &:hover {
    color: @primary-color;
  }

  &__wrap {
    p {
      margin-bottom: 0;
    }
  }
}
</style>
