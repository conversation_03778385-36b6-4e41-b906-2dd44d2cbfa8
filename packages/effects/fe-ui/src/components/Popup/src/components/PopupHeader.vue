<script lang="ts">
import { defineComponent } from 'vue';

import { BasicTitle } from '@vben/fe-ui';

import { ArrowLeftOutlined } from '@ant-design/icons-vue';
import { Button } from 'ant-design-vue';

import { useDesign } from '#/hooks/web/useDesign';

import { headerProps } from '../props';

export default defineComponent({
  name: 'BasicPopupHeader',
  components: { Button, BasicTitle, ArrowLeftOutlined },
  inheritAttrs: false,
  props: {
    ...headerProps,
  },
  emits: ['close', 'ok', 'continue'],
  setup(_, { emit }) {
    const { prefixCls } = useDesign('basic-popup-header');

    function handleOk() {
      emit('ok');
    }
    function handleContinue(e: Event) {
      emit('continue', e);
    }
    function handleClose() {
      emit('close');
    }

    return { prefixCls, handleOk, handleContinue, handleClose };
  },
});
</script>
<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}-left`">
      <span @click="handleClose" v-if="showBackIcon">
        <ArrowLeftOutlined :class="`${prefixCls}__back`" />
      </span>
      <slot name="title" v-if="$slots.title"></slot>
      <BasicTitle :help-message="helpMessage" v-if="!$slots.title">
        {{ title }}
      </BasicTitle>
    </div>
    <div :class="`${prefixCls}__toolbar`">
      <slot name="insertToolbar"></slot>
      <Button
        :type="continueType"
        @click="handleContinue"
        :loading="continueLoading"
        :disabled="confirmLoading"
        class="ml-2"
        v-bind="continueButtonProps"
        v-if="showContinueBtn"
      >
        {{ continueText }}
      </Button>
      <Button
        :type="okType"
        @click="handleOk"
        v-bind="okButtonProps"
        class="ml-2"
        :loading="confirmLoading"
        :disabled="okButtonProps?.disabled || continueLoading"
        v-if="showOkBtn"
      >
        {{ okText }}
      </Button>
      <slot name="centerToolbar"></slot>
      <Button v-bind="cancelButtonProps" @click="handleClose" class="ml-2" v-if="showCancelBtn">
        {{ cancelText }}
      </Button>
      <slot name="appendToolbar"></slot>
    </div>
  </div>
</template>

<style lang="less">
@import '#/style/index.less';
@prefix-cls: ~'@{namespace}-basic-popup-header';
@footer-height: 60px;
.@{prefix-cls} {
  flex-shrink: 0;
  display: flex;
  height: 60px;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
  border-bottom: 1px solid @border-color-base;
  box-sizing: border-box;
  &-left {
    display: flex;
    align-items: center;
  }

  &__back {
    padding-right: 16px;
    cursor: pointer;

    &:hover {
      color: @primary-color;
    }
  }
  &__toolbar {
    display: flex;
    align-items: center;
  }
}
</style>
