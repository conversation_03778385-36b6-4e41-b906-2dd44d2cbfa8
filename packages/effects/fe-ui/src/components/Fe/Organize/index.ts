import { withInstall } from '#/utils';

// import OrganizeSelect from './src/OrganizeSelect.vue';
import OrganizeSelectAsync from './src/OrganizeSelectAsync.vue';
// import DepSelect from './src/DepSelect.vue';
// import DepSelectAsync from './src/DepSelectAsync.vue';
// import PosSelect from './src/PosSelect.vue';
// import GroupSelect from './src/GroupSelect.vue';
// import RoleSelect from './src/RoleSelect.vue';
import UserSelect from './src/UserSelect.vue';
// import UsersSelect from './src/UsersSelect.vue';

// const isAsync = true;

export const FeOrganizeSelect = withInstall(OrganizeSelectAsync);
// export const FeOrganizeSelect = withInstall(isAsync ? OrganizeSelectAsync : OrganizeSelect);
// export const FeDepSelect = withInstall(isAsync ? DepSelectAsync : DepSelect);
// export const FePosSelect = withInstall(PosSelect);
// export const FeGroupSelect = withInstall(GroupSelect);
// export const FeRoleSelect = withInstall(RoleSelect);
export const FeUserSelect = withInstall(UserSelect);
// export const FeUsersSelect = withInstall(UsersSelect);
