<script lang="ts" setup>
import type { Nullable } from '@vben/types';

import type { ScrollActionType } from '#/components/Container';
import type { TreeActionType } from '#/components/Tree';

import { computed, nextTick, reactive, ref, unref, watch } from 'vue';

import { DeleteOutlined } from '@ant-design/icons-vue';
import { Modal as AModal, Button, Form, InputSearch, Select, Tag } from 'ant-design-vue';
import { cloneDeep, pick } from 'lodash-es';

import { ScrollContainer } from '#/components/Container';
import FeEmpty from '#/components/Fe/Empty/src/Empty.vue';
import ModalClose from '#/components/Modal/src/components/ModalClose.vue';
import { BasicTree } from '#/components/Tree';
import { useAttrs } from '#/hooks/core/useAttrs';

import { organizeSelectProps } from './props';

defineOptions({ name: 'FeOrganizeSelect', inheritAttrs: false });
const props = defineProps(organizeSelectProps);
const emit = defineEmits(['update:value', 'change']);
const { getOrgListApi, getOrgListPyIdsApi } = props.api;
const attrs: any = useAttrs({ excludeDefaultKeys: false });
const visible = ref(false);
const treeRef = ref<Nullable<TreeActionType>>(null);
const innerValue = ref<any[] | string | undefined>([]);
const nodeId = ref('-1');
const treeKey = ref(Date.now());
const pagination = reactive({
  keyword: '',
  currentPage: 1,
  pageSize: 20,
  currOrgId: '',
});
const finish = ref<boolean>(false);
const isAsync = ref<boolean>(false);
const activeKey = ref('');
const infiniteBody = ref<Nullable<ScrollActionType>>(null);
const treeData = ref<any[]>([]);
const options = ref<any[]>([]);
const loading = ref(false);
const selectedIds = ref<any[]>([]);
const selectedData = ref<any[]>([]);
const formItemContext = Form.useInjectFormItemContext();
const systemFieldList = ref<any[]>([
  { id: '@currentOrg', fullName: '当前组织', organIds: ['@currentOrg'], organ: '当前组织' },
  {
    id: '@currentOrgAndSubOrg',
    fullName: '当前组织及子组织',
    organIds: ['@currentOrgAndSubOrg'],
    organ: '当前组织及子组织',
  },
  { id: '@currentGradeOrg', fullName: '当前分管组织', organIds: ['@currentGradeOrg'], organ: '当前分管组织' },
]);
const topItem = {
  id: '-1',
  fullName: '顶级节点',
  organ: '顶级节点',
  organIds: ['-1'],
  hasChildren: true,
  icon: 'icon-ym icon-ym-tree-organization3',
};

const getSelectBindValue = computed(() => ({
  ...pick(props, ['placeholder', 'disabled', 'size', 'allowClear']),
  fieldNames: { label: 'name', value: 'id' },
  open: false,
  mode: props.multiple ? 'multiple' : '',
  showSearch: false,
  showArrow: true,
  class: unref(attrs).class ? `w-full ${unref(attrs).class}` : 'w-full',
}));
const getIsLazy = computed(
  () =>
    props.selectType === 'all' && (!props.auth || (props.auth && props.parentId !== '-1')) && activeKey.value === '1',
);
const getTreeBindValue = computed(() => {
  const data: any = {
    treeData: treeData.value,
    onSelect: handleSelect,
    defaultExpandAll: !unref(getIsLazy),
    loading: loading.value,
    key: treeKey.value,
  };
  if (unref(getIsLazy)) data.loadData = onLoadData;
  return data;
});

watch(
  () => props.value,
  () => {
    setValue();
  },
  { immediate: true },
);
watch(
  () => activeKey.value,
  (val) => {
    if (!val) return;
    isAsync.value = false;
    pagination.keyword = '';
    initData();
  },
);
watch(
  () => visible.value,
  (val) => {
    if (!val) activeKey.value = '';
  },
);

function setValue() {
  if (!props.value || props.value.length === 0) return setNullValue();
  const ids = props.multiple ? (props.value as any[]) : [props.value];
  // if (!Array.isArray(ids[0])) return;
  const lastIds = ids;
  // const selectSysData: any[] = getSelectSysData(lastIds);
  // lastIds = lastIds.filter((o) => !o.includes('@') && o !== '-1');
  // if (lastIds.length === 0) return setOptions(selectSysData);
  getOrgListPyIdsApi(lastIds).then((res) => {
    if (!props.value || props.value.length === 0) return setNullValue();
    // setOptions([...(res || []), ...selectSysData]);
    setOptions([...(res || [])]);
  });
}
function setOptions(data) {
  if (!props.value || props.value.length === 0) return setNullValue();
  const selectedList: any[] = data;
  selectedIds.value = selectedList.map((o) => o.id);
  const innerIds = selectedList.map((o) => o.id);
  innerValue.value = props.multiple ? innerIds : innerIds[0];
  options.value = cloneDeep(selectedList);
  selectedData.value = cloneDeep(selectedList);
}
function setNullValue() {
  innerValue.value = props.multiple ? [] : undefined;
  options.value = [];
  selectedIds.value = [];
  selectedData.value = [];
}
// function getSelectSysData(ids) {
//   const list: any[] = [];
//   const sysFieldList = [...unref(systemFieldList), topItem];
//   for (const element of sysFieldList) {
//     inner: for (const id of ids) {
//       if (element.id === id) {
//         list.push({ ...element });
//         break inner;
//       }
//     }
//   }
//   return list;
// }
function onChange(_val, option) {
  if (option) {
    selectedData.value = option;
    selectedIds.value = option.map((o) => o.id);
  } else {
    selectedData.value = [];
    selectedIds.value = [];
  }
  handleSubmit();
}
function onTagClose(i) {
  selectedIds.value.splice(i, 1);
  selectedData.value.splice(i, 1);
  handleSubmit();
}
function openSelectModal() {
  if (props.disabled) return;
  visible.value = true;
  activeKey.value = '1';
  setValue();
  nextTick(() => {
    // 不分页 不需要了
    // bindScroll();
  });
}
function handleCancel() {
  visible.value = false;
}
function handleSearch(value) {
  if (loading.value) return;
  pagination.keyword = value || '';
  if (!unref(getIsLazy)) return getTree().setSearchValue(value);
  isAsync.value = !!pagination.keyword;
  if (isAsync.value) {
    nextTick(() => {
      // 不分页 不需要了
      // bindScroll();
    });
  }
  initData();
}
function handleSelect(keys) {
  if (keys.length === 0) return;
  const data = getTree().getSelectedNode(keys[0]);
  if (data?.disabled) return;
  handleNodeClick(data);
}
function handleNodeClick(data) {
  const currId = data?.id;
  if (props.multiple) {
    // const boo = selectedIds.value.some((o) => o.join(',') === currId.join(','));
    // if (boo) return;
    selectedIds.value.push(currId);
    selectedData.value.push(data);
  } else {
    selectedIds.value = [currId];
    selectedData.value = [data];
  }
}
function removeAll() {
  selectedIds.value = [];
  selectedData.value = [];
}
function removeData(index: number) {
  selectedIds.value.splice(index, 1);
  selectedData.value.splice(index, 1);
}
function getTree() {
  const tree = unref(treeRef);
  if (!tree) {
    throw new Error('tree is null!');
  }
  return tree;
}
function handleSubmit() {
  const ids = unref(selectedData).map((o) => o.id);
  options.value = unref(selectedData);
  innerValue.value = props.multiple ? ids : ids[0];
  if (props.multiple) {
    emit('update:value', unref(selectedIds));
    emit('change', unref(selectedIds), unref(options));
  } else {
    emit('update:value', unref(selectedIds)[0] || '');
    emit('change', unref(selectedIds)[0] || '', unref(options)[0]);
  }
  formItemContext.onFieldChange();
  handleCancel();
}
// function bindScroll() {
//   const bodyRef = infiniteBody.value;
//   const vBody = bodyRef?.getScrollWrap();
//   vBody?.addEventListener('scroll', () => {
//     if (vBody.scrollHeight - vBody.clientHeight - vBody.scrollTop <= 200 && !loading.value && !finish.value) {
//       pagination.currentPage += 1;
//       getList();
//     }
//   });
// }
function onLoadData(node) {
  nodeId.value = node.id;
  return new Promise((resolve: (value?: unknown) => void) => {
    // const method = props.auth
    //   ? (props.isOnlyOrg
    //     ? getOrganizeSelectorAsyncByAuth
    //     : getDepartmentSelectorAsyncByAuth)
    //   : getOrgListApi;
    const method = getOrgListApi;
    const type = props.isOnlyOrg ? 'COMPANY' : '';
    method({ parentId: nodeId.value, type, ...pagination }).then((res) => {
      res.forEach((item) => {
        item.isLeaf = Boolean(item.isLeaf);
        item.icon = item.type === 'COMPANY' ? 'clarity:organization-line' : 'clarity:group-line';
      });
      const list = res;
      getTree().updateNodeByKey(node.eventKey, { children: list, isLeaf: list.length === 0 });
      resolve();
    });
  });
}
function getList() {
  loading.value = true;
  if (pagination.keyword) nodeId.value = '';
  // const method = props.auth
  //   ? (props.isOnlyOrg
  //     ? getOrganizeSelectorAsyncByAuth
  //     : getDepartmentSelectorAsyncByAuth)
  //   : getOrgListApi;
  const method = getOrgListApi;
  const type = props.isOnlyOrg ? 'COMPANY' : '';
  method({ parentId: nodeId.value, type, name: pagination.keyword })
    .then((res) => {
      res.forEach((item) => {
        item.isLeaf = Boolean(item.isLeaf);
        item.icon = item.type === 'COMPANY' ? 'clarity:organization-line' : 'clarity:group-line';
      });
      if (pagination.keyword) {
        // if (res.length < pagination.pageSize) finish.value = true;
        treeData.value = [...treeData.value, ...res];
      } else {
        treeData.value = res;
        if (treeData.value.length > 0 && nodeId.value === '-1') {
          getTree().setExpandedKeys([treeData.value[0].id]);
        }
      }
      loading.value = false;
    })
    .catch((error) => {
      console.error(error);
      loading.value = false;
    });
}
async function initData() {
  treeKey.value = Date.now();
  nodeId.value = '-1';
  finish.value = false;
  treeData.value = [];
  pagination.currentPage = 1;
  pagination.currOrgId = props.currOrgId || '';
  if (props.selectType === 'all') {
    if (props.auth) {
      if (props.isOnlyOrg && props.parentId === '-1') return (treeData.value = [topItem]);
      nodeId.value = '-1';
      getList();
    } else {
      if (activeKey.value === '1') {
        getList();
      } else {
        treeData.value = cloneDeep(systemFieldList.value);
      }
    }
  } else {
    // if (!props.ableIds?.length) return (treeData.value = []);
    // loading.value = true;
    // const departIds = props.ableIds ? props.ableIds.map((o) => o[o.length - 1]) : [];
    // const query = { departIds };
    // getOrgByOrganizeCondition(query).then((res) => {
    //   treeData.value = res.data.list;
    //   loading.value = false;
    // });
  }
}
</script>

<template>
  <div class="select-tag-list" :class="[$attrs.class]" v-if="buttonType === 'button'">
    <Button pre-icon="icon-ym icon-ym-btn-add" @click="openSelectModal">{{ modalTitle }}</Button>
    <div class="tags">
      <Tag class="!mt-10px" :closable="!disabled" v-for="(item, i) in options" :key="item.id" @close="onTagClose(i)">
        {{ item.organ }}
      </Tag>
    </div>
  </div>
  <Select
    v-bind="getSelectBindValue"
    v-model:value="innerValue"
    :options="options"
    @change="onChange"
    @click="openSelectModal"
    v-else
  />
  <AModal
    v-model:open="visible"
    :title="modalTitle"
    :width="800"
    class="transfer-modal"
    @ok="handleSubmit"
    centered
    :mask-closable="false"
    :keyboard="false"
  >
    <template #closeIcon>
      <ModalClose :can-fullscreen="false" @cancel="handleCancel" />
    </template>
    <div class="transfer__body">
      <div class="transfer-pane left-pane">
        <div class="transfer-pane__tool">
          <InputSearch
            placeholder="请输入组织名称"
            allow-clear
            v-model:value="pagination.keyword"
            @search="handleSearch"
          />
        </div>
        <div class="transfer-pane__body transfer-pane__body-tab">
          <!--<Tabs v-model:active-key="activeKey" :tab-bar-gutter="10" size="small" class="pane-tabs" v-if="hasSys">-->
          <!--  <TabPane key="1" tab="全部数据" />-->
          <!--  <TabPane key="system" tab="动态参数" />-->
          <!--</Tabs>-->
          <BasicTree class="tree-main" ref="treeRef" v-bind="getTreeBindValue" v-if="!isAsync" />
          <ScrollContainer v-loading="loading && pagination.currentPage === 1" v-else ref="infiniteBody">
            <div v-for="item in treeData" :key="item.id" class="selected-item" @click="handleNodeClick(item)">
              <span :title="item.organ">{{ item.fullName }}</span>
            </div>
            <FeEmpty v-if="treeData.length === 0" />
          </ScrollContainer>
        </div>
      </div>
      <div class="transfer-pane right-pane">
        <div class="transfer-pane__tool">
          <span>已选</span>
          <span class="remove-all-btn" @click="removeAll">清空列表</span>
        </div>
        <div class="transfer-pane__body">
          <ScrollContainer>
            <div v-for="(item, i) in selectedData" :key="i" class="selected-item">
              <span :title="item.organ">{{ item.fullName }}</span>
              <DeleteOutlined class="delete-btn" @click="removeData(i)" />
            </div>
            <FeEmpty v-if="selectedData.length === 0" />
          </ScrollContainer>
        </div>
      </div>
    </div>
  </AModal>
</template>
