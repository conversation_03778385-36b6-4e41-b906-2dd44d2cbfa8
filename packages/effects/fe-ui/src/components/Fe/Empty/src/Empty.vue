<script lang="ts" setup>
import { computed, ref, unref } from 'vue';

import { Empty } from 'ant-design-vue';

import { useAttrs } from '#/hooks/core/useAttrs';

defineOptions({ name: 'FeEmpty', inheritAttrs: false });

const props = defineProps({
  image: { type: [String, Boolean, Object], default: null },
});

const attrs = useAttrs({ excludeDefaultKeys: false });
const defaultImage = ref(Empty.PRESENTED_IMAGE_SIMPLE);

const getBindValue: any = computed(() => ({
  ...unref(attrs),
  image: props.image || props.image === false ? props.image : unref(defaultImage),
}));
</script>

<template>
  <Empty v-bind="getBindValue">
    <template #[item]="data" v-for="item in Object.keys($slots)">
      <slot :name="item" v-bind="data || {}"></slot>
    </template>
  </Empty>
</template>
