<script lang="ts" setup>
import type { CheckboxChangeEvent } from 'ant-design-vue/es/checkbox/interface';

import { computed, ref, unref, watch } from 'vue';

import { Checkbox } from 'ant-design-vue';
import { omit } from 'lodash-es';

import { useAttrs } from '#/hooks/core/useAttrs';

defineOptions({ name: 'FeCheckboxSingle', inheritAttrs: false });
const props = defineProps({
  checkedValue: { type: [Number, Boolean, String], default: 1 },
  unCheckedValue: { type: [Number, Boolean, String], default: 0 },
  value: { type: [Number, Boolean, String], required: true },
  label: { type: String, default: '' },
});
const emit = defineEmits(['update:value', 'change']);
const attrs = useAttrs({ excludeDefaultKeys: false });
const innerValue = ref(false);

const getBindValue = computed(() => ({ ...unref(attrs), ...omit(props, ['value']) }));

watch(
  () => props.value,
  (val) => {
    setValue(val);
  },
  { immediate: true },
);

function setValue(value: boolean | number | string) {
  innerValue.value = value === props.checkedValue;
}
function onChange(e: CheckboxChangeEvent) {
  const value = e.target.checked ? props.checkedValue : props.unCheckedValue;
  emit('update:value', value);
  emit('change', value);
}
</script>

<template>
  <Checkbox v-model:checked="innerValue" v-bind="getBindValue" @change="onChange">{{ label }}</Checkbox>
</template>
