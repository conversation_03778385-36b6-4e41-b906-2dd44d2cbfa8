import type { PropType } from 'vue';

export interface FieldNames {
  label?: string;
  value?: string;
  disabled?: string;
}

export const checkboxProps = {
  value: {
    type: Array as PropType<boolean[] | number[] | string[]>,
  },
  options: {
    type: Array,
    default: () => [],
  },
  fieldNames: {
    type: Object as PropType<FieldNames>,
    default: () => ({ value: 'id', label: 'fullName', disabled: 'disabled' }),
  },
  direction: {
    type: String,
    default: 'horizontal',
  },
};
