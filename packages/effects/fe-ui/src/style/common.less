@import '#/style/index.less';
.ant-modal.transfer-modal {
  &.member-modal {
    .ant-modal-body > .scrollbar {
      padding: 10px;
      .transfer__body .transfer-pane {
        width: 100%;
      }
    }
  }
  .ant-modal-body {
    padding: 20px 10px;
    & > .scrollbar {
      padding: 20px 10px;
    }
    .scrollbar .scrollbar__wrap {
      margin-bottom: 0 !important;
    }
    .transfer__body {
      line-height: 32px;
      display: flex;
      justify-content: space-around;
      height: 400px;
    }
    .transfer-pane {
      width: 360px;
      &.left-pane {
        .selected-item {
          cursor: pointer;
          justify-content: flex-start;
        }
        .selected-item-user {
          cursor: pointer;
        }
      }
      .transfer-pane__tool {
        margin-bottom: 8px;
        height: 32px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .remove-all-btn {
          cursor: pointer;
          color: @error-color;
        }
      }
      .transfer-pane__body {
        position: relative;
        width: 100%;
        height: calc(100% - 40px);
        overflow: auto;
        overflow-x: hidden;
        font-size: 14px;
        border: 1px solid @border-color-base;
        border-radius: var(--border-radius);
        &.transfer-pane__body-tab {
          overflow: hidden;
          display: flex;
          flex-direction: column;
          .ant-tabs {
            .ant-tabs-nav {
              margin-bottom: 0;
            }
            .ant-tabs-nav-list {
              width: 100%;
            }
            .ant-tabs-tab {
              flex: auto;
              .ant-tabs-tab-btn {
                width: 100%;
                text-align: center;
              }
            }
          }
          .tree-main {
            flex: 1;
            overflow: auto;
          }
          .pane-tabs {
            flex-shrink: 0;
            &.pane-tabs-single {
              .ant-tabs-tab {
                width: 25%;
                flex: none;
              }
            }
            .ant-tabs-nav-operations {
              display: none !important;
            }
          }
        }
      }
      .ant-tree {
        .ant-tree-treenode {
          &.ant-tree-treenode-selected {
            background-color: @selected-hover-bg;
          }
        }
      }
      .custom-title {
        height: 38px;
        padding: 0 12px;
        line-height: 38px;
        font-size: 14px;
        border-bottom: 1px solid @border-color-base;
      }
      .selected-item {
        width: 100%;
        padding: 0px 12px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        &.selected-item-user {
          .selected-item-main {
            border-bottom: 1px solid @border-color-base1;
            display: flex;
            align-items: center;
            height: 50px;
            width: 100%;
            box-sizing: border-box;
          }
          .selected-item-headIcon {
            flex-shrink: 0;
            &.icon {
              width: 36px;
              height: 36px;
              text-align: center;
              i {
                font-size: 22px;
                line-height: 36px;
              }
            }
          }
          .selected-item-text {
            min-width: 0;
            flex: 1;
            margin-left: 10px;
            .name {
              height: 20px;
              line-height: 20px;
              font-size: 14px;
              margin-bottom: 2px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            .organize {
              height: 17px;
              line-height: 17px;
              color: #999999;
              font-size: 12px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
        &:hover {
          background-color: @selected-hover-bg;
        }
        span {
          max-width: 90%;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .delete-btn:hover {
          color: @error-color;
          cursor: pointer;
        }
      }
      .selected-item-user-multiple {
        padding: 0 12px;
        position: relative;
        .selected-item-title {
          font-size: 14px;
          display: flex;
          align-items: center;
          span {
            padding-left: 6px;
          }
        }
        .selected-item-user {
          padding: 0 15px;
          &:last-child {
            border-bottom: 1px solid @border-color-base1;
            .selected-item-main {
              border-bottom: none;
            }
          }
          .selected-item-main {
            box-sizing: content-box;
          }
        }
        .selected-item-icon {
          width: 36px;
          height: 36px;
          background: linear-gradient(193deg, #a7d6ff 0%, #1990fa 100%);
          border-radius: 50%;
          line-height: 36px;
          color: #ffffff;
          font-size: 14px;
          text-align: center;
        }
      }
    }
  }
}
