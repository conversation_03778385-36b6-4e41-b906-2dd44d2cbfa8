export function getDateFormat(format) {
  if (!format) return 'YYYY-MM-DD HH:mm:ss';
  const formatObj = {
    yyyy: 'YYYY',
    'yyyy-MM': 'YYYY-MM',
    'yyyy-MM-dd': 'YYYY-MM-DD',
    'yyyy-MM-dd HH:mm': 'YYYY-MM-DD HH:mm',
    'yyyy-MM-dd HH:mm:ss': 'YYYY-MM-DD HH:mm:ss',
    YYYY: 'YYYY',
    'YYYY-MM': 'YYYY-MM',
    'YYYY-MM-DD': 'YYYY-MM-DD',
    'YYYY-MM-DD HH:mm': 'YYYY-MM-DD HH:mm',
    'YYYY-MM-DD HH:mm:ss': 'YYYY-MM-DD HH:mm:ss',
  };
  return formatObj[format] || 'YYYY-MM-DD HH:mm:ss';
}
export function toDecimal(num: number = 0) {
  const sign = num === (num = Math.abs(num));
  num = Math.floor(num * 100 + 0.500_000_000_01);
  const cents = num % 100;
  let value: string = Math.floor(num / 100).toString();
  const centsStr: string = cents < 10 ? `0${cents}` : cents.toString();
  for (let i = 0; i < Math.floor((value.length - (1 + i)) / 3); i++)
    value = `${value.slice(0, Math.max(0, value.length - (4 * i + 3)))}${value.slice(Math.max(0, value.length - (4 * i + 3)))}`;
  return `${(sign ? '' : '-') + value}.${centsStr}`;
}
export function toFileSize(size) {
  if (size === null || size === '') return '';
  if (size < 1024) return `${toDecimal(size)} 字节`;
  else if (size >= 1024 && size < 1_048_576) return `${toDecimal(size / 1024)} KB`;
  else if (size >= 1_048_576 && size < 1_073_741_824) return `${toDecimal(size / 1024 / 1024)} MB`;
  else if (size >= 1_073_741_824) return `${toDecimal(size / 1024 / 1024 / 1024)} GB`;
}
