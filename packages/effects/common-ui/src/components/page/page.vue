<script setup lang="ts">
import type { StyleValue } from 'vue';

import type { PageProps } from './types';

import { computed, nextTick, onMounted, ref, useTemplateRef } from 'vue';

import { useTabs } from '@vben/hooks';

import { preferences } from '@vben-core/preferences';
import { CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT } from '@vben-core/shared/constants';
import { cn } from '@vben-core/shared/utils';

import { PageHeader } from 'ant-design-vue';

defineOptions({
  name: 'Page',
});

const { autoContentHeight = false, showBack = false } = defineProps<PageProps>();

const headerHeight = ref(0);
const footerHeight = ref(0);
const shouldAutoHeight = ref(false);

const headerRef = useTemplateRef<HTMLDivElement>('headerRef');
const footerRef = useTemplateRef<HTMLDivElement>('footerRef');

const contentStyle = computed<StyleValue>(() => {
  if (autoContentHeight) {
    return {
      height: `calc(var(${CSS_VARIABLE_LAYOUT_CONTENT_HEIGHT}) - ${headerHeight.value}px)`,
      overflowY: shouldAutoHeight.value ? 'auto' : 'unset',
    };
  }
  return {};
});

async function calcContentHeight() {
  if (!autoContentHeight) {
    return;
  }
  await nextTick();
  headerHeight.value = headerRef.value?.offsetHeight || 0;
  footerHeight.value = footerRef.value?.offsetHeight || 0;
  setTimeout(() => {
    shouldAutoHeight.value = true;
  }, 30);
}

const { closeCurrentTab } = useTabs();
const goBack = () => {
  if (preferences.tabbar.enable) {
    closeCurrentTab();
  } else {
    window.history.back();
  }
};
const pageHeaderAttr: { onBack?: () => void } = {};

if (showBack) {
  pageHeaderAttr.onBack = goBack;
}

onMounted(() => {
  calcContentHeight();
});
</script>

<template>
  <div class="relative">
    <div
      v-if="description || $slots.description || title || $slots.title || $slots.extra || $slots.headerFooter"
      ref="headerRef"
      :class="cn(headerClass)"
    >
      <div class="px-4 pt-4">
        <PageHeader :ghost="false" :sub-title="description" :title="title" v-bind="pageHeaderAttr" class="border">
          <template #title>
            <slot name="title"></slot>
          </template>
          <template #subTitle>
            <slot name="description"></slot>
          </template>
          <template #extra>
            <slot name="extra"></slot>
          </template>
          <template v-if="$slots.headerFooter" #footer>
            <slot name="headerFooter"></slot>
          </template>
        </PageHeader>
      </div>
    </div>

    <div
      :class="
        cn(
          'h-full px-4 pb-4',
          description || $slots.description || title || $slots.title || $slots.extra || $slots.headerFooter
            ? ''
            : 'pt-4',
          contentClass,
        )
      "
      :style="contentStyle"
    >
      <slot></slot>
    </div>

    <div
      v-if="$slots.footer"
      ref="footerRef"
      :class="cn('bg-card align-center absolute bottom-0 left-0 right-0 flex px-6 py-4', footerClass)"
    >
      <slot name="footer"></slot>
    </div>
  </div>
</template>
