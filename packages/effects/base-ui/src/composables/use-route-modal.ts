import { onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

interface RouteModalOptions {
  onDetail?: (data: any) => void;
  onEdit?: (data: any) => void;
  onAdd?: () => void;
  getDataById?: (id: string) => any | Promise<any>;
}

export function useRouteModal(options: RouteModalOptions) {
  const route = useRoute();
  const router = useRouter();

  const handleRouteParams = async () => {
    const { action, id } = route.query;

    if (!action) return;

    try {
      if (action === 'add' && options.onAdd) {
        options.onAdd();
      } else if ((action === 'detail' || action === 'edit') && id) {
        let rowData = { id };

        // 如果提供了获取数据的方法，则获取完整数据
        if (options.getDataById) {
          rowData = await options.getDataById(id as string);
        }

        if (action === 'detail' && options.onDetail) {
          options.onDetail(rowData);
        } else if (action === 'edit' && options.onEdit) {
          options.onEdit(rowData);
        }
      }

      // 清除URL参数
      router.replace({ query: {} });
    } catch (error) {
      console.error('处理路由参数失败:', error);
    }
  };

  onMounted(async () => {
    await handleRouteParams();
  });

  watch(() => route.query, handleRouteParams);

  return {
    handleRouteParams,
  };
}
