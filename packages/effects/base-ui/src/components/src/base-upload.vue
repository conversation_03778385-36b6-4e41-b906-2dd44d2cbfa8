<script setup lang="ts">
import type { UploadChangeParam } from 'ant-design-vue';
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';

import { ref } from 'vue';

import { LoadingOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { Upload } from 'ant-design-vue';

const props = defineProps({
  uploadApi: { type: Function, required: true },
});
const emit = defineEmits(['uploadSuccess']);
const filePath = defineModel({ type: String });
const fileList = ref([]);
const loading = ref(false);
const handleUploadChange = (info: UploadChangeParam) => {
  if (info.file.status === 'done') {
    emit('uploadSuccess', info.file.response);
  }
};
const uploadFile = ({
  data = {},
  file,
  filename = 'file',
  headers,
  onError,
  onProgress,
  onSuccess,
  withCredentials,
}: UploadRequestOption) => {
  const formData = { ...data };
  formData[filename] = file;
  // 使用框架上传方法已实现 FormData 不需要重复转换
  // const formData = new FormData();
  // if (data) {
  //   Object.keys(data).forEach((key) => {
  //     formData.append(key, data[key] as string);
  //   });
  // }
  // formData.append(filename, file);
  props
    .uploadApi(formData, {
      withCredentials,
      headers,
      onUploadProgress: ({ total, loaded }: { loaded: number; total: number }) => {
        if (onProgress) {
          onProgress({ percent: Number(Math.round((loaded / total) * 100).toFixed(2)) });
        }
      },
    })
    .then((response: unknown) => {
      if (onSuccess) {
        onSuccess(response);
      }
    })
    .catch(onError);
  return {
    abort() {
      console.warn('upload progress is aborted.');
    },
  };
};
</script>

<template>
  <Upload
    v-model:file-list="fileList"
    :custom-request="uploadFile"
    :show-upload-list="false"
    :max-count="1"
    list-type="picture-card"
    v-bind="$attrs"
    @change="handleUploadChange"
  >
    <img v-if="filePath" :src="filePath" alt="" />
    <div v-else>
      <LoadingOutlined v-if="loading" />
      <PlusOutlined v-else />
      <div class="">上传</div>
    </div>
  </Upload>
</template>

<style scoped></style>
