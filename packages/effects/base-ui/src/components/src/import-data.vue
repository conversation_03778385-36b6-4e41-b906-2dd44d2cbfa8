<script setup lang="ts">
import type { UploadChangeParam } from 'ant-design-vue';
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';

import { reactive } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space, Upload } from 'ant-design-vue';

const props = defineProps({
  title: { type: String, default: '导入' },
  downloadTemplateApi: { type: Function, default: null },
  uploadApi: { type: Function, required: true },
  icon: { type: [String], default: '' },
});
const emit = defineEmits(['importSuccess']);
const loading = reactive({
  download: false,
});
const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  showConfirmButton: false,
  cancelText: '完成',
});
const openDialog = () => {
  modalApi.open();
};
const downloadTemplate = async () => {
  loading.download = true;
  try {
    await props.downloadTemplateApi();
  } finally {
    loading.download = false;
  }
};
const handleUploadChange = (info: UploadChangeParam) => {
  if (info.file.status === 'done') {
    modalApi.close();
    message.success('导入成功');
    emit('importSuccess', info.file.response);
  }
};
const uploadFile = ({
  data = {},
  file,
  filename = 'file',
  headers,
  onError,
  onProgress,
  onSuccess,
  withCredentials,
}: UploadRequestOption) => {
  const formData = { ...data };
  formData[filename] = file;
  // 使用框架上传方法已实现 FormData 不需要重复转换
  // const formData = new FormData();
  // if (data) {
  //   Object.keys(data).forEach((key) => {
  //     formData.append(key, data[key] as string);
  //   });
  // }
  // formData.append(filename, file);
  props
    .uploadApi(formData, {
      withCredentials,
      headers,
      onUploadProgress: ({ total, loaded }: { loaded: number; total: number }) => {
        if (onProgress) {
          onProgress({ percent: Number(Math.round((loaded / total) * 100).toFixed(2)) });
        }
      },
    })
    .then((response: unknown) => {
      if (onSuccess) {
        onSuccess(response);
      }
    })
    .catch(onError);
  return {
    abort() {
      console.warn('upload progress is aborted.');
    },
  };
};
</script>

<template>
  <div>
    <Button type="primary" :icon="icon" @click="openDialog">导入</Button>
    <Modal :title="props.title" centered>
      <Space direction="vertical" size="middle">
        <Button v-if="props.downloadTemplateApi" @click="downloadTemplate">下载模板</Button>
        <Upload :custom-request="uploadFile" :max-count="1" name="file" @change="handleUploadChange">
          <Button type="primary">上传文件</Button>
        </Upload>
      </Space>
    </Modal>
  </div>
</template>

<style></style>
