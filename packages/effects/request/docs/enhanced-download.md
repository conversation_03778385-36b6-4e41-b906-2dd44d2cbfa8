# 增强的文件下载功能

本文档介绍了新增的文件下载功能，支持从 HTTP 响应头的 `Content-Disposition` 中自动解析文件名。

## 功能特性

- ✅ 优先从 `Content-Disposition` 响应头解析文件名
- ✅ 支持 RFC 5987 编码的文件名（`filename*=UTF-8''encoded-name`）
- ✅ 支持引号包围的文件名（`filename="quoted-name"`）
- ✅ 支持无引号的文件名（`filename=unquoted-name`）
- ✅ **支持 URL 编码的中文文件名**（`filename=%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`）
- ✅ 自动回退到备用文件名或 URL 中的文件名
- ✅ 提供便捷的下载并保存方法
- ✅ 向后兼容现有的下载 API

## API 参考

### RequestClient 新增方法

#### `downloadWithFileName(url, options?)`

下载文件并自动解析文件名。

```typescript
const result = await requestClient.downloadWithFileName('https://example.com/file', {
  fallbackFileName: 'backup-name.pdf',
  config: {
    headers: { 'Authorization': 'Bearer token' }
  }
});

console.log(result.fileName); // 从 Content-Disposition 解析的文件名
console.log(result.blob);     // 文件的 Blob 数据
```

#### `downloadAndSave(url, options?)`

下载文件并直接触发浏览器下载。

```typescript
await requestClient.downloadAndSave('https://example.com/file', {
  fallbackFileName: 'backup-name.pdf'
});
```

### 通用工具函数

#### `downloadFileWithAutoFileName(options)`

使用 fetch API 下载文件并解析文件名。

```typescript
import { downloadFileWithAutoFileName } from '@vben/shared';

const result = await downloadFileWithAutoFileName({
  url: 'https://example.com/file',
  fallbackFileName: 'backup-name.pdf',
  headers: { 'Authorization': 'Bearer token' }
});
```

#### `downloadAndSaveWithAutoFileName(options)`

下载文件并直接保存。

```typescript
import { downloadAndSaveWithAutoFileName } from '@vben/shared';

await downloadAndSaveWithAutoFileName({
  url: 'https://example.com/file',
  fallbackFileName: 'backup-name.pdf'
});
```

## 文件名解析优先级

1. **Content-Disposition 响应头**（最高优先级）
   - RFC 5987 编码格式：`filename*=UTF-8''%E6%96%87%E4%BB%B6.pdf`
   - 引号包围格式：`filename="文件名.pdf"`
   - 无引号格式：`filename=filename.pdf`
   - URL 编码格式：`filename=%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx`
   - 引号包围的 URL 编码：`filename="%E6%96%87%E4%BB%B6%E5%90%8D.pdf"`

2. **备用文件名**
   - 通过 `fallbackFileName` 参数提供

3. **URL 中的文件名**
   - 从 URL 路径的最后一部分提取

4. **默认文件名**
   - 使用 `downloaded_file` 作为最后的备选

## 使用示例

### 基础用法

```typescript
// 最简单的用法
await requestClient.downloadAndSave('https://example.com/report.pdf');

// 带备用文件名
await requestClient.downloadAndSave('https://example.com/api/download', {
  fallbackFileName: 'monthly-report.pdf'
});
```

### 获取文件信息

```typescript
const result = await requestClient.downloadWithFileName('https://example.com/file');

console.log('文件名:', result.fileName);
console.log('文件大小:', result.blob.size, 'bytes');
console.log('文件类型:', result.blob.type);
```

### 批量下载

```typescript
const urls = [
  'https://example.com/file1.pdf',
  'https://example.com/file2.docx',
  'https://example.com/file3.xlsx'
];

for (const url of urls) {
  try {
    await requestClient.downloadAndSave(url);
    console.log(`Downloaded: ${url}`);
  } catch (error) {
    console.error(`Failed to download: ${url}`, error);
  }
}
```

### 改进现有的下载功能

```typescript
// 原来的代码
function handleDownload() {
  downloadFileApi(selectedRowKeys.value).then((res) => {
    downloadByUrl({ url: res.url, fileName: res.name });
  });
}

// 改进后的代码
async function handleDownload() {
  try {
    const res = await downloadFileApi(selectedRowKeys.value);
    
    // 使用新的下载功能，优先从响应头解析文件名
    await requestClient.downloadAndSave(res.url, {
      fallbackFileName: res.name // 使用 API 返回的名称作为备用
    });
  } catch (error) {
    console.error('下载失败:', error);
  }
}
```

## Content-Disposition 响应头示例

服务器可能返回以下各种格式的响应头，我们的解析器都能正确处理：

```http
# 标准格式
Content-Disposition: attachment; filename="report.pdf"

# RFC 5987 编码格式
Content-Disposition: attachment; filename*=UTF-8''%E6%8A%A5%E5%91%8A.pdf

# URL 编码格式（常见于中文文件名）
Content-Disposition: attachment;filename=%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx

# 引号包围的 URL 编码格式
Content-Disposition: attachment; filename="%E6%96%87%E4%BB%B6%E5%90%8D.pdf"

# 无引号格式
Content-Disposition: attachment; filename=document.xlsx

# 内联显示格式
Content-Disposition: inline; filename="image.jpg"
```

### 实际测试案例

以下是一些实际的服务器响应示例：

```http
# 中文文件名 - 商品导入模板.xlsx
Content-Disposition: attachment;filename=%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx

# 中文文件名 - 用户数据导出_2024年07月15日.xlsx
Content-Disposition: attachment;filename=%E7%94%A8%E6%88%B7%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%87%BA_2024%E5%B9%B407%E6%9C%8815%E6%97%A5.xlsx

# 包含特殊字符 - 报表(最终版).docx
Content-Disposition: attachment;filename=%E6%8A%A5%E8%A1%A8%28%E6%9C%80%E7%BB%88%E7%89%88%29.docx
```

## 注意事项

1. **浏览器兼容性**: 新功能在所有现代浏览器中都能正常工作
2. **编码处理**:
   - 自动处理 UTF-8 编码的文件名
   - 支持 URL 编码的中文文件名（如 `%E5%95%86%E5%93%81.xlsx`）
   - 支持 RFC 5987 编码格式
3. **错误处理**:
   - 如果文件名解析失败，会自动回退到备用方案
   - 对于损坏的 URL 编码，会返回原始文件名而不是失败
4. **向后兼容**: 原有的 `download()` 方法保持不变，不会影响现有代码

## 迁移指南

如果你想升级现有的下载功能：

1. **保持现有代码不变**（推荐）
   - 现有的 `requestClient.download()` 方法继续工作
   - 逐步在新功能中使用 `downloadAndSave()` 或 `downloadWithFileName()`

2. **逐步迁移**
   ```typescript
   // 从这个
   const blob = await requestClient.download(url);
   
   // 迁移到这个
   const result = await requestClient.downloadWithFileName(url);
   ```

3. **完全替换**
   ```typescript
   // 从这个
   downloadByUrl({ url: fileUrl, fileName: fileName });
   
   // 替换为这个
   await requestClient.downloadAndSave(fileUrl, { fallbackFileName: fileName });
   ```
