/**
 * URL 编码文件名处理演示
 * 
 * 这个文件演示了如何处理服务器返回的 URL 编码文件名，
 * 特别是像 %E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx 这样的情况
 */

import { requestClient } from '../request-client';

/**
 * 演示处理 URL 编码的中文文件名
 */
export async function demoUrlEncodedFilename() {
  console.log('=== URL 编码文件名处理演示 ===\n');

  // 模拟服务器返回的各种 Content-Disposition 格式
  const testCases = [
    {
      name: '商品导入模板',
      contentDisposition: 'attachment;filename=%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
      expectedFileName: '商品导入模板.xlsx'
    },
    {
      name: '用户数据导出',
      contentDisposition: 'attachment; filename="%E7%94%A8%E6%88%B7%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%87%BA_2024.csv"',
      expectedFileName: '用户数据导出_2024.csv'
    },
    {
      name: '报表(最终版)',
      contentDisposition: 'attachment;filename=%E6%8A%A5%E8%A1%A8%28%E6%9C%80%E7%BB%88%E7%89%88%29.docx',
      expectedFileName: '报表(最终版).docx'
    },
    {
      name: 'RFC 5987 格式',
      contentDisposition: "attachment; filename*=UTF-8''%E6%96%87%E4%BB%B6%E5%90%8D.pdf",
      expectedFileName: '文件名.pdf'
    }
  ];

  // 演示解析函数
  function parseFileNameFromContentDisposition(contentDisposition: string): string | null {
    if (!contentDisposition) {
      return null;
    }

    // 匹配 filename*=UTF-8''encoded-filename 格式 (RFC 5987)
    const rfc5987Match = contentDisposition.match(/filename\*=UTF-8''([^;]+)/i);
    if (rfc5987Match) {
      try {
        return decodeURIComponent(rfc5987Match[1]);
      } catch {
        // 如果解码失败，继续尝试其他格式
      }
    }

    // 匹配 filename="quoted-filename" 格式
    const quotedMatch = contentDisposition.match(/filename="([^"]+)"/i);
    if (quotedMatch) {
      const fileName = quotedMatch[1];
      // 尝试解码 URL 编码的文件名
      try {
        return decodeURIComponent(fileName);
      } catch {
        // 如果解码失败，返回原始文件名
        return fileName;
      }
    }

    // 匹配 filename=unquoted-filename 格式（包括 URL 编码的情况）
    const unquotedMatch = contentDisposition.match(/filename=([^;]+)/i);
    if (unquotedMatch) {
      const fileName = unquotedMatch[1].trim();
      // 尝试解码 URL 编码的文件名
      try {
        return decodeURIComponent(fileName);
      } catch {
        // 如果解码失败，返回原始文件名
        return fileName;
      }
    }

    return null;
  }

  // 测试每个案例
  testCases.forEach(({ name, contentDisposition, expectedFileName }) => {
    console.log(`测试案例: ${name}`);
    console.log(`输入: ${contentDisposition}`);
    
    const parsedFileName = parseFileNameFromContentDisposition(contentDisposition);
    console.log(`解析结果: ${parsedFileName}`);
    console.log(`期望结果: ${expectedFileName}`);
    console.log(`✅ ${parsedFileName === expectedFileName ? '通过' : '失败'}\n`);
  });
}

/**
 * 实际下载示例
 */
export async function downloadWithUrlEncodedFilename(downloadUrl: string) {
  try {
    console.log('开始下载文件...');
    
    // 使用新的下载功能
    const result = await requestClient.downloadWithFileName(downloadUrl);
    
    console.log(`文件下载成功:`);
    console.log(`- 文件名: ${result.fileName}`);
    console.log(`- 文件大小: ${result.blob.size} bytes`);
    console.log(`- 文件类型: ${result.blob.type}`);
    
    // 触发浏览器下载
    const downloadUrl_blob = URL.createObjectURL(result.blob);
    const link = document.createElement('a');
    link.href = downloadUrl_blob;
    link.download = result.fileName;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    // 清理临时 URL
    setTimeout(() => URL.revokeObjectURL(downloadUrl_blob), 100);
    
    return result;
  } catch (error) {
    console.error('下载失败:', error);
    throw error;
  }
}

/**
 * 批量下载示例
 */
export async function batchDownloadWithUrlEncodedFilenames(urls: string[]) {
  const results = [];
  
  for (let i = 0; i < urls.length; i++) {
    const url = urls[i];
    console.log(`正在下载第 ${i + 1}/${urls.length} 个文件...`);
    
    try {
      const result = await requestClient.downloadWithFileName(url);
      
      console.log(`✅ 下载成功: ${result.fileName}`);
      results.push({
        url,
        fileName: result.fileName,
        success: true,
        blob: result.blob
      });
      
      // 触发下载
      await requestClient.downloadAndSave(url);
      
    } catch (error) {
      console.error(`❌ 下载失败: ${url}`, error);
      results.push({
        url,
        fileName: null,
        success: false,
        error: error.message
      });
    }
    
    // 添加延迟避免并发过多
    if (i < urls.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  // 输出统计信息
  const successCount = results.filter(r => r.success).length;
  console.log(`\n批量下载完成: ${successCount}/${urls.length} 个文件下载成功`);
  
  return results;
}

/**
 * 云盘下载改进示例
 */
export async function improvedCloudDiskDownload(fileIds: string[]) {
  try {
    // 假设这是获取下载链接的 API 调用
    const response = await fetch('/api/cloud-disk/pack-download', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ ids: fileIds }),
    });
    
    const data = await response.json();
    
    console.log('获取到下载链接:', data.url);
    console.log('服务器建议的文件名:', data.name);
    
    // 使用新的下载功能，优先从响应头解析文件名
    const result = await requestClient.downloadWithFileName(data.url, {
      fallbackFileName: data.name, // 使用 API 返回的名称作为备用
    });
    
    console.log('实际下载的文件名:', result.fileName);
    
    // 如果文件名是从 Content-Disposition 解析的，说明服务器提供了更准确的文件名
    if (result.fileName !== data.name) {
      console.log('✨ 从响应头解析到了更准确的文件名!');
    }
    
    // 触发下载
    const downloadUrl = URL.createObjectURL(result.blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = result.fileName;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    setTimeout(() => URL.revokeObjectURL(downloadUrl), 100);
    
    return result;
  } catch (error) {
    console.error('云盘下载失败:', error);
    throw error;
  }
}

/**
 * 运行演示
 */
export function runDemo() {
  console.log('🚀 开始运行 URL 编码文件名处理演示...\n');
  
  // 运行解析演示
  demoUrlEncodedFilename();
  
  console.log('演示完成! 现在您可以使用以下方法下载文件:');
  console.log('1. downloadWithUrlEncodedFilename(url) - 单个文件下载');
  console.log('2. batchDownloadWithUrlEncodedFilenames(urls) - 批量下载');
  console.log('3. improvedCloudDiskDownload(fileIds) - 改进的云盘下载');
}

// 如果直接运行此文件，执行演示
if (typeof window !== 'undefined') {
  // 在浏览器环境中可以直接调用
  // runDemo();
}
