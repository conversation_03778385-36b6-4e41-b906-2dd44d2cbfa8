import { describe, expect, it } from 'vitest';

/**
 * 从 Content-Disposition 响应头中解析文件名
 * 这是一个独立的测试函数，用于验证解析逻辑
 */
function parseFileNameFromContentDisposition(contentDisposition: string): string | null {
  if (!contentDisposition) {
    return null;
  }

  // 匹配 filename*=UTF-8''encoded-filename 格式 (RFC 5987)
  const rfc5987Match = contentDisposition.match(/filename\*=UTF-8''([^;]+)/i);
  if (rfc5987Match) {
    try {
      return decodeURIComponent(rfc5987Match[1]);
    } catch {
      // 如果解码失败，继续尝试其他格式
    }
  }

  // 匹配 filename="quoted-filename" 格式
  const quotedMatch = contentDisposition.match(/filename="([^"]+)"/i);
  if (quotedMatch) {
    const fileName = quotedMatch[1];
    // 尝试解码 URL 编码的文件名
    try {
      return decodeURIComponent(fileName);
    } catch {
      // 如果解码失败，返回原始文件名
      return fileName;
    }
  }

  // 匹配 filename=unquoted-filename 格式（包括 URL 编码的情况）
  const unquotedMatch = contentDisposition.match(/filename=([^;]+)/i);
  if (unquotedMatch) {
    const fileName = unquotedMatch[1].trim();
    // 尝试解码 URL 编码的文件名
    try {
      return decodeURIComponent(fileName);
    } catch {
      // 如果解码失败，返回原始文件名
      return fileName;
    }
  }

  return null;
}

describe('Content-Disposition 文件名解析', () => {
  describe('URL 编码支持', () => {
    it('应该解析 URL 编码的中文文件名（无引号格式）', () => {
      const contentDisposition = 'attachment;filename=%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('商品导入模板.xlsx');
    });

    it('应该解析 URL 编码的中文文件名（引号格式）', () => {
      const contentDisposition = 'attachment; filename="%E6%96%87%E4%BB%B6%E5%90%8D.pdf"';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('文件名.pdf');
    });

    it('应该解析 RFC 5987 格式的编码文件名', () => {
      const contentDisposition = "attachment; filename*=UTF-8''%E6%96%87%E4%BB%B6%E5%90%8D.pdf";
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('文件名.pdf');
    });

    it('应该解析复杂的中文文件名', () => {
      const contentDisposition = 'attachment;filename=%E7%94%A8%E6%88%B7%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%87%BA_2024%E5%B9%B407%E6%9C%8815%E6%97%A5.xlsx';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('用户数据导出_2024年07月15日.xlsx');
    });

    it('应该解析包含特殊字符的文件名', () => {
      const contentDisposition = 'attachment;filename=%E6%8A%A5%E8%A1%A8%28%E6%9C%80%E7%BB%88%E7%89%88%29.docx';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('报表(最终版).docx');
    });

    it('应该处理混合编码的文件名', () => {
      const contentDisposition = 'attachment;filename=report_%E6%8A%A5%E8%A1%A8_2024.pdf';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('report_报表_2024.pdf');
    });
  });

  describe('传统格式支持', () => {
    it('应该解析普通的英文文件名', () => {
      const contentDisposition = 'attachment; filename="report.pdf"';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('report.pdf');
    });

    it('应该解析无引号的文件名', () => {
      const contentDisposition = 'attachment; filename=document.xlsx';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('document.xlsx');
    });

    it('应该处理带空格的文件名', () => {
      const contentDisposition = 'attachment; filename="my document.pdf"';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('my document.pdf');
    });
  });

  describe('错误处理', () => {
    it('应该处理空的 Content-Disposition', () => {
      const result = parseFileNameFromContentDisposition('');
      expect(result).toBeNull();
    });

    it('应该处理 null 输入', () => {
      const result = parseFileNameFromContentDisposition(null as any);
      expect(result).toBeNull();
    });

    it('应该处理格式错误的 Content-Disposition', () => {
      const contentDisposition = 'attachment; invalid-format';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBeNull();
    });

    it('应该处理损坏的 URL 编码', () => {
      const contentDisposition = 'attachment; filename="malformed%E5%95%86%E5%93%81%"';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      // 如果解码失败，应该返回原始文件名
      expect(result).toBe('malformed%E5%95%86%E5%93%81%');
    });

    it('应该处理不完整的 RFC 5987 格式', () => {
      const contentDisposition = "attachment; filename*=UTF-8''%E6%96%87%E4%BB%B6%E5%90%8D%";
      const result = parseFileNameFromContentDisposition(contentDisposition);
      // 如果解码失败，应该返回 null 并尝试其他格式
      expect(result).toBeNull();
    });
  });

  describe('边界情况', () => {
    it('应该处理多个参数的 Content-Disposition', () => {
      const contentDisposition = 'attachment; filename="%E6%96%87%E4%BB%B6.pdf"; size=1024';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('文件.pdf');
    });

    it('应该处理大小写不敏感的匹配', () => {
      const contentDisposition = 'ATTACHMENT; FILENAME="%E6%96%87%E4%BB%B6.PDF"';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('文件.PDF');
    });

    it('应该处理带分号的文件名', () => {
      const contentDisposition = 'attachment; filename="file;name.txt"';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('file;name.txt');
    });

    it('应该优先使用 RFC 5987 格式', () => {
      const contentDisposition = 'attachment; filename="fallback.txt"; filename*=UTF-8\'\'%E6%96%87%E4%BB%B6.pdf';
      const result = parseFileNameFromContentDisposition(contentDisposition);
      expect(result).toBe('文件.pdf');
    });
  });

  describe('实际场景测试', () => {
    it('应该处理常见的服务器响应格式', () => {
      const testCases = [
        {
          input: 'attachment;filename=%E5%95%86%E5%93%81%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx',
          expected: '商品导入模板.xlsx'
        },
        {
          input: 'attachment; filename="%E7%94%A8%E6%88%B7%E5%88%97%E8%A1%A8.csv"',
          expected: '用户列表.csv'
        },
        {
          input: 'inline; filename*=UTF-8\'\'%E5%9B%BE%E7%89%87.jpg',
          expected: '图片.jpg'
        },
        {
          input: 'attachment; filename="report_2024.pdf"',
          expected: 'report_2024.pdf'
        }
      ];

      testCases.forEach(({ input, expected }) => {
        const result = parseFileNameFromContentDisposition(input);
        expect(result).toBe(expected);
      });
    });
  });
});
