/* ant-design-vue 组件库的一些样式重置 */

.ant-app {
  width: 100%;
  height: 100%;
  overscroll-behavior: none;
  color: inherit;
}

.ant-btn {
  .anticon {
    display: inline-flex;
  }

  /* * 修复按钮添加图标时的位置问题 */
  > svg {
    display: inline-block;
  }

  > svg + span {
    margin-inline-start: 6px;
  }
}

.ant-tag {
  > svg {
    display: inline-block;
  }

  > svg + span {
    margin-inline-start: 4px;
  }
}

.ant-message-notice-content,
.ant-notification-notice {
  @apply dark:border-border/60 dark:border;
}

.form-valid-error {
  /** select 选择器的样式 */

  .ant-select:not(.valid-success) .ant-select-selector:not(.valid-success) {
    border-color: hsl(var(--destructive)) !important;
  }

  .ant-select-focused .ant-select-selector {
    box-shadow: 0 0 0 2px rgb(255 38 5 / 6%) !important;
  }

  /** 数字输入框样式 */
  .ant-input-number-focused {
    box-shadow: 0 0 0 2px rgb(255 38 5 / 6%);
  }

  /** 密码输入框样式 */
  .ant-input-affix-wrapper:hover {
    border-color: hsl(var(--destructive));
    box-shadow: 0 0 0 2px rgb(255 38 5 / 6%);
  }

  .ant-input:not(.valid-success) {
    border-color: hsl(var(--destructive)) !important;
  }
}

/** 区间选择器下面来回切换时的样式 */
.ant-app .form-valid-error .ant-picker-active-bar {
  background-color: hsl(var(--destructive));
}

/** 时间选择器的样式 */
.ant-app .form-valid-error .ant-picker-focused {
  box-shadow: 0 0 0 2px rgb(255 38 5 / 6%);
}
