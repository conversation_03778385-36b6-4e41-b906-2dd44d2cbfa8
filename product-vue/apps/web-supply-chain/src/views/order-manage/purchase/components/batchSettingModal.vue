<!-- ... existing code ... -->
<template>
  <Modal v-model:visible="visible" title="批量设置" @ok="handleOk" @cancel="handleCancel">
    <Tabs v-model:active-key="activeTab">
      <Tabs-pane key="1" tab="调整含税单价">
        <div style="padding: 20px;">
          <p>含税单价调整方式：</p>
          <Radio-group v-model:value="taxPriceAdjustMethod" style="display: flex; flex-direction: column;">
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
              <Radio value="fixed">设为固定值</Radio>
              <a-input-number v-model:value="adjustForm.fixedAmount" placeholder="请输入金额" style="margin-left: 20px; width: 200px;" />
            </div>
            <div style="display: flex; align-items: center; margin-bottom: 15px;">
              <Radio value="percentage">基价上下浮动比率(%)</Radio>
              <a-input-number v-model:value="adjustForm.percentage" placeholder="请输入比率" style="margin-left: 20px; width: 200px;" />
            </div>
            <div style="display: flex; align-items: center;">
              <Radio value="amount">基价上下浮动值</Radio>
              <a-input-number v-model:value="adjustForm.amount" placeholder="请输入金额" style="margin-left: 20px; width: 200px;" />
            </div>
          </Radio-group>
        </div>
      </Tabs-pane>
      <!-- ... existing code ... -->
    </Tabs>
    <template #footer>
      <div style="text-align: right;">
        <Button type="primary" @click="handleOk">确定</Button>
        <Button @click="handleCancel" style="margin-left: 10px;">关闭</Button>
      </div>
    </template>
  </Modal>
</template>
<!-- ... existing code ... -->