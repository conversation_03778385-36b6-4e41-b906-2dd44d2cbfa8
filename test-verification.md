# VbenInput addonAfter 功能验证

## 修改内容

### 1. 修改了 VbenInput 组件
文件：`packages/@core/ui-kit/shadcn-ui/src/ui/input/Input.vue`

**添加的功能：**
- 新增 `addonAfter` 属性支持
- 支持函数和字符串两种类型
- 当有 `addonAfter` 时，使用 flex 布局将输入框和后缀组合

**类型定义：**
```typescript
addonAfter?: (() => any) | string;
```

**模板结构：**
```vue
<template>
  <div v-if="hasAddonAfter" class="flex w-full">
    <input class="... rounded-r-none border-r-0" />
    <div class="border-input bg-background flex items-center rounded-r-md border border-l-0 px-3">
      <component :is="props.addonAfter" v-if="typeof props.addonAfter === 'function'" />
      <span v-else-if="typeof props.addonAfter === 'string'">{{ props.addonAfter }}</span>
    </div>
  </div>
  <input v-else class="..." />
</template>
```

### 2. 忘记密码页面使用示例
文件：`apps/web-base-data/src/views/_core/authentication/forget-password.vue`

**验证码输入框配置：**
```typescript
{
  component: 'VbenInput',
  componentProps: {
    placeholder: '请输入验证码',
    addonAfter: () =>
      h(
        VbenButton,
        {
          disabled: sendCodeLoading.value || countdown.value > 0,
          loading: sendCodeLoading.value,
          size: 'sm',
          variant: 'default',
          onClick: handleSendCode,
        },
        () => (countdown.value > 0 ? `${countdown.value}s` : '发送验证码'),
      ),
  },
  fieldName: 'captcha',
  label: '验证码',
  rules: z.string().min(1, { message: '请输入验证码' }),
}
```

## 功能特性

### 1. 支持的 addonAfter 类型
- **函数类型**：`() => any` - 返回 Vue 组件
- **字符串类型**：`string` - 显示纯文本

### 2. 样式特性
- 输入框和后缀无缝连接
- 输入框右边框圆角移除，后缀左边框移除
- 后缀区域有合适的内边距
- 保持与原输入框相同的高度和边框样式

### 3. 响应式支持
- 支持响应式数据绑定
- 按钮状态可以根据外部状态动态变化
- 支持事件处理

## 使用场景

### 1. 验证码发送按钮
```typescript
addonAfter: () => h(VbenButton, { onClick: sendCode }, '发送验证码')
```

### 2. 倒计时按钮
```typescript
addonAfter: () => h(VbenButton, { 
  disabled: countdown > 0 
}, countdown > 0 ? `${countdown}s` : '发送验证码')
```

### 3. 简单文本后缀
```typescript
addonAfter: '元'
```

### 4. 图标按钮
```typescript
addonAfter: () => h(VbenButton, { 
  size: 'sm',
  variant: 'ghost'
}, () => h(SearchIcon))
```

## 兼容性
- 向后兼容：没有 `addonAfter` 时行为与原来完全一致
- 类型安全：TypeScript 类型检查支持
- 样式一致：与现有设计系统保持一致

## 测试建议
1. 测试基本的字符串后缀
2. 测试函数返回的组件后缀
3. 测试响应式数据变化
4. 测试事件处理
5. 测试样式在不同主题下的表现
