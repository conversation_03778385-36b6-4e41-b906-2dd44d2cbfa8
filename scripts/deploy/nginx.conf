
#user  nobody;
worker_processes 1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
  worker_connections 1024;
}


http {
  include mime.types;
  default_type application/octet-stream;

  types {
    application/javascript  js mjs;
    text/css                css;
    text/html               html;
  }

  sendfile on;
  # tcp_nopush     on;

  #keepalive_timeout  0;
  # keepalive_timeout 65;

  # gzip on;
  # gzip_buffers 32 16k;
  # gzip_comp_level 6;
  # gzip_min_length 1k;
  # gzip_static on;
  # gzip_types text/plain
  #   text/css
  #   application/javascript
  #   application/json
  #   application/x-javascript
  #   text/xml
  #   application/xml
  #   application/xml+rss
  #   text/javascript; #设置压缩的文件类型
  # gzip_vary on;

  server {
    listen 8080;
    server_name localhost;

    location / {
      root /usr/share/nginx/html;
      try_files $uri $uri/ /index.html;
      index index.html;
      # Enable CORS
      add_header 'Access-Control-Allow-Origin' '*';
      add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
      add_header 'Access-Control-Allow-Headers' 'DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type';
      if ($request_method = 'OPTIONS') {
        add_header 'Access-Control-Max-Age' 1728000;
        add_header 'Content-Type' 'text/plain charset=UTF-8';
        add_header 'Content-Length' 0;
        return 204;
      }
    }

    error_page 500 502 503 504 /50x.html;

    location = /50x.html {
        root /usr/share/nginx/html;
    }
  }
}
