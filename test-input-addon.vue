<template>
  <div class="p-4 space-y-4">
    <h2>VbenInput addonAfter 测试</h2>
    
    <!-- 测试字符串 addonAfter -->
    <div>
      <label>字符串 addonAfter:</label>
      <VbenInput v-model="value1" placeholder="输入内容" addon-after="发送" />
    </div>
    
    <!-- 测试函数 addonAfter -->
    <div>
      <label>函数 addonAfter (按钮):</label>
      <VbenInput 
        v-model="value2" 
        placeholder="输入验证码" 
        :addon-after="() => h(VbenButton, { 
          size: 'sm', 
          variant: 'default',
          onClick: handleClick 
        }, () => '发送验证码')" 
      />
    </div>
    
    <!-- 测试倒计时按钮 -->
    <div>
      <label>倒计时按钮:</label>
      <VbenInput 
        v-model="value3" 
        placeholder="输入验证码" 
        :addon-after="() => h(VbenButton, { 
          size: 'sm', 
          variant: 'default',
          disabled: countdown > 0,
          onClick: startCountdown 
        }, () => countdown > 0 ? `${countdown}s` : '发送验证码')" 
      />
    </div>
    
    <!-- 普通输入框 -->
    <div>
      <label>普通输入框:</label>
      <VbenInput v-model="value4" placeholder="普通输入框" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { h, ref } from 'vue';
import { VbenButton, Input as VbenInput } from '@vben-core/shadcn-ui';

const value1 = ref('');
const value2 = ref('');
const value3 = ref('');
const value4 = ref('');
const countdown = ref(0);

function handleClick() {
  alert('按钮被点击了！');
}

function startCountdown() {
  countdown.value = 60;
  const timer = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(timer);
    }
  }, 1000);
}
</script>
