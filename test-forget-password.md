# 忘记密码页面验证码发送功能实现

## 功能概述
在忘记密码页面的第2步（验证身份）中，为验证码输入框添加了"发送验证码"按钮，实现了以下功能：

## 主要功能
1. **发送验证码按钮**：在验证码输入框右侧添加按钮
2. **倒计时功能**：发送后60秒倒计时，防止频繁发送
3. **加载状态**：发送时显示loading状态
4. **自动禁用**：倒计时期间按钮自动禁用

## 实现细节

### 1. 状态管理
```typescript
// 发送验证码相关状态
const sendCodeLoading = ref(false);
const countdown = ref(0);
const countdownTimer = ref<NodeJS.Timeout | null>(null);
```

### 2. 验证码输入框配置
```typescript
{
  component: 'VbenInput',
  componentProps: {
    placeholder: '请输入验证码',
    addonAfter: () =>
      h(
        VbenButton,
        {
          disabled: sendCodeLoading.value || countdown.value > 0,
          loading: sendCodeLoading.value,
          size: 'sm',
          variant: 'default',
          onClick: handleSendCode,
        },
        () => (countdown.value > 0 ? `${countdown.value}s` : '发送验证码'),
      ),
  },
  fieldName: 'captcha',
  label: '验证码',
  rules: z.string().min(1, { message: '请输入验证码' }),
}
```

### 3. 发送验证码函数
```typescript
async function handleSendCode() {
  if (!confirmAccount.value || !userName.value || !findType.value) {
    return;
  }

  try {
    sendCodeLoading.value = true;
    
    // 根据找回方式选择对应的API
    const sendCodeApi = findType.value === 'mobile' ? sendSmsCodeApi : sendEmailCodeApi;
    const params = {
      userName: userName.value,
      [findType.value === 'mobile' ? 'mobile' : 'email']: confirmAccount.value,
    };
    
    await sendCodeApi(params);
    
    // 开始倒计时
    startCountdown();
  } catch (error) {
    console.error('发送验证码失败:', error);
  } finally {
    sendCodeLoading.value = false;
  }
}
```

### 4. 倒计时功能
```typescript
// 开始倒计时
function startCountdown() {
  countdown.value = 60;
  countdownTimer.value = setInterval(() => {
    countdown.value--;
    if (countdown.value <= 0) {
      clearInterval(countdownTimer.value!);
      countdownTimer.value = null;
    }
  }, 1000);
}

// 清理定时器
function clearCountdownTimer() {
  if (countdownTimer.value) {
    clearInterval(countdownTimer.value);
    countdownTimer.value = null;
    countdown.value = 0;
  }
}
```

### 5. 生命周期管理
```typescript
// 组件卸载时清理定时器
onBeforeUnmount(() => {
  clearCountdownTimer();
});
```

## 用户体验
1. **初始状态**：显示"发送验证码"按钮
2. **点击发送**：按钮显示loading状态，发送验证码
3. **发送成功**：开始60秒倒计时，按钮显示"60s"、"59s"...
4. **倒计时结束**：按钮恢复为"发送验证码"，可再次点击
5. **页面离开**：自动清理定时器，避免内存泄漏

## 技术要点
1. 使用 `addonAfter` 属性在输入框后添加按钮
2. 使用 `h()` 函数创建 VbenButton 组件
3. 响应式状态控制按钮的禁用和文本显示
4. 定时器管理和清理
5. 错误处理和用户反馈
