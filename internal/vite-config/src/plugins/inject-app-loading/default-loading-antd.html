<style data-app-loading="inject-css">
  html {
    /* same as ant-design-vue/dist/reset.css setting, avoid the title line-height changed */
    line-height: 1.15;
  }

  .dark .loading {
    background-color: #0d0d10;
  }

  .dark .loading .title {
    color: rgb(255 255 255 / 85%);
  }

  .loading {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    background-color: #f4f7f9;
  }

  .loading.hidden {
    visibility: hidden;
    opacity: 0;
    transition: all 0.6s ease-out;
  }

  .loading .title {
    margin-top: 36px;
    font-size: 30px;
    font-weight: 600;
    color: rgb(0 0 0 / 85%);
  }

  .dot {
    position: relative;
    box-sizing: border-box;
    display: inline-block;
    width: 48px;
    height: 48px;
    margin-top: 30px;
    font-size: 32px;
    transform: rotate(45deg);
    animation: rotate-ani 1.2s infinite linear;
  }

  .dot i {
    position: absolute;
    display: block;
    width: 20px;
    height: 20px;
    background-color: hsl(var(--primary, 210 100% 50%));
    border-radius: 100%;
    opacity: 0.3;
    transform: scale(0.75);
    transform-origin: 50% 50%;
    animation: spin-move-ani 1s infinite linear alternate;
  }

  .dot i:nth-child(1) {
    top: 0;
    left: 0;
  }

  .dot i:nth-child(2) {
    top: 0;
    right: 0;
    animation-delay: 0.4s;
  }

  .dot i:nth-child(3) {
    right: 0;
    bottom: 0;
    animation-delay: 0.8s;
  }

  .dot i:nth-child(4) {
    bottom: 0;
    left: 0;
    animation-delay: 1.2s;
  }

  @keyframes rotate-ani {
    to {
      transform: rotate(405deg);
    }
  }

  @keyframes spin-move-ani {
    to {
      opacity: 1;
    }
  }
</style>
<div class="loading" id="__app-loading__">
  <span class="dot"><i></i><i></i><i></i><i></i></span>
  <div class="title"><%= VITE_APP_TITLE %></div>
</div>
